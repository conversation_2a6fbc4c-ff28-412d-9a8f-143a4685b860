# 管理员安全增强功能迁移指南

## 概述

本文档描述了如何将现有的DStatus系统升级到新的管理员安全增强功能，包括JWT会话管理、双因子认证、动态登录入口等功能。

## 迁移步骤

### 1. 安装新依赖

```bash
npm install bcrypt@^5.1.1 jsonwebtoken@^9.0.2 speakeasy@^2.0.0
```

### 2. 运行数据库迁移

```bash
# 运行所有迁移（包括新的安全增强表）
npm run migrate

# 或者测试迁移是否成功
npm run test:admin-migration
```

### 3. 初始化管理员安全功能

```bash
# 初始化默认管理员用户和安全配置
npm run init:admin-security
```

## 迁移内容

### 数据库表

迁移将创建以下新表：

1. **admin_users** - 管理员用户信息
   - 存储用户名、密码哈希、GitHub信息
   - TOTP密钥和备份码（加密存储）
   - 登录时间和2FA提醒状态

2. **audit_logs** - 审计日志
   - 记录所有安全相关事件
   - 包括登录、2FA操作、配置变更等

3. **login_attempts** - 登录尝试记录
   - 用于限速和安全分析
   - 记录IP、用户名、尝试类型和结果

4. **rate_limits** - 限速状态
   - 管理各种操作的限速状态
   - 支持IP和用户维度的限制

5. **refresh_tokens** - 刷新令牌
   - 存储JWT刷新令牌的哈希
   - 支持令牌撤销和过期管理

### 配置项

初始化脚本将设置以下配置：

- **JWT配置**: 自动生成256位密钥
- **登录入口**: 默认设置为 `/admin-login`
- **安全策略**: 限速、锁定等安全参数
- **通知设置**: 登录事件通知配置

### 默认管理员

- 从现有的 `password` 配置创建默认管理员用户
- 用户名: `admin`
- 密码: 继承现有配置中的密码
- 使用bcrypt进行安全哈希存储

## 验收检查

### 数据库验收

运行测试脚本验证迁移：

```bash
npm run test:admin-migration
```

检查项：
- [ ] 所有新表创建成功
- [ ] 索引创建正确
- [ ] 数据类型适配正确

### 功能验收

1. **JWT系统**
   - [ ] 密钥生成成功（256位随机）
   - [ ] 配置正确保存

2. **默认管理员**
   - [ ] 用户创建成功
   - [ ] 密码哈希正确
   - [ ] 可以正常登录（需要后续JWT认证实现）

3. **配置系统**
   - [ ] 所有安全配置项设置成功
   - [ ] 登录入口配置正确

## 注意事项

### 安全考虑

1. **密码安全**: 新系统使用bcrypt替代MD5，安全性大幅提升
2. **JWT密钥**: 自动生成的密钥应妥善保管，不要泄露
3. **默认密码**: 如果检测到默认密码，建议首次登录后立即修改

### 兼容性

1. **Guest用户**: 完全不受影响，现有功能保持不变
2. **API接口**: 在JWT认证实现后，API行为保持兼容
3. **WebSocket**: 在JWT认证实现后，数据推送逻辑保持不变

### 回滚

如果需要回滚：

1. 备份当前数据库
2. 恢复到迁移前的代码版本
3. 删除新创建的表（可选）

```sql
-- 回滚SQL（谨慎使用）
DROP TABLE IF EXISTS refresh_tokens;
DROP TABLE IF EXISTS rate_limits;
DROP TABLE IF EXISTS login_attempts;
DROP TABLE IF EXISTS audit_logs;
DROP TABLE IF EXISTS admin_users;
```

## 下一步

完成迁移后，可以继续实施：

1. JWT管理器组件
2. 2FA验证器组件
3. 认证中间件和路由
4. 管理界面

详见项目的任务列表文档。