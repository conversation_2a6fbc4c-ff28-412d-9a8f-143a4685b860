"use strict";

const { randomUUID } = require('crypto');

module.exports = (DB) => {
  const TABLE = 'email_logs';

  const emailLogs = {
    async create({ to = [], cc = [], bcc = [], subject = '', body_text = '', body_html = '', template = 'custom', payload = {}, type = 'custom' }) {
      const id = randomUUID();
      const sql = `INSERT INTO ${TABLE} (id, [to], cc, bcc, subject, body_text, body_html, template, payload, type, status, created_at)
                   VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,'pending', strftime('%s','now'))`;
      await DB.run(sql, [id, JSON.stringify(to), JSON.stringify(cc), JSON.stringify(bcc), subject, body_text, body_html, template, JSON.stringify(payload), type]);
      return id;
    },
    async mark(id, { status, provider_message_id = null, error = null }) {
      const sql = `UPDATE ${TABLE} SET status=$1, provider_message_id=$2, error=$3, sent_at=strftime('%s','now') WHERE id=$4`;
      await DB.run(sql, [status, provider_message_id, error, id]);
    },
    async list(page = 1, pageSize = 20) {
      const offset = (page - 1) * pageSize;
      const totalRow = await DB.get(`SELECT COUNT(*) AS total FROM ${TABLE}`);
      const rows = await DB.all(`SELECT * FROM ${TABLE} ORDER BY created_at DESC LIMIT $1 OFFSET $2`, [pageSize, offset]);
      return { total: totalRow?.total || 0, page, pageSize, rows };
    },
    async cleanup(days = 30) {
      const sec = days * 86400;
      await DB.run(`DELETE FROM ${TABLE} WHERE created_at < (strftime('%s','now') - $1)`, [sec]);
    }
  };

  return { emailLogs };
};

