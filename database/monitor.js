'use strict';

const dataRetentionCache = {
    data: null,
    timestamp: 0,
    ttl: 3600000,
    get() {
        const now = Date.now();
        if (this.data && (now - this.timestamp) < this.ttl) {
            return this.data;
        }
        return null;
    },
    set(data) {
        this.data = data;
        this.timestamp = Date.now();
    },
    clear() {
        this.data = null;
        this.timestamp = 0;
        console.log('[Monitor] 数据保留配置缓存已清除');
    }
};

// 将缓存对象暴露到global，供其他模块访问
global.dataRetentionCache = dataRetentionCache;

function pad(arr, len) {
    for (var i = arr.length; i < len; ++i)
        arr.unshift({ success_rate: 0, avg_time: 0, min_time: 0, max_time: 0 });
    return arr;
}

module.exports = (DB) => {
    // 通用TCPing表操作函数
    function createTcpingTable(tableName) {
        return {
            async shift(targetId, data, timestamp = null) {
                try {
                    const ts = timestamp || Math.floor(Date.now() / 1000);
                    const expireTime = ts + 86400; // 24小时后过期
                    
                    await DB.run(`
                        INSERT INTO ${tableName} 
                        (target_id, sid, success_rate, avg_time, min_time, max_time, created_at, expire_time) 
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    `, [
                        targetId,
                        data.sid || 'unknown',
                        data.success_rate || 0,
                        data.avg_time || 0,
                        data.min_time || 0,
                        data.max_time || 0,
                        ts,
                        expireTime
                    ]);
                    return true;
                } catch (err) {
                    console.error(`保存数据到${tableName}表失败:`, err);
                    return false;
                }
            },

            async getMonitoringNodes(targetId, duration = 3600) {
                try {
                    const cutoffTime = Math.floor(Date.now() / 1000) - duration;
                    const result = await DB.all(`
                        SELECT DISTINCT sid 
                        FROM ${tableName} 
                        WHERE target_id = $1 AND created_at >= $2 AND sid IS NOT NULL AND sid != ''
                        ORDER BY sid
                    `, [targetId, cutoffTime]);
                    
                    return result.map(row => row.sid).filter(sid => sid && sid !== 'unknown');
                } catch (err) {
                    console.error(`获取${tableName}监控节点失败:`, err);
                    return [];
                }
            },

            async selectByTimeRange(targetId, duration = 3600, limit = 0) {
                try {
                    const cutoffTime = Math.floor(Date.now() / 1000) - duration;
                    let query = `
                        SELECT * FROM ${tableName} 
                        WHERE target_id = $1 AND created_at >= $2 
                        ORDER BY created_at DESC
                    `;
                    const params = [targetId, cutoffTime];
                    
                    if (limit > 0) {
                        query += ` LIMIT $3`;
                        params.push(limit);
                    }
                    
                    return await DB.all(query, params);
                } catch (err) {
                    console.error(`查询${tableName}时间范围数据失败:`, err);
                    return [];
                }
            },

            async selectByTimeRangeAndNode(targetId, nodeId, duration = 3600) {
                try {
                    const cutoffTime = Math.floor(Date.now() / 1000) - duration;
                    return await DB.all(`
                        SELECT * FROM ${tableName} 
                        WHERE target_id = $1 AND sid = $2 AND created_at >= $3 
                        ORDER BY created_at DESC
                    `, [targetId, nodeId, cutoffTime]);
                } catch (err) {
                    console.error(`查询${tableName}指定节点数据失败:`, err);
                    return [];
                }
            },

            async selectByNode(targetId, nodeId, limit = 24) {
                try {
                    return await DB.all(`
                        SELECT * FROM ${tableName} 
                        WHERE target_id = $1 AND sid = $2 
                        ORDER BY created_at DESC 
                        LIMIT $3
                    `, [targetId, nodeId, limit]);
                } catch (err) {
                    console.error(`查询${tableName}节点历史数据失败:`, err);
                    return [];
                }
            },

            async select(targetId, limit = 100) {
                try {
                    return await DB.all(`
                        SELECT * FROM ${tableName} 
                        WHERE target_id = $1 
                        ORDER BY created_at DESC 
                        LIMIT $2
                    `, [targetId, limit]);
                } catch (err) {
                    console.error(`查询${tableName}数据失败:`, err);
                    return [];
                }
            },

            async cleanup(options = {}) {
                try {
                    const now = Math.floor(Date.now() / 1000);
                    const beforeTime = options.before_time || (now - 86400); // 默认清理1天前的数据
                    
                    const result = await DB.run(`
                        DELETE FROM ${tableName} 
                        WHERE created_at < $1
                    `, [beforeTime]);
                    
                    return result.changes || 0;
                } catch (err) {
                    console.error(`清理${tableName}数据失败:`, err);
                    return 0;
                }
            }
        };
    }

    // 归档表特殊处理
    function createArchiveTable() {
        return {
            async insert(targetId, data) {
                try {
                    const now = new Date();
                    const timestamp = Math.floor(now.getTime() / 1000);
                    
                    await DB.run(`
                        INSERT INTO tcping_archive 
                        (target_id, sid, success_rate, avg_time, min_time, max_time, created_at, year, month, day, hour, minute) 
                        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    `, [
                        targetId,
                        data.sid || 'unknown',
                        data.success_rate || 0,
                        data.avg_time || 0,
                        data.min_time || 0,
                        data.max_time || 0,
                        timestamp,
                        now.getFullYear(),
                        now.getMonth() + 1,
                        now.getDate(),
                        now.getHours(),
                        now.getMinutes()
                    ]);
                    return true;
                } catch (err) {
                    console.error('保存数据到归档表失败:', err);
                    return false;
                }
            },

            async cleanup(options = {}) {
                try {
                    const beforeTime = options.before_time || (Math.floor(Date.now() / 1000) - 3600); // 默认清理1小时前
                    
                    const result = await DB.run(`
                        DELETE FROM tcping_archive 
                        WHERE created_at < $1
                    `, [beforeTime]);
                    
                    return result.changes || 0;
                } catch (err) {
                    console.error('清理归档数据失败:', err);
                    return 0;
                }
            },

            async select(targetId, limit = 1000) {
                try {
                    return await DB.all(`
                        SELECT * FROM tcping_archive 
                        WHERE target_id = $1 
                        ORDER BY created_at DESC 
                        LIMIT $2
                    `, [targetId, limit]);
                } catch (err) {
                    console.error('查询归档数据失败:', err);
                    return [];
                }
            },

            async query(options = {}) {
                try {
                    const { target_id, sid, limit = 1000, time_range } = options;
                    let whereClause = [];
                    let params = [];
                    let paramIndex = 1;

                    if (target_id) {
                        whereClause.push(`target_id = $${paramIndex++}`);
                        params.push(target_id);
                    }

                    if (sid) {
                        whereClause.push(`sid = $${paramIndex++}`);
                        params.push(sid);
                    }

                    if (time_range) {
                        const now = Math.floor(Date.now() / 1000);
                        let seconds = 3600; // 默认1小时
                        
                        if (time_range.includes('h')) {
                            seconds = parseInt(time_range) * 3600;
                        } else if (time_range.includes('d')) {
                            seconds = parseInt(time_range) * 24 * 3600;
                        }
                        
                        const cutoffTime = now - seconds;
                        whereClause.push(`created_at >= $${paramIndex++}`);
                        params.push(cutoffTime);
                    }

                    const whereStr = whereClause.length > 0 ? `WHERE ${whereClause.join(' AND ')}` : '';
                    
                    return await DB.all(`
                        SELECT * FROM tcping_archive 
                        ${whereStr}
                        ORDER BY created_at DESC 
                        LIMIT $${paramIndex}
                    `, [...params, limit]);
                } catch (err) {
                    console.error('查询归档数据失败:', err);
                    return [];
                }
            },

            async getStats(options = {}) {
                try {
                    const { target_id, sid, time_range } = options;
                    let whereClause = [];
                    let params = [];
                    let paramIndex = 1;

                    if (target_id) {
                        whereClause.push(`target_id = $${paramIndex++}`);
                        params.push(target_id);
                    }

                    if (sid) {
                        whereClause.push(`sid = $${paramIndex++}`);
                        params.push(sid);
                    }

                    if (time_range) {
                        const now = Math.floor(Date.now() / 1000);
                        let seconds = 3600; // 默认1小时
                        
                        if (time_range.includes('h')) {
                            seconds = parseInt(time_range) * 3600;
                        } else if (time_range.includes('d')) {
                            seconds = parseInt(time_range) * 24 * 3600;
                        }
                        
                        const cutoffTime = now - seconds;
                        whereClause.push(`created_at >= $${paramIndex++}`);
                        params.push(cutoffTime);
                    }

                    const whereStr = whereClause.length > 0 ? `WHERE ${whereClause.join(' AND ')}` : '';
                    
                    const result = await DB.get(`
                        SELECT COUNT(*) as count FROM tcping_archive 
                        ${whereStr}
                    `, params);
                    
                    return { count: result?.count || 0 };
                } catch (err) {
                    console.error('获取归档数据统计失败:', err);
                    return { count: 0 };
                }
            }
        };
    }

    const regions = {
        async add(id, name, description = '') {
            try {
                await DB.run('INSERT INTO monitor_regions (id, name, description) VALUES ($1, $2, $3)', [id, name, description]);
                return true;
            } catch (err) {
                console.error('添加监控地区失败:', err);
                return false;
            }
        },
        async update(id, name, description) {
            try {
                await DB.run('UPDATE monitor_regions SET name = $1, description = $2 WHERE id = $3', [name, description, id]);
                return true;
            } catch (err) {
                console.error('更新监控地区失败:', err);
                return false;
            }
        },
        async delete(id) {
            try {
                const targets = await DB.get('SELECT COUNT(*) as count FROM monitor_targets WHERE region_id = $1', [id]);
                if (targets && targets.count > 0) {
                    return { success: false, message: '该地区还有监控目标，无法删除' };
                }
                await DB.run('DELETE FROM monitor_regions WHERE id = $1', [id]);
                return { success: true };
            } catch (err) {
                console.error('删除监控地区失败:', err);
                return { success: false, message: err.message };
            }
        },
        async getAll() {
            return await DB.all('SELECT * FROM monitor_regions ORDER BY name');
        },
        async get(id) {
            return await DB.get('SELECT * FROM monitor_regions WHERE id = $1', [id]);
        }
    };

    const targets = {
        async add(id, region_id, name, host, port, description = '', mode = 'auto', node_id = null, test_type = 'tcping') {
            try {
                if (test_type === 'ping' && (port === null || port === undefined)) {
                    port = 0;
                }
                let node_id_value = Array.isArray(node_id) ? JSON.stringify(node_id) : node_id;
                await DB.run('INSERT INTO monitor_targets (id, region_id, name, host, port, description, mode, node_id, test_type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)', [id, region_id, name, host, port, description, mode, node_id_value, test_type]);
                return true;
            } catch (err) {
                console.error('添加监控目标失败:', err);
                return false;
            }
        },
        async update(id, region_id, name, host, port, description, mode = 'auto', node_id = null, test_type = 'tcping') {
            try {
                if (test_type === 'ping' && (port === null || port === undefined)) {
                    port = 0;
                }
                let node_id_value = Array.isArray(node_id) ? JSON.stringify(node_id) : node_id;
                await DB.run('UPDATE monitor_targets SET region_id = $1, name = $2, host = $3, port = $4, description = $5, mode = $6, node_id = $7, test_type = $8 WHERE id = $9', [region_id, name, host, port, description, mode, node_id_value, test_type, id]);
                return true;
            } catch (err) {
                console.error('更新监控目标失败:', err);
                return false;
            }
        },
        async delete(id) {
            const client = await DB.beginTransaction();
            try {
                if (DB.type === 'sqlite') {
                    await client.query('PRAGMA foreign_keys = OFF');
                }
                for (const table of ['tcping_m', 'tcping_5m', 'tcping_h', 'tcping_d', 'tcping_month', 'tcping_archive']) {
                    await client.query(`DELETE FROM ${table} WHERE target_id = $1`, [id]);
                }
                await client.query('DELETE FROM monitor_targets WHERE id = $1', [id]);
                if (DB.type === 'sqlite') {
                    await client.query('PRAGMA foreign_keys = ON');
                }
                await DB.commitTransaction(client);
                return true;
            } catch (err) {
                await DB.rollbackTransaction(client);
                if (DB.type === 'sqlite') {
                    try {
                        await DB.run('PRAGMA foreign_keys = ON');
                    } catch (pragmaErr) {
                        console.error('重新启用外键约束失败:', pragmaErr);
                    }
                }
                console.error('删除监控目标失败:', err);
                return false;
            }
        },
        async getAll() {
            return await DB.all('SELECT t.*, r.name as region_name FROM monitor_targets t JOIN monitor_regions r ON t.region_id = r.id ORDER BY t.region_id, t.name');
        },
        async get(id) {
            const sql = 'SELECT t.*, r.name as region_name FROM monitor_targets t JOIN monitor_regions r ON t.region_id = r.id WHERE t.id = $1';
            
            try {
                const result = await DB.get(sql, [id]);
                return result;
            } catch (error) {
                console.error('[Monitor] 查询失败:', error);
                throw error;
            }
        },
        async getByRegion(region_id) {
            return await DB.all('SELECT * FROM monitor_targets WHERE region_id = $1 ORDER BY name', [region_id]);
        }
    };

    // 创建所有TCPing表的实例
    const tcping_m = createTcpingTable('tcping_m');
    const tcping_5m = createTcpingTable('tcping_5m');
    const tcping_h = createTcpingTable('tcping_h');
    const tcping_d = createTcpingTable('tcping_d');
    const tcping_month = createTcpingTable('tcping_month');
    const tcping_archive = createArchiveTable();

    return {
        regions,
        targets,
        tcping_m,
        tcping_5m,
        tcping_h,
        tcping_d,
        tcping_month,
        tcping_archive
    };
};
