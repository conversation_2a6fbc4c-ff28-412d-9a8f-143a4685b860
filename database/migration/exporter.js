"use strict";

/**
 * 数据导出模块
 * 负责从源数据库（SQLite）导出数据
 */

const migrationConfig = require('./config');

class DataExporter {
    constructor(sourceAdapter) {
        this.sourceAdapter = sourceAdapter;
    }

    /**
     * 导出单个表的数据
     * @param {string} tableName - 表名
     * @param {Array} columns - 列名数组
     * @returns {AsyncGenerator} 数据流
     */
    async* exportTable(tableName, columns = ['*']) {
        const columnList = columns.join(', ');
        const sql = `SELECT ${columnList} FROM ${tableName}`;
        
        console.log(`[导出] 开始导出表 ${tableName}`);
        
        if (this.sourceAdapter.type === 'sqlite') {
            // 使用 better-sqlite3 的 iterate 方法进行流式导出
            let count = 0;
            
            for (const row of this.sourceAdapter.iterate(sql)) {
                count++;
                if (count % migrationConfig.logging.progressInterval === 0) {
                    console.log(`[导出] 已导出 ${tableName} 表 ${count} 条记录`);
                }
                yield row;
            }
            
            console.log(`[导出] 表 ${tableName} 导出完成，共 ${count} 条记录`);
        } else {
            // 对于其他数据库，使用分页查询
            const pageSize = migrationConfig.migration.batchSize;
            let offset = 0;
            let hasMore = true;
            let totalCount = 0;
            
            while (hasMore) {
                const rows = await this.sourceAdapter.all(
                    `${sql} LIMIT ${pageSize} OFFSET ${offset}`
                );
                
                if (rows.length === 0) {
                    hasMore = false;
                } else {
                    for (const row of rows) {
                        totalCount++;
                        yield row;
                    }
                    offset += pageSize;
                    
                    if (totalCount % (migrationConfig.logging.progressInterval * 5) === 0) {
                        console.log(`[导出] 已导出 ${tableName} 表 ${totalCount} 条记录`);
                    }
                }
            }
            
            console.log(`[导出] 表 ${tableName} 导出完成，共 ${totalCount} 条记录`);
        }
    }

    /**
     * 导出 groups 表
     * @returns {AsyncGenerator} 数据流
     */
    async* exportGroups() {
        yield* this.exportTable('groups', ['id', 'name', 'top']);
    }

    /**
     * 导出 servers 表
     * @returns {AsyncGenerator} 数据流
     */
    async* exportServers() {
        yield* this.exportTable('servers', [
            'sid', 'name', 'data', 'top', 'status', 'expire_time', 'group_id',
            'traffic_limit', 'traffic_reset_day', 'traffic_alert_percent', 
            'traffic_last_reset', 'traffic_direction', 'traffic_calibration_date', 
            'traffic_calibration_value'
        ]);
    }

    /**
     * 导出 traffic 表
     * @returns {AsyncGenerator} 数据流
     */
    async* exportTraffic() {
        yield* this.exportTable('traffic', ['sid', 'hs', 'ds', 'ms']);
    }

    /**
     * 导出 lt 表
     * @returns {AsyncGenerator} 数据流
     */
    async* exportLt() {
        yield* this.exportTable('lt', ['sid', 'traffic']);
    }

    /**
     * 导出 traffic_calibration 表
     */
    async* exportTrafficCalibration() {
        yield* this.exportTable('traffic_calibration', ['sid', 'calibration_date', 'calibration_value']);
    }

    /**
     * 导出 autodiscovery_servers 表
     */
    async* exportAutodiscoveryServers() {
        yield* this.exportTable('autodiscovery_servers', ['id', 'hostname', 'ip', 'system', 'version', 'device', 'api_key', 'status', 'created_at', 'updated_at']);
    }

    /**
     * 导出 ai_reports 表
     */
    async* exportAiReports() {
        yield* this.exportTable('ai_reports', ['id', 'report_id', 'title', 'report_data', 'metadata', 'created_at', 'time_range_start', 'time_range_end', 'servers_analyzed', 'overall_score', 'status']);
    }

    /**
     * 导出 load_archive 表
     */
    async* exportLoadArchive() {
        yield* this.exportTable('load_archive', ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at']);
    }

    /**
     * 导出 load_h 表
     */
    async* exportLoadH() {
        yield* this.exportTable('load_h', ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at']);
    }

    /**
     * 导出 load_m 表
     */
    async* exportLoadM() {
        yield* this.exportTable('load_m', ['id', 'sid', 'cpu', 'mem', 'swap', 'ibw', 'obw', 'expire_time', 'created_at']);
    }

    /**
     * 导出 tcping_5m 表
     */
    async* exportTcping5m() {
        yield* this.exportTable('tcping_5m', ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'sid']);
    }

    /**
     * 导出 tcping_archive 表
     */
    async* exportTcpingArchive() {
        yield* this.exportTable('tcping_archive', ['id', 'target_id', 'sid', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'year', 'month', 'day', 'hour', 'minute']);
    }

    /**
     * 导出 tcping_d 表
     */
    async* exportTcpingD() {
        yield* this.exportTable('tcping_d', ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid']);
    }

    /**
     * 导出 tcping_h 表
     */
    async* exportTcpingH() {
        yield* this.exportTable('tcping_h', ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid']);
    }

    /**
     * 导出 tcping_m 表
     */
    async* exportTcpingM() {
        yield* this.exportTable('tcping_m', ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid']);
    }

    /**
     * 导出 tcping_month 表
     */
    async* exportTcpingMonth() {
        yield* this.exportTable('tcping_month', ['id', 'target_id', 'success_rate', 'avg_time', 'min_time', 'max_time', 'created_at', 'expire_time', 'node_id', 'sid']);
    }

    /**
     * 导出 setting 表
     */
    async* exportSetting() {
        yield* this.exportTable('setting', ['key', 'val']);
    }

    /**
     * 导出 ssh_scripts 表
     */
    async* exportSshScripts() {
        yield* this.exportTable('ssh_scripts', ['id', 'name', 'content', 'category', 'description', 'variables', 'examples', 'tags', 'usage_count', 'created_at', 'updated_at']);
    }

    /**
     * 导出 monitor_regions 表
     */
    async* exportMonitorRegions() {
        yield* this.exportTable('monitor_regions', ['id', 'name', 'description', 'created_at']);
    }

    /**
     * 导出 monitor_targets 表
     */
    async* exportMonitorTargets() {
        yield* this.exportTable('monitor_targets', ['id', 'region_id', 'name', 'host', 'port', 'description', 'created_at', 'mode', 'node_id', 'test_type']);
    }

    /**
     * 导出 db_migrations 表
     */
    async* exportDbMigrations() {
        console.log(`[导出] 开始导出表 db_migrations`);

        if (this.sourceAdapter.type === 'sqlite') {
            // 兼容两种历史结构：applied_at(INTEGER, epoch秒) 与 executed_at(TIMESTAMP/ISO)
            // 动态探测可用列，统一输出 executed_at 字段供导入端使用
            let count = 0;
            try {
                const cols = await this.sourceAdapter.all(`PRAGMA table_info(db_migrations)`);
                const hasAppliedAt = cols.some(c => c.name === 'applied_at');
                const hasExecutedAt = cols.some(c => c.name === 'executed_at');

                // 构建最安全的选择列表（总是选择 version, name, status，再选择时间列）
                let sql;
                if (hasAppliedAt) {
                    sql = `SELECT version, name, status, applied_at FROM db_migrations`;
                } else if (hasExecutedAt) {
                    sql = `SELECT version, name, status, executed_at FROM db_migrations`;
                } else {
                    // 没有时间列也能导出（时间设为null）
                    sql = `SELECT version, name, status FROM db_migrations`;
                }

                // 使用流式导出以节省内存
                for (const row of this.sourceAdapter.iterate(sql)) {
                    count++;
                    // 统一生成 executed_at
                    if (hasAppliedAt && row.applied_at != null) {
                        row.executed_at = new Date(row.applied_at * 1000).toISOString();
                        delete row.applied_at;
                    } else if (hasExecutedAt && row.executed_at != null) {
                        // 尝试将数值/字符串归一化为ISO
                        const v = row.executed_at;
                        if (typeof v === 'number') {
                            // 可能是epoch秒
                            row.executed_at = new Date(v * 1000).toISOString();
                        } else if (typeof v === 'string') {
                            // 若已是ISO则保留，否则尝试Date解析
                            const t = new Date(v);
                            row.executed_at = isNaN(t.getTime()) ? null : t.toISOString();
                        }
                    } else {
                        row.executed_at = null;
                    }
                    yield row;
                }

                console.log(`[导出] 表 db_migrations 导出完成，共 ${count} 条记录`);
            } catch (error) {
                console.error('[导出] db_migrations 导出失败:', error.message);
                throw error;
            }
        }
    }

    /**
     * 获取表的记录数量
     * @param {string} tableName - 表名
     * @returns {number} 记录数量
     */
    async getTableCount(tableName) {
        try {
            const result = await this.sourceAdapter.get(`SELECT COUNT(*) as count FROM ${tableName}`);
            return result.count;
        } catch (error) {
            console.warn(`[警告] 无法获取表 ${tableName} 的记录数量:`, error.message);
            return 0;
        }
    }

    /**
     * 获取所有表的统计信息
     * @returns {Object} 统计信息对象
     */
    async getExportStatistics() {
        const tables = [
            'groups', 'servers', 'traffic', 'lt', 'traffic_calibration', 'autodiscovery_servers',
            'ai_reports', 'load_archive', 'load_h', 'load_m', 'tcping_5m', 'tcping_archive',
            'tcping_d', 'tcping_h', 'tcping_m', 'tcping_month', 'setting', 'ssh_scripts',
            'monitor_regions', 'monitor_targets', 'db_migrations'
        ];
        const stats = {};
        
        console.log('[导出] 收集统计信息...');
        
        for (const table of tables) {
            const count = await this.getTableCount(table);
            stats[table] = count;
            console.log(`[导出] 表 ${table}: ${count} 条记录`);
        }
        
        return stats;
    }
}

module.exports = DataExporter;
