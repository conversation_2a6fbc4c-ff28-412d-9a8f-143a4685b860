"use strict";

const JWTManager = require('./jwt-manager');
const RefreshTokenStore = require('./refresh-token-store');
const JWTMiddleware = require('./jwt-middleware');
const WebSocketAuth = require('./websocket-auth');
const TwoFactorAuth = require('./two-factor-auth');

/**
 * 认证模块主入口
 * 提供统一的认证服务接口
 */
class AuthModule {
    constructor(config, db) {
        this.config = config;
        this.db = db;
        this.jwtMiddleware = null;
        this.webSocketAuth = null;
        this.twoFactorAuth = null;
        this.initialized = false;
    }

    /**
     * 初始化认证模块
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            console.log('[AuthModule] 初始化认证模块...');

            // 初始化JWT中间件
            this.jwtMiddleware = new JWTMiddleware(this.config, this.db);
            await this.jwtMiddleware.initialize();

            // 初始化WebSocket认证
            this.webSocketAuth = new WebSocketAuth(this.jwtMiddleware);

            // 初始化2FA认证
            this.twoFactorAuth = new TwoFactorAuth(this.config, this.db);
            await this.twoFactorAuth.initialize();

            this.initialized = true;
            console.log('[AuthModule] 认证模块初始化完成');
        } catch (error) {
            console.error('[AuthModule] 认证模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取JWT中间件
     * @returns {JWTMiddleware} JWT中间件实例
     */
    getJWTMiddleware() {
        if (!this.initialized) {
            throw new Error('Auth module not initialized');
        }
        return this.jwtMiddleware;
    }

    /**
     * 获取WebSocket认证
     * @returns {WebSocketAuth} WebSocket认证实例
     */
    getWebSocketAuth() {
        if (!this.initialized) {
            throw new Error('Auth module not initialized');
        }
        return this.webSocketAuth;
    }

    /**
     * 获取2FA认证
     * @returns {TwoFactorAuth} 2FA认证实例
     */
    getTwoFactorAuth() {
        if (!this.initialized) {
            throw new Error('Auth module not initialized');
        }
        return this.twoFactorAuth;
    }

    /**
     * 获取Express中间件
     * @returns {Object} 中间件对象
     */
    getExpressMiddleware() {
        const jwtMiddleware = this.getJWTMiddleware();
        return {
            extractJWT: jwtMiddleware.extractJWT,
            setAdminFlag: jwtMiddleware.setAdminFlag,
            requireAdmin: jwtMiddleware.requireAdmin
        };
    }

    /**
     * 获取WebSocket中间件
     * @returns {Function} WebSocket中间件函数
     */
    getWebSocketMiddleware() {
        const webSocketAuth = this.getWebSocketAuth();
        return webSocketAuth.createMiddleware();
    }

    /**
     * 用户登录
     * @param {Object} user - 用户信息
     * @returns {Object} 登录结果
     */
    async login(user) {
        const jwtMiddleware = this.getJWTMiddleware();
        const tokenPair = await jwtMiddleware.generateTokenPair(user);
        
        // 更新用户最后登录时间
        await this.updateLastLogin(user.id);
        
        return {
            success: true,
            user: {
                id: user.id,
                username: user.username,
                githubId: user.github_id,
                githubUsername: user.github_username
            },
            tokens: tokenPair
        };
    }

    /**
     * 刷新令牌
     * @param {string} refreshToken - 刷新令牌
     * @returns {Object} 刷新结果
     */
    async refreshToken(refreshToken) {
        try {
            const jwtMiddleware = this.getJWTMiddleware();
            const tokenPair = await jwtMiddleware.refreshTokenPair(refreshToken);
            
            return {
                success: true,
                tokens: tokenPair
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 用户登出
     * @param {string} userId - 用户ID
     * @returns {Object} 登出结果
     */
    async logout(userId) {
        try {
            const jwtMiddleware = this.getJWTMiddleware();
            await jwtMiddleware.revokeUserTokens(userId);

            return {
                success: true,
                message: 'User tokens revoked successfully'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 验证2FA令牌
     * @param {string} userId - 用户ID
     * @param {string} token - 2FA令牌
     * @returns {Object} 验证结果
     */
    async verify2FA(userId, token) {
        const twoFactorAuth = this.getTwoFactorAuth();
        return await twoFactorAuth.verifyUserToken(userId, token);
    }

    /**
     * 启用用户2FA
     * @param {string} userId - 用户ID
     * @param {string} secret - TOTP密钥
     * @param {Array} backupCodes - 备份码
     * @returns {Object} 操作结果
     */
    async enable2FA(userId, secret, backupCodes) {
        try {
            const twoFactorAuth = this.getTwoFactorAuth();
            await twoFactorAuth.enableTwoFactor(userId, secret, backupCodes);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 禁用用户2FA
     * @param {string} userId - 用户ID
     * @returns {Object} 操作结果
     */
    async disable2FA(userId) {
        try {
            const twoFactorAuth = this.getTwoFactorAuth();
            await twoFactorAuth.disableTwoFactor(userId);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成2FA密钥
     * @param {string} username - 用户名
     * @returns {Object} 密钥信息
     */
    generate2FASecret(username) {
        const twoFactorAuth = this.getTwoFactorAuth();
        return twoFactorAuth.generateSecret(username);
    }

    /**
     * 重新生成备份码
     * @param {string} userId - 用户ID
     * @returns {Array} 新的备份码
     */
    async regenerateBackupCodes(userId) {
        const twoFactorAuth = this.getTwoFactorAuth();
        return await twoFactorAuth.regenerateBackupCodes(userId);
    }

    /**
     * 更新用户最后登录时间
     * @param {string} userId - 用户ID
     */
    async updateLastLogin(userId) {
        try {
            const now = Math.floor(Date.now() / 1000);
            await this.db.run(
                'UPDATE admin_users SET last_login_at = ?, updated_at = ? WHERE id = ?',
                [now, now, userId]
            );
        } catch (error) {
            console.error('[AuthModule] 更新最后登录时间失败:', error);
        }
    }

    /**
     * 获取用户信息
     * @param {string} userId - 用户ID
     * @returns {Object|null} 用户信息
     */
    async getUser(userId) {
        try {
            const user = await this.db.get(
                'SELECT id, username, github_id, github_username, totp_enabled, last_login_at FROM admin_users WHERE id = ?',
                [userId]
            );
            return user || null;
        } catch (error) {
            console.error('[AuthModule] 获取用户信息失败:', error);
            return null;
        }
    }

    /**
     * 获取认证统计信息
     * @returns {Object} 统计信息
     */
    async getStats() {
        try {
            const jwtMiddleware = this.getJWTMiddleware();
            const twoFactorAuth = this.getTwoFactorAuth();
            const refreshTokenStore = jwtMiddleware.getRefreshTokenStore();
            
            const [userCount, tokenStats, twoFactorStats] = await Promise.all([
                this.db.get('SELECT COUNT(*) as count FROM admin_users'),
                refreshTokenStore.getStats(),
                twoFactorAuth.getStats()
            ]);
            
            return {
                users: userCount ? userCount.count : 0,
                tokens: tokenStats,
                twoFactor: twoFactorStats
            };
        } catch (error) {
            console.error('[AuthModule] 获取统计信息失败:', error);
            return {
                users: 0,
                tokens: { total: 0, active: 0, expired: 0, revoked: 0 },
                twoFactor: { totalUsers: 0, enabledUsers: 0, backupCodes: { total: 0, unused: 0, used: 0 } }
            };
        }
    }

    /**
     * 执行维护任务
     */
    async performMaintenance() {
        if (!this.initialized) {
            return;
        }

        try {
            console.log('[AuthModule] 执行认证模块维护任务...');
            await this.jwtMiddleware.performMaintenance();
            console.log('[AuthModule] 维护任务完成');
        } catch (error) {
            console.error('[AuthModule] 维护任务失败:', error);
        }
    }

    /**
     * 健康检查
     * @returns {Object} 健康状态
     */
    async healthCheck() {
        try {
            const stats = await this.getStats();
            const jwtManager = this.jwtMiddleware.getJWTManager();
            
            return {
                status: 'healthy',
                initialized: this.initialized,
                jwtManagerReady: !!jwtManager,
                twoFactorReady: !!this.twoFactorAuth,
                stats: stats,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// 导出类和组件
module.exports = {
    AuthModule,
    JWTManager,
    RefreshTokenStore,
    JWTMiddleware,
    WebSocketAuth,
    TwoFactorAuth
};