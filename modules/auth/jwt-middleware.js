"use strict";

/**
 * JWT认证中间件
 * 提供JWT令牌提取、验证和管理员标志设置功能
 * 替代现有的admin_tokens检查机制
 */

const JWTManager = require('./jwt-manager');
const RefreshTokenStore = require('./refresh-token-store');

class JWTMiddleware {
    constructor(config, db) {
        this.config = config;
        this.db = db;
        this.jwtManager = null;
        this.refreshTokenStore = null;
        this.initialized = false;
    }

    /**
     * 初始化JWT中间件
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            console.log('[JWTMiddleware] 初始化JWT中间件...');

            // 获取JWT密钥
            let jwtSecret;
            try {
                jwtSecret = await this.config.get('security.jwtSecret');
            } catch (error) {
                throw new Error('JWT secret not configured. Please run: npm run init:admin-security');
            }

            if (!jwtSecret) {
                throw new Error('JWT secret is empty. Please run: npm run init:admin-security');
            }

            // 获取JWT配置
            const accessTokenExpiry = await this.config.get('security.jwtAccessTokenExpiry') || '15m';
            const refreshTokenExpiry = await this.config.get('security.jwtRefreshTokenExpiry') || '7d';

            // 创建JWT管理器
            this.jwtManager = new JWTManager(jwtSecret, {
                issuer: 'dstatus',
                algorithm: 'HS256',
                accessTokenExpiry,
                refreshTokenExpiry
            });

            // 创建刷新令牌存储
            this.refreshTokenStore = new RefreshTokenStore(this.db);

            this.initialized = true;
            console.log('[JWTMiddleware] JWT中间件初始化完成');
        } catch (error) {
            console.error('[JWTMiddleware] JWT中间件初始化失败:', error);
            throw error;
        }
    }

    /**
     * 提取JWT令牌中间件
     * 从Authorization header提取JWT令牌并验证
     */
    extractJWT = (req, res, next) => {
        try {
            // 初始化用户相关属性
            req.jwtPayload = null;
            req.user = null;
            req.jwtValid = false;

            // 从Authorization header提取JWT令牌
            const token = this.jwtManager.extractFromRequest(req);
            
            if (token) {
                // 验证令牌
                const verification = this.jwtManager.verify(token);
                
                if (verification.valid && verification.payload.type === 'access') {
                    req.jwtPayload = verification.payload;
                    req.user = {
                        id: verification.payload.userId,
                        username: verification.payload.username,
                        githubId: verification.payload.githubId,
                        githubUsername: verification.payload.githubUsername
                    };
                    req.jwtValid = true;
                } else {
                    // JWT验证失败，记录日志但不阻止请求
                    console.log('[JWTMiddleware] JWT验证失败:', verification.error || 'Invalid token type');
                }
            }
            
            next();
        } catch (error) {
            console.error('[JWTMiddleware] JWT提取过程发生错误:', error);
            // 发生错误时不阻止请求，继续执行
            next();
        }
    }

    /**
     * 设置管理员标志中间件
     * 基于JWT payload设置req.admin标志
     */
    setAdminFlag = (req, res, next) => {
        try {
            // 基于JWT验证结果设置admin标志
            if (req.jwtValid && req.jwtPayload) {
                req.admin = true;
            } else {
                req.admin = false;
            }
            
            next();
        } catch (error) {
            console.error('[JWTMiddleware] 设置管理员标志时发生错误:', error);
            req.admin = false;
            next();
        }
    }

    /**
     * 要求管理员权限的中间件
     * 检查req.admin标志，未授权时重定向或返回错误
     */
    requireAdmin = (req, res, next) => {
        if (!req.admin) {
            // 对于页面请求，重定向到登录页
            if (req.accepts('html') && !req.accepts('json')) {
                return res.redirect('/login');
            }
            // 对于API请求，返回JSON错误
            return res.status(401).json({ 
                code: 0, 
                msg: '需要管理员权限' 
            });
        }
        next();
    }

    /**
     * 生成JWT令牌对
     * @param {Object} user - 用户信息
     * @returns {Object} 令牌对
     */
    async generateTokenPair(user) {
        if (!this.initialized) {
            throw new Error('JWT middleware not initialized');
        }

        try {
            // 创建用户载荷
            const payload = {
                userId: user.id,
                username: user.username,
                githubId: user.github_id || null,
                githubUsername: user.github_username || null
            };

            // 生成令牌对
            const tokenPair = this.jwtManager.signTokenPair(payload);

            // 存储Refresh Token
            await this.refreshTokenStore.save(
                tokenPair.refreshTokenId,
                user.id,
                this.jwtManager.hashToken(tokenPair.refreshToken),
                this.jwtManager.calculateExpiryTimestamp(this.jwtManager.options.refreshTokenExpiry)
            );

            // 限制用户的活跃Token数量
            await this.refreshTokenStore.limitUserTokens(user.id, 5);

            return tokenPair;
        } catch (error) {
            console.error('[JWTMiddleware] 生成令牌对失败:', error);
            throw error;
        }
    }

    /**
     * 刷新JWT令牌
     * @param {string} refreshToken - 刷新令牌
     * @returns {Object} 新的令牌对
     */
    async refreshTokenPair(refreshToken) {
        if (!this.initialized) {
            throw new Error('JWT middleware not initialized');
        }

        try {
            return await this.jwtManager.refresh(refreshToken, this.refreshTokenStore);
        } catch (error) {
            console.error('[JWTMiddleware] 刷新令牌失败:', error);
            throw error;
        }
    }

    /**
     * 撤销用户的所有令牌
     * @param {string} userId - 用户ID
     */
    async revokeUserTokens(userId) {
        if (!this.initialized) {
            throw new Error('JWT middleware not initialized');
        }

        try {
            await this.refreshTokenStore.revokeAllForUser(userId);
        } catch (error) {
            console.error('[JWTMiddleware] 撤销用户令牌失败:', error);
            throw error;
        }
    }

    /**
     * 执行维护任务
     */
    async performMaintenance() {
        if (!this.initialized) {
            return;
        }

        try {
            const cleaned = await this.refreshTokenStore.cleanupExpired();
            if (cleaned > 0) {
                console.log(`[JWTMiddleware] 清理了 ${cleaned} 个过期的刷新令牌`);
            }
        } catch (error) {
            console.error('[JWTMiddleware] 维护任务失败:', error);
        }
    }

    /**
     * 获取JWT管理器实例
     * @returns {JWTManager} JWT管理器
     */
    getJWTManager() {
        return this.jwtManager;
    }

    /**
     * 获取刷新令牌存储实例
     * @returns {RefreshTokenStore} 刷新令牌存储
     */
    getRefreshTokenStore() {
        return this.refreshTokenStore;
    }
}

module.exports = JWTMiddleware;
