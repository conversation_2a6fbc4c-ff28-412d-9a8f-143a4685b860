"use strict";

const crypto = require('crypto');
const speakeasy = require('speakeasy');

class TwoFactorAuth {
    constructor(config, db) {
        this.config = config;
        this.db = db;
        this.encryptionKey = null;
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            this.encryptionKey = await this.config.get('security.totpEncryptionKey');
            if (!this.encryptionKey) {
                throw new Error('TOTP encryption key not configured');
            }

            this.initialized = true;
            console.log('[TwoFactorAuth] 2FA管理器初始化成功');
        } catch (error) {
            console.error('[TwoFactorAuth] 初始化失败:', error);
            throw error;
        }
    }

    generateSecret(username, issuer = 'DStatus') {
        const secret = speakeasy.generateSecret({
            name: `${issuer}:${username}`,
            issuer: issuer,
            length: 32
        });

        return {
            secret: secret.base32,
            qrCodeUrl: secret.otpauth_url,
            manualEntryKey: secret.base32
        };
    }

    encryptSecret(secret) {
        if (!this.encryptionKey) {
            throw new Error('Encryption key not initialized');
        }

        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(secret, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }

    decryptSecret(encryptedSecret) {
        if (!this.encryptionKey) {
            throw new Error('Encryption key not initialized');
        }

        try {
            const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
            let decrypted = decipher.update(encryptedSecret, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        } catch (error) {
            console.error('[TwoFactorAuth] 解密TOTP密钥失败:', error);
            throw new Error('Failed to decrypt TOTP secret');
        }
    }

    verifyToken(token, encryptedSecret, options = {}) {
        try {
            const secret = this.decryptSecret(encryptedSecret);
            
            const verified = speakeasy.totp.verify({
                secret: secret,
                encoding: 'base32',
                token: token,
                window: options.window || 2,
                step: 30
            });

            return verified;
        } catch (error) {
            console.error('[TwoFactorAuth] TOTP验证失败:', error);
            return false;
        }
    }

    generateBackupCodes(count = 10) {
        const codes = [];
        for (let i = 0; i < count; i++) {
            const code = crypto.randomBytes(4).toString('hex').toUpperCase();
            const formatted = `${code.slice(0, 4)}-${code.slice(4, 8)}`;
            codes.push(formatted);
        }
        return codes;
    }

    hashBackupCode(code) {
        return crypto.createHash('sha256').update(code.replace('-', '')).digest('hex');
    }

    verifyBackupCode(code, hashedCode) {
        const inputHash = this.hashBackupCode(code);
        return crypto.timingSafeEqual(
            Buffer.from(inputHash, 'hex'),
            Buffer.from(hashedCode, 'hex')
        );
    }

    async enableTwoFactor(userId, secret, backupCodes) {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            const encryptedSecret = this.encryptSecret(secret);
            const encryptedBackupCodes = backupCodes.map(code => this.encryptSecret(code));
            const backupCodesJson = JSON.stringify(encryptedBackupCodes);
            const now = Math.floor(Date.now() / 1000);

            await this.db.run(`
                UPDATE admin_users 
                SET totp_secret = ?, totp_enabled = 1, backup_codes = ?, updated_at = ?
                WHERE id = ?
            `, [encryptedSecret, backupCodesJson, now, userId]);

            console.log(`[TwoFactorAuth] 为用户 ${userId} 启用2FA`);
        } catch (error) {
            console.error('[TwoFactorAuth] 启用2FA失败:', error);
            throw new Error('Failed to enable two-factor authentication');
        }
    }

    async disableTwoFactor(userId) {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            const now = Math.floor(Date.now() / 1000);

            await this.db.run(`
                UPDATE admin_users 
                SET totp_secret = NULL, totp_enabled = 0, backup_codes = NULL, updated_at = ?
                WHERE id = ?
            `, [now, userId]);

            console.log(`[TwoFactorAuth] 为用户 ${userId} 禁用2FA`);
        } catch (error) {
            console.error('[TwoFactorAuth] 禁用2FA失败:', error);
            throw new Error('Failed to disable two-factor authentication');
        }
    }

    async getUserTwoFactorStatus(userId) {
        try {
            const user = await this.db.get(`
                SELECT totp_enabled, backup_codes FROM admin_users WHERE id = ?
            `, [userId]);

            if (!user) {
                return { enabled: false, backupCodesCount: 0 };
            }

            let backupCodesCount = 0;
            if (user.backup_codes) {
                try {
                    const encryptedCodes = JSON.parse(user.backup_codes);
                    backupCodesCount = encryptedCodes.length;
                } catch (error) {
                    console.error('[TwoFactorAuth] 解析备份码失败:', error);
                }
            }

            return {
                enabled: !!user.totp_enabled,
                backupCodesCount: backupCodesCount
            };
        } catch (error) {
            console.error('[TwoFactorAuth] 获取2FA状态失败:', error);
            return { enabled: false, backupCodesCount: 0 };
        }
    }

    async regenerateBackupCodes(userId) {
        if (!this.initialized) {
            await this.initialize();
        }

        try {
            const newCodes = this.generateBackupCodes(10);
            const encryptedBackupCodes = newCodes.map(code => this.encryptSecret(code));
            const backupCodesJson = JSON.stringify(encryptedBackupCodes);
            const now = Math.floor(Date.now() / 1000);

            await this.db.run(`
                UPDATE admin_users SET backup_codes = ?, updated_at = ?
                WHERE id = ?
            `, [backupCodesJson, now, userId]);

            console.log(`[TwoFactorAuth] 为用户 ${userId} 重新生成备份码`);
            return newCodes;
        } catch (error) {
            console.error('[TwoFactorAuth] 重新生成备份码失败:', error);
            throw new Error('Failed to regenerate backup codes');
        }
    }

    async getStats() {
        try {
            const [totalUsers, enabledUsers] = await Promise.all([
                this.db.get('SELECT COUNT(*) as count FROM admin_users'),
                this.db.get('SELECT COUNT(*) as count FROM admin_users WHERE totp_enabled = 1')
            ]);

            const usersWithBackupCodes = await this.db.all(`
                SELECT backup_codes FROM admin_users 
                WHERE totp_enabled = 1 AND backup_codes IS NOT NULL
            `);

            let totalBackupCodes = 0;
            for (const user of usersWithBackupCodes) {
                try {
                    const encryptedCodes = JSON.parse(user.backup_codes);
                    totalBackupCodes += encryptedCodes.length;
                } catch (error) {
                    // 忽略解析失败的备份码
                }
            }

            return {
                totalUsers: totalUsers ? totalUsers.count : 0,
                enabledUsers: enabledUsers ? enabledUsers.count : 0,
                backupCodes: {
                    total: totalBackupCodes,
                    unused: totalBackupCodes,
                    used: 0
                }
            };
        } catch (error) {
            console.error('[TwoFactorAuth] 获取统计信息失败:', error);
            return {
                totalUsers: 0,
                enabledUsers: 0,
                backupCodes: { total: 0, unused: 0, used: 0 }
            };
        }
    }

    async checkGitHubWhitelist(githubUsername) {
        try {
            const whitelist = await this.config.get('security.githubWhitelist');
            if (!Array.isArray(whitelist) || whitelist.length === 0) {
                return true;
            }
            return whitelist.includes(githubUsername.toLowerCase());
        } catch (error) {
            console.error('[TwoFactorAuth] 检查GitHub白名单失败:', error);
            return false;
        }
    }
}

module.exports = TwoFactorAuth;
