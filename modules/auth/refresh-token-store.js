"use strict";

/**
 * 刷新令牌存储管理器
 * 负责Refresh Token的存储、验证、撤销和清理
 */

const crypto = require('crypto');

class RefreshTokenStore {
    constructor(db) {
        if (!db) {
            throw new Error('Database instance is required');
        }
        this.db = db;
    }

    /**
     * 保存Refresh Token（存储SHA256哈希）
     * @param {string} tokenId - 令牌ID (jti)
     * @param {string} userId - 用户ID
     * @param {string} tokenHash - 令牌哈希
     * @param {number} expiresAt - 过期时间戳
     */
    async save(tokenId, userId, tokenHash, expiresAt) {
        const now = Math.floor(Date.now() / 1000);
        
        await this.db.DB.run(`
            INSERT INTO refresh_tokens (
                id, user_id, token_hash, expires_at, created_at, revoked
            ) VALUES (?, ?, ?, ?, ?, ?)
        `, [tokenId, userId, tokenHash, expiresAt, now, 0]);
    }

    /**
     * 检查Token是否有效（未撤销且未过期）
     * @param {string} tokenId - 令牌ID
     * @returns {boolean} 是否有效
     */
    async isValid(tokenId) {
        const now = Math.floor(Date.now() / 1000);
        
        const result = await this.db.DB.get(`
            SELECT id FROM refresh_tokens 
            WHERE id = ? AND revoked = 0 AND expires_at > ?
        `, [tokenId, now]);
        
        return Boolean(result);
    }

    /**
     * 撤销Token
     * @param {string} tokenId - 令牌ID
     */
    async revoke(tokenId) {
        await this.db.DB.run(`
            UPDATE refresh_tokens 
            SET revoked = 1 
            WHERE id = ?
        `, [tokenId]);
    }

    /**
     * 撤销用户所有Token
     * @param {string} userId - 用户ID
     */
    async revokeAllForUser(userId) {
        await this.db.DB.run(`
            UPDATE refresh_tokens 
            SET revoked = 1 
            WHERE user_id = ?
        `, [userId]);
    }

    /**
     * 清理过期Token
     * @returns {number} 清理的数量
     */
    async cleanupExpired() {
        const now = Math.floor(Date.now() / 1000);
        
        const result = await this.db.DB.run(`
            DELETE FROM refresh_tokens 
            WHERE expires_at <= ? OR revoked = 1
        `, [now]);
        
        return result.changes || 0;
    }

    /**
     * 获取用户的活跃Token数量
     * @param {string} userId - 用户ID
     * @returns {number} 活跃Token数量
     */
    async getActiveTokenCount(userId) {
        const now = Math.floor(Date.now() / 1000);
        
        const result = await this.db.DB.get(`
            SELECT COUNT(*) as count FROM refresh_tokens 
            WHERE user_id = ? AND revoked = 0 AND expires_at > ?
        `, [userId, now]);
        
        return result ? result.count : 0;
    }

    /**
     * 获取Token信息（用于审计）
     * @param {string} tokenId - 令牌ID
     * @returns {Object|null} Token信息
     */
    async getTokenInfo(tokenId) {
        const result = await this.db.DB.get(`
            SELECT id, user_id, expires_at, created_at, revoked 
            FROM refresh_tokens 
            WHERE id = ?
        `, [tokenId]);
        
        if (!result) {
            return null;
        }
        
        return {
            id: result.id,
            userId: result.user_id,
            expiresAt: result.expires_at,
            createdAt: result.created_at,
            revoked: Boolean(result.revoked),
            isExpired: result.expires_at <= Math.floor(Date.now() / 1000)
        };
    }

    /**
     * 限制用户的活跃Token数量
     * @param {string} userId - 用户ID
     * @param {number} maxTokens - 最大Token数量
     */
    async limitUserTokens(userId, maxTokens = 5) {
        const now = Math.floor(Date.now() / 1000);
        
        // 获取用户的活跃Token，按创建时间排序
        const tokens = await this.db.DB.all(`
            SELECT id FROM refresh_tokens 
            WHERE user_id = ? AND revoked = 0 AND expires_at > ?
            ORDER BY created_at DESC
        `, [userId, now]);
        
        // 如果超过限制，撤销最旧的Token
        if (tokens.length > maxTokens) {
            const tokensToRevoke = tokens.slice(maxTokens);
            for (const token of tokensToRevoke) {
                await this.revoke(token.id);
            }
        }
    }

    /**
     * 验证Token哈希
     * @param {string} token - 原始令牌
     * @param {string} storedHash - 存储的哈希
     * @returns {boolean} 是否匹配
     */
    verifyTokenHash(token, storedHash) {
        const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
        return tokenHash === storedHash;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    async getStats() {
        const now = Math.floor(Date.now() / 1000);
        
        const [total, active, expired, revoked] = await Promise.all([
            this.db.DB.get('SELECT COUNT(*) as count FROM refresh_tokens'),
            this.db.DB.get('SELECT COUNT(*) as count FROM refresh_tokens WHERE revoked = 0 AND expires_at > ?', [now]),
            this.db.DB.get('SELECT COUNT(*) as count FROM refresh_tokens WHERE expires_at <= ?', [now]),
            this.db.DB.get('SELECT COUNT(*) as count FROM refresh_tokens WHERE revoked = 1')
        ]);
        
        return {
            total: total ? total.count : 0,
            active: active ? active.count : 0,
            expired: expired ? expired.count : 0,
            revoked: revoked ? revoked.count : 0
        };
    }
}

module.exports = RefreshTokenStore;