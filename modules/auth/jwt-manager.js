"use strict";

/**
 * JWT管理器 - 负责JWT令牌的签发、验证和刷新
 * 支持Access+Refresh双令牌机制，提供安全的会话管理
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class JWTManager {
    constructor(secretKey, options = {}) {
        if (!secretKey) {
            throw new Error('JWT secret key is required');
        }
        
        this.secretKey = secretKey;
        this.options = {
            issuer: options.issuer || 'dstatus',
            algorithm: options.algorithm || 'HS256',
            accessTokenExpiry: options.accessTokenExpiry || '15m',
            refreshTokenExpiry: options.refreshTokenExpiry || '7d'
        };
    }

    /**
     * 签发JWT令牌
     * @param {Object} payload - 令牌载荷
     * @param {string} expiresIn - 过期时间
     * @returns {string} JWT令牌
     */
    sign(payload, expiresIn = null) {
        const jwtPayload = {
            ...payload,
            iat: Math.floor(Date.now() / 1000),
            iss: this.options.issuer
        };
        
        const signOptions = {
            algorithm: this.options.algorithm
        };
        
        if (expiresIn) {
            signOptions.expiresIn = expiresIn;
        }
        
        return jwt.sign(jwtPayload, this.secretKey, signOptions);
    }

    /**
     * 验证JWT令牌
     * @param {string} token - JWT令牌
     * @returns {Object} 验证结果
     */
    verify(token) {
        try {
            const payload = jwt.verify(token, this.secretKey, {
                algorithms: [this.options.algorithm],
                issuer: this.options.issuer
            });
            
            return {
                valid: true,
                payload: payload
            };
        } catch (error) {
            return {
                valid: false,
                error: error.name,
                message: error.message
            };
        }
    }

    /**
     * 从请求中提取JWT令牌
     * @param {Object} req - Express请求对象
     * @returns {string|null} JWT令牌
     */
    extractFromRequest(req) {
        // 仅从Authorization header获取
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return null;
    }

    /**
     * 签发Access Token和Refresh Token对
     * @param {Object} payload - 用户信息载荷
     * @returns {Object} 令牌对
     */
    signTokenPair(payload) {
        // 生成Access Token（短期）
        const accessToken = this.sign({
            ...payload,
            type: 'access'
        }, this.options.accessTokenExpiry);
        
        // 生成Refresh Token（长期）
        const refreshTokenId = crypto.randomUUID();
        const refreshToken = this.sign({
            ...payload,
            type: 'refresh',
            jti: refreshTokenId
        }, this.options.refreshTokenExpiry);
        
        return {
            accessToken,
            refreshToken,
            refreshTokenId,
            expiresIn: this.parseExpiryToSeconds(this.options.accessTokenExpiry)
        };
    }

    /**
     * 刷新令牌（需要独立的Refresh Token）
     * @param {string} refreshToken - 刷新令牌
     * @param {Object} refreshTokenStore - 刷新令牌存储
     * @returns {Object} 新的令牌对
     */
    async refresh(refreshToken, refreshTokenStore) {
        // 验证Refresh Token
        const verification = this.verify(refreshToken);
        if (!verification.valid || verification.payload.type !== 'refresh') {
            throw new Error('Invalid refresh token');
        }
        
        const { payload } = verification;
        
        // 检查Refresh Token是否在有效存储中
        const isValidRefreshToken = await refreshTokenStore.isValid(payload.jti);
        if (!isValidRefreshToken) {
            throw new Error('Refresh token revoked or expired');
        }
        
        // 撤销旧的Refresh Token
        await refreshTokenStore.revoke(payload.jti);
        
        // 清理JWT内部字段，准备签发新令牌
        const userPayload = { ...payload };
        delete userPayload.iat;
        delete userPayload.exp;
        delete userPayload.jti;
        delete userPayload.type;
        delete userPayload.iss;
        
        // 签发新的Token对
        const newTokenPair = this.signTokenPair(userPayload);
        
        // 存储新的Refresh Token
        await refreshTokenStore.save(
            newTokenPair.refreshTokenId,
            userPayload.userId,
            this.hashToken(newTokenPair.refreshToken),
            this.calculateExpiryTimestamp(this.options.refreshTokenExpiry)
        );
        
        return newTokenPair;
    }

    /**
     * 验证Access Token并提取用户信息
     * @param {string} accessToken - 访问令牌
     * @returns {Object} 用户信息
     */
    verifyAccessToken(accessToken) {
        const verification = this.verify(accessToken);
        if (!verification.valid || verification.payload.type !== 'access') {
            throw new Error('Invalid access token');
        }
        
        return verification.payload;
    }

    /**
     * 生成令牌哈希（用于安全存储）
     * @param {string} token - 令牌
     * @returns {string} SHA256哈希
     */
    hashToken(token) {
        return crypto.createHash('sha256').update(token).digest('hex');
    }

    /**
     * 解析过期时间字符串为秒数
     * @param {string} expiry - 过期时间字符串（如'15m', '7d'）
     * @returns {number} 秒数
     */
    parseExpiryToSeconds(expiry) {
        const match = expiry.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error(`Invalid expiry format: ${expiry}`);
        }
        
        const value = parseInt(match[1]);
        const unit = match[2];
        
        const multipliers = {
            's': 1,
            'm': 60,
            'h': 3600,
            'd': 86400
        };
        
        return value * multipliers[unit];
    }

    /**
     * 计算过期时间戳
     * @param {string} expiry - 过期时间字符串
     * @returns {number} Unix时间戳
     */
    calculateExpiryTimestamp(expiry) {
        const seconds = this.parseExpiryToSeconds(expiry);
        return Math.floor(Date.now() / 1000) + seconds;
    }

    /**
     * 创建用户载荷
     * @param {Object} user - 用户信息
     * @returns {Object} JWT载荷
     */
    createUserPayload(user) {
        return {
            userId: user.id,
            username: user.username,
            isAdmin: true, // 当前只有admin用户
            githubId: user.github_id || null,
            totpEnabled: Boolean(user.totp_enabled)
        };
    }
}

module.exports = JWTManager;