"use strict";

/**
 * 认证路由模块
 * 实现登录、登出、2FA验证、令牌刷新等API端点
 * 提供完整的认证API，支持本地密码登录和GitHub OAuth登录流程
 */

const express = require('express');
const crypto = require('crypto');
const bcrypt = require('bcrypt');

// 导入核心函数
const { pr } = require('../../core');

/**
 * 创建认证路由
 * @param {Object} authModule - 认证模块实例
 * @param {Object} config - 配置管理器
 * @param {Object} db - 数据库实例
 * @returns {express.Router} Express路由器
 */
function createAuthRoutes(authModule, config, db) {
    const router = express.Router();

    // 获取组件实例
    const jwtMiddleware = authModule.getJWTMiddleware();
    const twoFactorAuth = authModule.getTwoFactorAuth();

    /**
     * 登录页面路由
     */
    router.get('/login', (req, res) => {
        if (req.admin) {
            res.redirect('/');
        } else {
            res.render('login', {});
        }
    });

    /**
     * 登录API - 第一步：验证用户名密码
     * POST /login
     */
    router.post('/login', async (req, res) => {
        try {
            const { password, originalPassword } = req.body;

            if (!password) {
                return res.json(pr(0, "密码不能为空"));
            }

            // 获取当前密码
            const currentPassword = await db.setting.get("password");
            const passwordHash = crypto.createHash('md5').update(currentPassword).digest('hex');

            // 验证密码
            if (password !== passwordHash) {
                // 记录登录失败
                console.log(`[Auth] 登录失败 - IP:${req.ip} 密码错误`);
                return res.json(pr(0, "密码错误"));
            }

            // 检查是否为默认密码
            const isDefaultPassword = currentPassword === "dstatus" || originalPassword === "dstatus";

            // 检查用户是否启用了2FA
            const adminUser = await db.get('SELECT * FROM admin_users WHERE username = ? LIMIT 1', ['admin']);
            const adminUsers = adminUser ? [adminUser] : [];

            if (adminUsers.length > 0 && adminUsers[0].totp_secret) {
                // 用户启用了2FA，需要进行二次验证
                // 生成临时令牌用于2FA验证
                const tempToken = crypto.randomBytes(32).toString('hex');
                const tempTokenExpiry = Date.now() + 5 * 60 * 1000; // 5分钟有效期

                // 存储临时令牌
                await db.run(
                    'INSERT OR REPLACE INTO temp_auth_tokens (token, user_id, expires_at, created_at) VALUES (?, ?, ?, ?)',
                    [tempToken, adminUsers[0].id, tempTokenExpiry, Date.now()]
                );

                return res.json(pr(1, {
                    requireTwoFactor: true,
                    tempToken: tempToken,
                    forceChangePassword: isDefaultPassword
                }));
            }

            // 用户未启用2FA，直接生成JWT令牌
            const user = {
                id: adminUsers.length > 0 ? adminUsers[0].id : 'admin',
                username: 'admin',
                github_id: adminUsers.length > 0 ? adminUsers[0].github_id : null,
                github_username: adminUsers.length > 0 ? adminUsers[0].github_username : null
            };

            const tokenPair = await jwtMiddleware.generateTokenPair(user);

            // 记录登录成功
            console.log(`[Auth] 登录成功 - IP:${req.ip} 用户:${user.username}`);

            return res.json(pr(1, {
                accessToken: tokenPair.accessToken,
                refreshToken: tokenPair.refreshToken,
                expiresIn: tokenPair.expiresIn,
                user: {
                    username: user.username,
                    githubUsername: user.github_username
                },
                forceChangePassword: isDefaultPassword
            }));

        } catch (error) {
            console.error('[Auth] 登录过程发生错误:', error);
            return res.json(pr(0, "登录失败，请稍后重试"));
        }
    });

    /**
     * 2FA验证API - 第二步：验证TOTP或备份码
     * POST /verify-2fa
     */
    router.post('/verify-2fa', async (req, res) => {
        try {
            const { tempToken, code, useBackupCode = false } = req.body;

            if (!tempToken || !code) {
                return res.json(pr(0, "验证码和临时令牌不能为空"));
            }

            // 验证临时令牌
            const tempTokenRecord = await db.get(
                'SELECT * FROM temp_auth_tokens WHERE token = ? AND expires_at > ? LIMIT 1',
                [tempToken, Date.now()]
            );

            if (!tempTokenRecord) {
                return res.json(pr(0, "临时令牌无效或已过期"));
            }

            // 获取用户信息
            const adminUser = await db.get(
                'SELECT * FROM admin_users WHERE id = ? LIMIT 1',
                [tempTokenRecord.user_id]
            );

            if (!adminUser) {
                return res.json(pr(0, "用户不存在"));
            }

            const user = adminUser;
            let verificationResult = false;

            if (useBackupCode) {
                // 验证备份码
                const backupCodes = await db.all(
                    'SELECT * FROM backup_codes WHERE user_id = ? AND used_at IS NULL',
                    [user.id]
                );

                for (const backupCode of backupCodes) {
                    if (twoFactorAuth.verifyBackupCode(code, backupCode.code_hash)) {
                        // 标记备份码为已使用
                        await db.run(
                            'UPDATE backup_codes SET used_at = ? WHERE id = ?',
                            [Date.now(), backupCode.id]
                        );
                        verificationResult = true;
                        break;
                    }
                }
            } else {
                // 验证TOTP
                verificationResult = twoFactorAuth.verifyToken(code, user.totp_secret);
            }

            if (!verificationResult) {
                console.log(`[Auth] 2FA验证失败 - IP:${req.ip} 用户:${user.username}`);
                return res.json(pr(0, "验证码错误"));
            }

            // 2FA验证成功，生成JWT令牌
            const tokenPair = await jwtMiddleware.generateTokenPair({
                id: user.id,
                username: user.username,
                github_id: user.github_id,
                github_username: user.github_username
            });

            // 删除临时令牌
            await db.run('DELETE FROM temp_auth_tokens WHERE token = ?', [tempToken]);

            // 记录登录成功
            console.log(`[Auth] 2FA验证成功，登录完成 - IP:${req.ip} 用户:${user.username}`);

            return res.json(pr(1, {
                accessToken: tokenPair.accessToken,
                refreshToken: tokenPair.refreshToken,
                expiresIn: tokenPair.expiresIn,
                user: {
                    username: user.username,
                    githubUsername: user.github_username
                }
            }));

        } catch (error) {
            console.error('[Auth] 2FA验证过程发生错误:', error);
            return res.json(pr(0, "验证失败，请稍后重试"));
        }
    });

    /**
     * 令牌刷新API
     * POST /refresh
     */
    router.post('/refresh', async (req, res) => {
        try {
            const { refreshToken } = req.body;

            if (!refreshToken) {
                return res.json(pr(0, "刷新令牌不能为空"));
            }

            // 刷新令牌
            const newTokenPair = await jwtMiddleware.refreshTokenPair(refreshToken);

            return res.json(pr(1, {
                accessToken: newTokenPair.accessToken,
                refreshToken: newTokenPair.refreshToken,
                expiresIn: newTokenPair.expiresIn
            }));

        } catch (error) {
            console.error('[Auth] 令牌刷新失败:', error);
            return res.json(pr(0, "令牌刷新失败"));
        }
    });

    /**
     * 登出API
     * POST /logout
     */
    router.post('/logout', async (req, res) => {
        try {
            const { refreshToken } = req.body;
            const userId = req.user ? req.user.id : null;

            if (refreshToken) {
                // 撤销特定的刷新令牌
                try {
                    const refreshTokenStore = jwtMiddleware.getRefreshTokenStore();
                    const jwtManager = jwtMiddleware.getJWTManager();
                    const tokenHash = jwtManager.hashToken(refreshToken);
                    await refreshTokenStore.revoke(tokenHash);
                } catch (error) {
                    console.error('[Auth] 撤销刷新令牌失败:', error);
                }
            } else if (userId) {
                // 撤销用户的所有令牌
                await jwtMiddleware.revokeUserTokens(userId);
            }

            // 记录登出
            console.log(`[Auth] 用户登出 - IP:${req.ip} 用户:${req.user ? req.user.username : 'unknown'}`);

            return res.json(pr(1, "登出成功"));

        } catch (error) {
            console.error('[Auth] 登出过程发生错误:', error);
            return res.json(pr(0, "登出失败"));
        }
    });

    /**
     * 传统登出路由（兼容性）
     * GET /logout
     */
    router.get('/logout', (req, res) => {
        // 清除cookie（兼容旧版本）
        res.clearCookie("token");
        res.redirect("/login");
    });

    /**
     * 获取当前用户信息API
     * GET /me
     */
    router.get('/me', jwtMiddleware.extractJWT, jwtMiddleware.setAdminFlag, (req, res) => {
        if (!req.admin) {
            return res.json(pr(0, "未授权"));
        }

        return res.json(pr(1, {
            user: req.user,
            authenticated: true,
            admin: req.admin
        }));
    });

    /**
     * 检查认证状态API
     * GET /status
     */
    router.get('/status', jwtMiddleware.extractJWT, jwtMiddleware.setAdminFlag, (req, res) => {
        return res.json(pr(1, {
            authenticated: req.jwtValid,
            admin: req.admin,
            user: req.user
        }));
    });

    /**
     * GitHub OAuth登录初始化
     * GET /github/login
     */
    router.get('/github/login', async (req, res) => {
        try {
            // 检查GitHub OAuth是否配置
            let githubClientId;
            try {
                githubClientId = await config.get('auth.github.clientId');
            } catch (error) {
                // 配置不存在，返回未配置状态
                githubClientId = null;
            }
            if (!githubClientId) {
                return res.json(pr(0, "GitHub OAuth未配置"));
            }

            // 生成state参数防止CSRF攻击
            const state = crypto.randomBytes(32).toString('hex');

            // 存储state到临时表
            await db.run(
                'INSERT OR REPLACE INTO oauth_states (state, created_at, expires_at) VALUES (?, ?, ?)',
                [state, Date.now(), Date.now() + 10 * 60 * 1000] // 10分钟有效期
            );

            const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${githubClientId}&state=${state}&scope=user:email`;

            return res.json(pr(1, {
                authUrl: githubAuthUrl,
                state: state
            }));

        } catch (error) {
            console.error('[Auth] GitHub OAuth初始化失败:', error);
            return res.json(pr(0, "GitHub登录初始化失败"));
        }
    });

    /**
     * GitHub OAuth回调处理
     * POST /github/callback
     */
    router.post('/github/callback', async (req, res) => {
        try {
            const { code, state } = req.body;

            if (!code || !state) {
                return res.json(pr(0, "缺少必要参数"));
            }

            // 验证state参数
            const stateRecord = await db.get(
                'SELECT * FROM oauth_states WHERE state = ? AND expires_at > ? LIMIT 1',
                [state, Date.now()]
            );

            if (!stateRecord) {
                return res.json(pr(0, "无效的state参数"));
            }

            // 删除已使用的state
            await db.run('DELETE FROM oauth_states WHERE state = ?', [state]);

            // 获取GitHub配置
            let githubClientId, githubClientSecret;
            try {
                githubClientId = await config.get('auth.github.clientId');
                githubClientSecret = await config.get('auth.github.clientSecret');
            } catch (error) {
                return res.json(pr(0, "GitHub OAuth配置不完整"));
            }

            if (!githubClientId || !githubClientSecret) {
                return res.json(pr(0, "GitHub OAuth配置不完整"));
            }

            // 交换访问令牌
            const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    client_id: githubClientId,
                    client_secret: githubClientSecret,
                    code: code
                })
            });

            const tokenData = await tokenResponse.json();

            if (!tokenData.access_token) {
                return res.json(pr(0, "获取GitHub访问令牌失败"));
            }

            // 获取用户信息
            const userResponse = await fetch('https://api.github.com/user', {
                headers: {
                    'Authorization': `token ${tokenData.access_token}`,
                    'User-Agent': 'DStatus'
                }
            });

            const githubUser = await userResponse.json();

            if (!githubUser.id) {
                return res.json(pr(0, "获取GitHub用户信息失败"));
            }

            // 检查或创建管理员用户
            let adminUser = await db.get(
                'SELECT * FROM admin_users WHERE github_id = ? LIMIT 1',
                [githubUser.id]
            );

            if (!adminUser) {
                // 创建新的管理员用户
                await db.run(
                    'INSERT INTO admin_users (username, github_id, github_username, created_at) VALUES (?, ?, ?, ?)',
                    ['admin', githubUser.id, githubUser.login, Date.now()]
                );

                adminUser = await db.get(
                    'SELECT * FROM admin_users WHERE github_id = ? LIMIT 1',
                    [githubUser.id]
                );
            } else {
                // 更新GitHub用户名
                await db.run(
                    'UPDATE admin_users SET github_username = ? WHERE id = ?',
                    [githubUser.login, adminUser.id]
                );
                adminUser.github_username = githubUser.login;
            }

            const user = adminUser;

            // 生成JWT令牌
            const tokenPair = await jwtMiddleware.generateTokenPair({
                id: user.id,
                username: user.username,
                github_id: user.github_id,
                github_username: user.github_username
            });

            // 记录登录成功
            console.log(`[Auth] GitHub登录成功 - IP:${req.ip} GitHub用户:${githubUser.login}`);

            return res.json(pr(1, {
                accessToken: tokenPair.accessToken,
                refreshToken: tokenPair.refreshToken,
                expiresIn: tokenPair.expiresIn,
                user: {
                    username: user.username,
                    githubUsername: user.github_username
                }
            }));

        } catch (error) {
            console.error('[Auth] GitHub OAuth回调处理失败:', error);
            return res.json(pr(0, "GitHub登录失败"));
        }
    });

    /**
     * 清理过期的临时令牌和状态
     */
    async function cleanupExpiredTokens() {
        try {
            const now = Date.now();
            await db.run('DELETE FROM temp_auth_tokens WHERE expires_at < ?', [now]);
            await db.run('DELETE FROM oauth_states WHERE expires_at < ?', [now]);
        } catch (error) {
            console.error('[Auth] 清理过期令牌失败:', error);
        }
    }

    // 定期清理过期令牌（每小时执行一次）
    setInterval(cleanupExpiredTokens, 60 * 60 * 1000);

    return router;
}

module.exports = createAuthRoutes;
