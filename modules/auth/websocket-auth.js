"use strict";

/**
 * WebSocket认证组件
 * 实现WebSocket连接的JWT Bearer认证机制
 * 替代现有的cookie token检查
 */

class WebSocketAuth {
    constructor(jwtMiddleware) {
        this.jwtMiddleware = jwtMiddleware;
    }

    /**
     * 认证WebSocket连接
     * @param {Object} req - WebSocket请求对象
     * @returns {Object} 认证结果
     */
    authenticate(req) {
        try {
            // 检查JWT中间件是否已初始化
            if (!this.jwtMiddleware || !this.jwtMiddleware.initialized) {
                return {
                    isValid: false,
                    isAdmin: false,
                    user: null,
                    error: 'JWT middleware not initialized'
                };
            }

            // 从Authorization header提取JWT令牌
            const jwtManager = this.jwtMiddleware.getJWTManager();
            const token = jwtManager.extractFromRequest(req);

            if (!token) {
                // 没有提供JWT令牌，返回guest用户状态
                return {
                    isValid: true,
                    isAdmin: false,
                    user: null,
                    error: null
                };
            }

            // 验证JWT令牌
            const verification = jwtManager.verify(token);

            if (!verification.valid) {
                return {
                    isValid: false,
                    isAdmin: false,
                    user: null,
                    error: verification.error || 'Invalid JWT token'
                };
            }

            // 检查令牌类型
            if (verification.payload.type !== 'access') {
                return {
                    isValid: false,
                    isAdmin: false,
                    user: null,
                    error: 'Invalid token type'
                };
            }

            // 提取用户信息
            const user = {
                id: verification.payload.userId,
                username: verification.payload.username,
                githubId: verification.payload.githubId,
                githubUsername: verification.payload.githubUsername
            };

            return {
                isValid: true,
                isAdmin: true, // JWT验证通过即为管理员
                user: user,
                error: null
            };

        } catch (error) {
            console.error('[WebSocketAuth] 认证过程发生错误:', error);
            return {
                isValid: false,
                isAdmin: false,
                user: null,
                error: error.message
            };
        }
    }

    /**
     * 创建WebSocket中间件函数
     * @returns {Function} WebSocket中间件
     */
    createMiddleware() {
        return (ws, req) => {
            try {
                // 执行认证
                const authResult = this.authenticate(req);

                // 设置WebSocket对象的认证属性
                ws.isAdmin = authResult.isAdmin;
                ws.user = authResult.user;
                ws.authError = authResult.error;

                // 记录认证结果
                const clientIP = req.ip;
                console.log(`[WebSocketAuth] WebSocket认证完成 - 管理员:${authResult.isAdmin} IP:${clientIP} 用户:${authResult.user ? authResult.user.username : 'guest'}`);

                // 如果认证失败且有错误，记录详细信息
                if (!authResult.isValid && authResult.error) {
                    console.log(`[WebSocketAuth] WebSocket认证失败 - IP:${clientIP} 错误:${authResult.error}`);
                }

                return authResult;

            } catch (error) {
                console.error('[WebSocketAuth] WebSocket中间件执行错误:', error);
                
                // 设置默认值
                ws.isAdmin = false;
                ws.user = null;
                ws.authError = error.message;

                return {
                    isValid: false,
                    isAdmin: false,
                    user: null,
                    error: error.message
                };
            }
        };
    }

    /**
     * 验证WebSocket连接是否有管理员权限
     * @param {Object} ws - WebSocket连接对象
     * @returns {boolean} 是否有管理员权限
     */
    hasAdminPermission(ws) {
        return !!ws.isAdmin;
    }

    /**
     * 获取WebSocket连接的用户信息
     * @param {Object} ws - WebSocket连接对象
     * @returns {Object|null} 用户信息
     */
    getUser(ws) {
        return ws.user || null;
    }

    /**
     * 检查WebSocket连接是否认证有效
     * @param {Object} ws - WebSocket连接对象
     * @returns {boolean} 认证是否有效
     */
    isAuthenticated(ws) {
        return !ws.authError;
    }

    /**
     * 获取认证错误信息
     * @param {Object} ws - WebSocket连接对象
     * @returns {string|null} 错误信息
     */
    getAuthError(ws) {
        return ws.authError || null;
    }

    /**
     * 为现有WebSocket路由提供兼容性方法
     * 直接返回认证结果，用于替代admin_tokens.has()检查
     * @param {Object} req - WebSocket请求对象
     * @returns {boolean} 是否为管理员
     */
    isAdminRequest(req) {
        const authResult = this.authenticate(req);
        return authResult.isAdmin;
    }

    /**
     * 获取认证统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            middlewareReady: !!this.jwtMiddleware,
            jwtManagerReady: !!(this.jwtMiddleware && this.jwtMiddleware.getJWTManager()),
            initialized: !!(this.jwtMiddleware && this.jwtMiddleware.initialized)
        };
    }
}

module.exports = WebSocketAuth;
