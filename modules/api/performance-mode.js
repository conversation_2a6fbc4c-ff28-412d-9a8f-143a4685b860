"use strict";
const express = require("express");

/**
 * 性能模式设置API模块
 * 提供性能模式的配置接口和预设管理
 */
module.exports = (svr, db) => {
    const { pr } = svr.locals;
    const router = express.Router();

    // 性能模式配置映射
    const PERFORMANCE_MODES = {
        eco: {
            name: '节能模式',
            polling_interval: 10000,    // 10秒
            websocket_interval: 12000   // 12秒
        },
        balanced: {
            name: '平衡模式',
            polling_interval: 3000,     // 3秒
            websocket_interval: 4000    // 4秒
        },
        performance: {
            name: '高性能模式',
            polling_interval: 1500,     // 1.5秒
            websocket_interval: 2000    // 2秒
        }
    };

    /**
     * 根据当前轮询间隔判断性能模式
     */
    function detectCurrentMode(pollingInterval) {
        // 允许一些误差范围（±500ms）
        const tolerance = 500;
        
        for (const [mode, config] of Object.entries(PERFORMANCE_MODES)) {
            if (Math.abs(pollingInterval - config.polling_interval) <= tolerance) {
                return mode;
            }
        }
        
        // 如果没有匹配的预设，返回自定义
        return 'custom';
    }

    /**
     * GET /api/settings/performance-mode
     * 获取当前性能模式
     */
    router.get('/api/settings/performance-mode', async (req, res) => {
        try {
            console.log('[性能模式API] 获取当前模式');

            // 从数据库获取当前轮询间隔
            const currentPollingInterval = await db.setting.get('polling_interval') || 3000;
            const currentWebSocketInterval = await db.setting.get('websocket_interval') || 4000;
            
            // 判断当前属于哪种性能模式
            const currentMode = detectCurrentMode(currentPollingInterval);
            
            console.log('[性能模式API] 当前配置:', {
                polling: currentPollingInterval + 'ms',
                websocket: currentWebSocketInterval + 'ms',
                mode: currentMode
            });

            res.json(pr(1, {
                message: '获取性能模式成功',
                mode: currentMode,
                config: {
                    polling_interval: currentPollingInterval,
                    websocket_interval: currentWebSocketInterval
                },
                modes: PERFORMANCE_MODES
            }));

        } catch (error) {
            console.error('[性能模式API] 获取模式失败:', error);
            res.json(pr(0, '获取性能模式失败: ' + error.message));
        }
    });

    /**
     * PUT /api/settings/performance-mode
     * 更新性能模式
     */
    router.put('/api/settings/performance-mode', async (req, res) => {
        try {
            console.log('[性能模式API] 收到更新请求:', req.body);

            const { mode } = req.body;
            
            // 验证模式是否有效
            if (!mode || !PERFORMANCE_MODES[mode]) {
                return res.json(pr(0, '请选择有效的性能模式: eco, balanced, performance'));
            }

            const config = PERFORMANCE_MODES[mode];
            console.log('[性能模式API] 使用模式配置:', mode, config);

            // 保存到数据库
            await db.setting.set('polling_interval', config.polling_interval);
            await db.setting.set('websocket_interval', config.websocket_interval);
            
            console.log('[性能模式API] 保存新配置:', {
                mode,
                polling_interval: config.polling_interval + 'ms',
                websocket_interval: config.websocket_interval + 'ms'
            });

            // 动态更新运行时配置（如果函数可用）
            try {
                if (svr.locals.updatePollingInterval) {
                    svr.locals.updatePollingInterval(config.polling_interval);
                    console.log('[性能模式API] 轮询间隔已动态更新');
                }

                if (svr.locals.updateWebSocketInterval) {
                    svr.locals.updateWebSocketInterval(config.websocket_interval);
                    console.log('[性能模式API] WebSocket间隔已动态更新');
                }
            } catch (updateError) {
                console.warn('[性能模式API] 动态更新失败（配置已保存）:', updateError.message);
            }

            res.json(pr(1, {
                message: `性能模式已切换到${config.name}`,
                mode,
                config: {
                    polling_interval: config.polling_interval,
                    websocket_interval: config.websocket_interval
                },
                applied_immediately: !!(svr.locals.updatePollingInterval && svr.locals.updateWebSocketInterval)
            }));

        } catch (error) {
            console.error('[性能模式API] 更新模式失败:', error);
            res.json(pr(0, '更新性能模式失败: ' + error.message));
        }
    });

    /**
     * GET /api/settings/performance-mode/presets
     * 获取所有性能模式预设
     */
    router.get('/api/settings/performance-mode/presets', (req, res) => {
        try {
            res.json(pr(1, {
                message: '获取性能模式预设成功',
                modes: PERFORMANCE_MODES
            }));
        } catch (error) {
            console.error('[性能模式API] 获取预设失败:', error);
            res.json(pr(0, '获取预设失败: ' + error.message));
        }
    });

    // 注册路由
    svr.use(router);

    console.log('[性能模式API] 模块已加载');
};