"use strict";
const express = require("express");
const path = require('path');

// 节点数量现在由前端通过 /api/servers 获取，无需后端查询

/**
 * 数据保留设置API模块
 * 提供历史数据保留时间的配置接口
 */
module.exports = (svr, db) => {
    const { pr } = svr.locals;
    const router = express.Router();

    // 定义默认配置
    const DEFAULT_CONFIG = {
        archive_hours: 3,      // 实时数据保留小时数（1-24）
        minute_days: 14,       // 分钟级数据保留天数（1-30）
        hour_days: 90          // 小时级数据保留天数（7-365）
    };

    // 预设方案配置
    const PRESET_CONFIGS = {
        minimal: {
            archive_hours: 1,
            minute_days: 7,
            hour_days: 30
        },
        standard: {
            archive_hours: 3,
            minute_days: 14,
            hour_days: 90
        },
        extended: {
            archive_hours: 6,
            minute_days: 30,
            hour_days: 180
        }
    };

    /**
     * 验证配置参数范围
     */
    function validateConfig(config) {
        const errors = [];

        if (config.archive_hours !== undefined) {
            const hours = parseInt(config.archive_hours);
            if (isNaN(hours) || hours < 1 || hours > 24) {
                errors.push('实时数据保留时间必须在1-24小时之间');
            }
        }

        if (config.minute_days !== undefined) {
            const days = parseInt(config.minute_days);
            if (isNaN(days) || days < 1 || days > 30) {
                errors.push('分钟级数据保留天数必须在1-30天之间');
            }
        }

        if (config.hour_days !== undefined) {
            const days = parseInt(config.hour_days);
            if (isNaN(days) || days < 7 || days > 365) {
                errors.push('小时级数据保留天数必须在7-365天之间');
            }
        }

        return errors;
    }

    // 单节点存储量信息（前端计算用，此处仅作文档记录）
    // minimal: 0.9MB/节点, standard: 1.9MB/节点, extended: 4.1MB/节点

    /**
     * GET /api/settings/data-retention
     * 获取当前数据保留设置
     */
    router.get('/api/settings/data-retention', async (req, res) => {
        try {
            console.log('[数据保留API] 获取当前设置');

            // 从数据库获取当前配置，如果不存在使用默认值
            const config = {
                archive_hours: await db.setting.get('data_retention_archive_hours') || DEFAULT_CONFIG.archive_hours,
                minute_days: await db.setting.get('data_retention_minute_days') || DEFAULT_CONFIG.minute_days,
                hour_days: await db.setting.get('data_retention_hour_days') || DEFAULT_CONFIG.hour_days
            };

            // 确定当前使用的预设方案
            let currentPreset = 'custom';
            for (const [presetName, presetConfig] of Object.entries(PRESET_CONFIGS)) {
                if (config.archive_hours === presetConfig.archive_hours &&
                    config.minute_days === presetConfig.minute_days &&
                    config.hour_days === presetConfig.hour_days) {
                    currentPreset = presetName;
                    break;
                }
            }

            console.log('[数据保留API] 当前配置:', config, '预设:', currentPreset);

            res.json(pr(1, {
                message: '获取设置成功',
                config,
                current_preset: currentPreset,
                presets: PRESET_CONFIGS,
                defaults: DEFAULT_CONFIG
            }));

        } catch (error) {
            console.error('[数据保留API] 获取设置失败:', error);
            res.json(pr(0, '获取设置失败: ' + error.message));
        }
    });

    /**
     * PUT /api/settings/data-retention
     * 更新数据保留设置
     */
    router.put('/api/settings/data-retention', async (req, res) => {
        try {
            console.log('[数据保留API] 收到更新请求:', req.body);

            const { preset } = req.body;
            
            // 只支持预设方案选择
            if (!preset || !PRESET_CONFIGS[preset]) {
                return res.json(pr(0, '请选择有效的预设方案: minimal, standard, extended'));
            }

            const config = { ...PRESET_CONFIGS[preset] };
            console.log('[数据保留API] 使用预设方案:', preset, config);

            // 保存到数据库
            const updates = [];
            await db.setting.set('data_retention_archive_hours', config.archive_hours);
            await db.setting.set('data_retention_minute_days', config.minute_days);
            await db.setting.set('data_retention_hour_days', config.hour_days);
            
            updates.push(`已切换到${preset}模式`);
            updates.push(`实时数据: ${config.archive_hours}小时`);
            updates.push(`分钟级数据: ${config.minute_days}天`);
            updates.push(`小时级数据: ${config.hour_days}天`);

            console.log('[数据保留API] 保存新配置:', config);

            // 清除monitor.js中的缓存，确保立即使用新配置
            try {
                // 清除数据保留设置的缓存，强制重新读取数据库配置
                const monitor = require('../database/monitor');
                if (global.dataRetentionCache) {
                    global.dataRetentionCache.clear();
                    console.log('[数据保留API] 缓存已清除，新配置立即生效');
                } else {
                    console.log('[数据保留API] 数据保留配置已保存，将在下次数据库操作时生效');
                }
            } catch (err) {
                console.warn('[数据保留API] 缓存清除失败（不影响配置保存）:', err.message);
            }


            res.json(pr(1, {
                message: '数据保留设置已更新',
                preset,
                config,
                updates
            }));

        } catch (error) {
            console.error('[数据保留API] 更新设置失败:', error);
            res.json(pr(0, '更新设置失败: ' + error.message));
        }
    });

    /**
     * GET /api/settings/data-retention/presets
     * 获取预设方案列表
     */
    router.get('/api/settings/data-retention/presets', (req, res) => {
        try {
            res.json(pr(1, {
                message: '获取预设成功',
                presets: PRESET_CONFIGS
            }));
        } catch (error) {
            console.error('[数据保留API] 获取预设失败:', error);
            res.json(pr(0, '获取预设失败: ' + error.message));
        }
    });

    /**
     * GET /api/data-retention
     * 简化API端点，用于性能测试
     */
    router.get('/api/data-retention', async (req, res) => {
        try {
            const startTime = Date.now();
            
            // 获取当前配置（优化后的查询）
            const config = {
                archive_hours: await db.setting.get('data_retention_archive_hours') || DEFAULT_CONFIG.archive_hours,
                minute_days: await db.setting.get('data_retention_minute_days') || DEFAULT_CONFIG.minute_days,
                hour_days: await db.setting.get('data_retention_hour_days') || DEFAULT_CONFIG.hour_days
            };
            
            const queryTime = Date.now() - startTime;
            
            res.json({
                success: true,
                data: config,
                query_time_ms: queryTime,
                timestamp: new Date().toISOString()
            });
            
        } catch (error) {
            console.error('[数据保留API] 简化API请求失败:', error);
            res.status(500).json({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    });

    // 注册路由
    svr.use(router);

    console.log('[数据保留API] 模块已加载');
};