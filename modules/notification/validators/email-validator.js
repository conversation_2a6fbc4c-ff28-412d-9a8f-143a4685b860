/**
 * 邮件验证器
 * 验证邮件配置数据格式
 */
"use strict";

const { createValidationResult, validators } = require('../middleware/validation-middleware');

class EmailValidator {
    /**
     * 验证邮件配置
     * @param {Object} config - 邮件配置对象
     * @returns {Object} 验证结果
     */
    static validateConfig(config) {
        const errors = [];
        const cleanConfig = {};

        if (!config || typeof config !== 'object') {
            return createValidationResult(false, ['配置数据必须是对象']);
        }

        // 验证 enabled 字段
        if (config.enabled !== undefined) {
            if (typeof config.enabled !== 'boolean') {
                errors.push('enabled 字段必须是布尔值');
            } else {
                cleanConfig.enabled = config.enabled;
            }
        } else {
            cleanConfig.enabled = false;
        }

        // 如果启用邮件，验证必填字段
        if (config.enabled) {
            // 验证 SMTP 主机
            const hostError = validators.required(config.host, 'host');
            if (hostError) {
                errors.push(hostError);
            } else if (typeof config.host !== 'string') {
                errors.push('host 必须是字符串');
            } else {
                cleanConfig.host = config.host.trim();
            }

            // 验证 SMTP 端口
            const portError = validators.required(config.port, 'port');
            if (portError) {
                errors.push(portError);
            } else {
                const portRangeError = validators.numberRange(config.port, 1, 65535, 'port');
                if (portRangeError) {
                    errors.push(portRangeError);
                } else {
                    cleanConfig.port = config.port;
                }
            }

            // 验证发件人地址
            const fromError = validators.required(config.from_address, 'from_address');
            if (fromError) {
                errors.push(fromError);
            } else {
                const emailError = validators.email(config.from_address);
                if (emailError) {
                    errors.push(`发件人地址${emailError}`);
                } else {
                    cleanConfig.from_address = config.from_address.trim();
                }
            }

            // 验证认证用户名
            const userError = validators.required(config.auth_user, 'auth_user');
            if (userError) {
                errors.push(userError);
            } else if (typeof config.auth_user !== 'string') {
                errors.push('auth_user 必须是字符串');
            } else {
                cleanConfig.auth_user = config.auth_user.trim();
            }

            // 验证认证密码
            const passError = validators.required(config.auth_pass, 'auth_pass');
            if (passError) {
                errors.push(passError);
            } else if (typeof config.auth_pass !== 'string') {
                errors.push('auth_pass 必须是字符串');
            } else {
                cleanConfig.auth_pass = config.auth_pass;
            }
        } else {
            // 如果未启用，设置默认值
            cleanConfig.host = config.host || '';
            cleanConfig.port = config.port || 587;
            cleanConfig.from_address = config.from_address || '';
            cleanConfig.auth_user = config.auth_user || '';
            cleanConfig.auth_pass = config.auth_pass || '';
        }

        // 验证 secure 字段
        if (config.secure !== undefined) {
            if (typeof config.secure !== 'boolean') {
                errors.push('secure 字段必须是布尔值');
            } else {
                cleanConfig.secure = config.secure;
            }
        } else {
            cleanConfig.secure = false;
        }

        // 验证发件人名称
        if (config.from_name !== undefined) {
            if (typeof config.from_name !== 'string') {
                errors.push('from_name 必须是字符串');
            } else {
                cleanConfig.from_name = config.from_name.trim();
            }
        } else {
            cleanConfig.from_name = '';
        }

        // 验证收件人列表
        const toResult = this.validateEmailList(config.to_addresses, 'to_addresses');
        if (!toResult.isValid) {
            errors.push(...toResult.errors);
        } else {
            cleanConfig.to_addresses = toResult.data;
        }

        // 验证抄送列表
        const ccResult = this.validateEmailList(config.cc_addresses, 'cc_addresses');
        if (!ccResult.isValid) {
            errors.push(...ccResult.errors);
        } else {
            cleanConfig.cc_addresses = ccResult.data;
        }

        // 验证密送列表
        const bccResult = this.validateEmailList(config.bcc_addresses, 'bcc_addresses');
        if (!bccResult.isValid) {
            errors.push(...bccResult.errors);
        } else {
            cleanConfig.bcc_addresses = bccResult.data;
        }

        // 验证通知类型配置
        const notificationTypesResult = this.validateNotificationTypes(config.notification_types);
        if (!notificationTypesResult.isValid) {
            errors.push(...notificationTypesResult.errors);
        } else {
            cleanConfig.notification_types = notificationTypesResult.data;
        }

        return createValidationResult(errors.length === 0, errors, cleanConfig);
    }

    /**
     * 验证邮件地址列表
     * @param {Array|string} emails - 邮件地址数组或字符串
     * @param {string} fieldName - 字段名称
     * @returns {Object} 验证结果
     */
    static validateEmailList(emails, fieldName = 'emails') {
        const errors = [];
        let cleanEmails = [];

        if (!emails) {
            return createValidationResult(true, [], []);
        }

        // 如果是字符串，尝试解析为数组
        if (typeof emails === 'string') {
            try {
                emails = emails.split(',').map(email => email.trim()).filter(email => email);
            } catch (e) {
                return createValidationResult(false, [`${fieldName} 格式不正确`]);
            }
        }

        if (!Array.isArray(emails)) {
            return createValidationResult(false, [`${fieldName} 必须是数组或逗号分隔的字符串`]);
        }

        for (const email of emails) {
            if (typeof email !== 'string') {
                errors.push(`${fieldName} 中包含非字符串项: ${email}`);
                continue;
            }

            const trimmedEmail = email.trim();
            if (!trimmedEmail) {
                continue; // 跳过空字符串
            }

            const emailError = validators.email(trimmedEmail);
            if (emailError) {
                errors.push(`${fieldName} 中包含无效邮箱: ${trimmedEmail}`);
            } else {
                cleanEmails.push(trimmedEmail);
            }
        }

        // 去重
        cleanEmails = [...new Set(cleanEmails)];

        return createValidationResult(errors.length === 0, errors, cleanEmails);
    }

    /**
     * 验证通知类型配置
     * @param {Object} notificationTypes - 通知类型配置
     * @returns {Object} 验证结果
     */
    static validateNotificationTypes(notificationTypes) {
        const defaultTypes = {
            serverOnline: true,
            serverOffline: true,
            trafficLimit: true,
            testNotification: true,
            statusSummary: true
        };

        if (!notificationTypes || typeof notificationTypes !== 'object') {
            return createValidationResult(true, [], defaultTypes);
        }

        const cleanTypes = { ...defaultTypes };
        const errors = [];

        for (const [key, value] of Object.entries(notificationTypes)) {
            if (key in defaultTypes) {
                if (typeof value === 'boolean') {
                    cleanTypes[key] = value;
                } else {
                    errors.push(`通知类型 ${key} 必须是布尔值`);
                }
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanTypes);
    }

    /**
     * 验证测试邮件请求
     * @param {Object} data - 测试邮件数据
     * @returns {Object} 验证结果
     */
    static validateTestEmail(data) {
        const errors = [];
        const cleanData = {};

        if (!data || typeof data !== 'object') {
            return createValidationResult(false, ['请求数据必须是对象']);
        }

        // 验证收件人（可选，如果不提供则使用配置中的默认值）
        if (data.to !== undefined) {
            const toResult = this.validateEmailList(data.to, 'to');
            if (!toResult.isValid) {
                errors.push(...toResult.errors);
            } else {
                cleanData.to = toResult.data;
            }
        }

        // 验证主题（可选）
        if (data.subject !== undefined) {
            if (typeof data.subject !== 'string') {
                errors.push('邮件主题必须是字符串');
            } else if (data.subject.length > 200) {
                errors.push('邮件主题不能超过200个字符');
            } else {
                cleanData.subject = data.subject.trim();
            }
        }

        // 验证内容（可选）
        if (data.content !== undefined) {
            if (typeof data.content !== 'string') {
                errors.push('邮件内容必须是字符串');
            } else if (data.content.length > 10000) {
                errors.push('邮件内容不能超过10000个字符');
            } else {
                cleanData.content = data.content;
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }

    /**
     * 验证日志查询参数
     * @param {Object} query - 查询参数
     * @returns {Object} 验证结果
     */
    static validateLogQuery(query) {
        const errors = [];
        const cleanQuery = {};

        if (!query || typeof query !== 'object') {
            return createValidationResult(false, ['查询参数必须是对象']);
        }

        // 验证页码
        if (query.page !== undefined) {
            const page = parseInt(query.page, 10);
            if (isNaN(page) || page < 1) {
                errors.push('页码必须是大于0的整数');
            } else {
                cleanQuery.page = page;
            }
        } else {
            cleanQuery.page = 1;
        }

        // 验证页面大小
        if (query.pageSize !== undefined) {
            const pageSize = parseInt(query.pageSize, 10);
            if (isNaN(pageSize) || pageSize < 1 || pageSize > 100) {
                errors.push('页面大小必须是1-100之间的整数');
            } else {
                cleanQuery.pageSize = pageSize;
            }
        } else {
            cleanQuery.pageSize = 20;
        }

        return createValidationResult(errors.length === 0, errors, cleanQuery);
    }

    /**
     * 验证日志清理参数
     * @param {Object} data - 清理参数
     * @returns {Object} 验证结果
     */
    static validateLogCleanup(data) {
        const errors = [];
        const cleanData = {};

        if (!data || typeof data !== 'object') {
            return createValidationResult(false, ['请求数据必须是对象']);
        }

        // 验证清理天数
        if (data.days !== undefined) {
            const days = parseInt(data.days, 10);
            if (isNaN(days) || days < 1 || days > 365) {
                errors.push('清理天数必须是1-365之间的整数');
            } else {
                cleanData.days = days;
            }
        } else {
            cleanData.days = 30; // 默认30天
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }
}

module.exports = EmailValidator;
