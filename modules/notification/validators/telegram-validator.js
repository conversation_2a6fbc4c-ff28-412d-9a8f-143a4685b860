/**
 * Telegram 验证器
 * 验证 Telegram 配置数据格式
 */
"use strict";

const { createValidationResult, validators } = require('../middleware/validation-middleware');

class TelegramValidator {
    /**
     * 验证 Telegram 配置
     * @param {Object} config - Telegram 配置对象
     * @returns {Object} 验证结果
     */
    static validateConfig(config) {
        const errors = [];
        const cleanConfig = {};

        if (!config || typeof config !== 'object') {
            return createValidationResult(false, ['配置数据必须是对象']);
        }

        // 验证 enabled 字段
        if (config.enabled !== undefined) {
            if (typeof config.enabled !== 'boolean') {
                errors.push('enabled 字段必须是布尔值');
            } else {
                cleanConfig.enabled = config.enabled;
            }
        } else {
            cleanConfig.enabled = false;
        }

        // 验证 token 字段
        if (config.enabled) {
            const tokenError = validators.required(config.token, 'token');
            if (tokenError) {
                errors.push(tokenError);
            } else if (typeof config.token !== 'string') {
                errors.push('token 必须是字符串');
            } else if (!this.validateTokenFormat(config.token)) {
                errors.push('token 格式不正确，应为 bot{数字}:{字符串} 格式');
            } else {
                cleanConfig.token = config.token.trim();
            }
        }

        // 验证 chatIds 字段
        const chatIdsResult = this.validateChatIds(config.chatIds);
        if (!chatIdsResult.isValid) {
            errors.push(...chatIdsResult.errors);
        } else {
            cleanConfig.chatIds = chatIdsResult.data;
        }

        // 验证 webhook 字段
        if (config.webhook !== undefined) {
            if (typeof config.webhook !== 'boolean') {
                errors.push('webhook 字段必须是布尔值');
            } else {
                cleanConfig.webhook = config.webhook;
            }
        } else {
            cleanConfig.webhook = false;
        }

        // 验证 webhookPort 字段
        if (config.webhookPort !== undefined) {
            const portError = validators.numberRange(config.webhookPort, 1, 65535, 'webhookPort');
            if (portError) {
                errors.push(portError);
            } else {
                cleanConfig.webhookPort = config.webhookPort;
            }
        }

        // 验证 baseApiUrl 字段
        if (config.baseApiUrl !== undefined) {
            if (typeof config.baseApiUrl !== 'string') {
                errors.push('baseApiUrl 必须是字符串');
            } else if (config.baseApiUrl && !this.validateUrl(config.baseApiUrl)) {
                errors.push('baseApiUrl 格式不正确');
            } else {
                cleanConfig.baseApiUrl = config.baseApiUrl?.trim() || '';
            }
        }

        // 验证通知类型配置
        const notificationTypesResult = this.validateNotificationTypes(config.notificationTypes);
        if (!notificationTypesResult.isValid) {
            errors.push(...notificationTypesResult.errors);
        } else {
            cleanConfig.notificationTypes = notificationTypesResult.data;
        }

        // 验证离线通知延迟
        if (config.offlineNotificationDelay !== undefined) {
            const delayError = validators.numberRange(config.offlineNotificationDelay, 0, 3600, 'offlineNotificationDelay');
            if (delayError) {
                errors.push(delayError);
            } else {
                cleanConfig.offlineNotificationDelay = config.offlineNotificationDelay;
            }
        } else {
            cleanConfig.offlineNotificationDelay = 300; // 默认5分钟
        }

        // 验证流量阈值
        const thresholdsResult = this.validateTrafficThresholds(config.trafficThresholds);
        if (!thresholdsResult.isValid) {
            errors.push(...thresholdsResult.errors);
        } else {
            cleanConfig.trafficThresholds = thresholdsResult.data;
        }

        return createValidationResult(errors.length === 0, errors, cleanConfig);
    }

    /**
     * 验证 Chat IDs
     * @param {Array|string} chatIds - Chat IDs 数组或字符串
     * @returns {Object} 验证结果
     */
    static validateChatIds(chatIds) {
        const errors = [];
        let cleanChatIds = [];

        if (!chatIds) {
            return createValidationResult(true, [], []);
        }

        // 如果是字符串，尝试解析为数组
        if (typeof chatIds === 'string') {
            try {
                chatIds = chatIds.split(',').map(id => id.trim()).filter(id => id);
            } catch (e) {
                return createValidationResult(false, ['chatIds 格式不正确']);
            }
        }

        if (!Array.isArray(chatIds)) {
            return createValidationResult(false, ['chatIds 必须是数组或逗号分隔的字符串']);
        }

        for (const chatId of chatIds) {
            if (typeof chatId === 'string') {
                const trimmedId = chatId.trim();
                if (trimmedId && this.validateChatIdFormat(trimmedId)) {
                    cleanChatIds.push(trimmedId);
                } else if (trimmedId) {
                    errors.push(`无效的 Chat ID: ${trimmedId}`);
                }
            } else if (typeof chatId === 'number') {
                cleanChatIds.push(chatId.toString());
            } else {
                errors.push(`Chat ID 必须是字符串或数字: ${chatId}`);
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanChatIds);
    }

    /**
     * 验证通知类型配置
     * @param {Object} notificationTypes - 通知类型配置
     * @returns {Object} 验证结果
     */
    static validateNotificationTypes(notificationTypes) {
        const defaultTypes = {
            serverOnline: true,
            serverOffline: true,
            trafficLimit: true,
            testNotification: true,
            statusSummary: true,
            newServerDiscovered: true,
            serverApproved: true
        };

        if (!notificationTypes || typeof notificationTypes !== 'object') {
            return createValidationResult(true, [], defaultTypes);
        }

        const cleanTypes = { ...defaultTypes };
        const errors = [];

        for (const [key, value] of Object.entries(notificationTypes)) {
            if (key in defaultTypes) {
                if (typeof value === 'boolean') {
                    cleanTypes[key] = value;
                } else {
                    errors.push(`通知类型 ${key} 必须是布尔值`);
                }
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanTypes);
    }

    /**
     * 验证流量阈值
     * @param {Array} thresholds - 流量阈值数组
     * @returns {Object} 验证结果
     */
    static validateTrafficThresholds(thresholds) {
        if (!thresholds) {
            return createValidationResult(true, [], [80, 90, 95]);
        }

        if (!Array.isArray(thresholds)) {
            return createValidationResult(false, ['流量阈值必须是数组']);
        }

        const errors = [];
        const cleanThresholds = [];

        for (const threshold of thresholds) {
            if (typeof threshold === 'number' && threshold > 0 && threshold <= 100) {
                cleanThresholds.push(threshold);
            } else {
                errors.push(`无效的流量阈值: ${threshold}，必须是1-100之间的数字`);
            }
        }

        // 去重并排序
        const uniqueThresholds = [...new Set(cleanThresholds)].sort((a, b) => a - b);

        return createValidationResult(errors.length === 0, errors, uniqueThresholds);
    }

    /**
     * 验证 Token 格式
     * @param {string} token - Telegram Bot Token
     * @returns {boolean} 是否有效
     */
    static validateTokenFormat(token) {
        if (typeof token !== 'string') return false;
        // Telegram Bot Token 格式: bot{数字}:{字符串}
        const tokenRegex = /^bot\d+:[A-Za-z0-9_-]+$/;
        return tokenRegex.test(token);
    }

    /**
     * 验证 Chat ID 格式
     * @param {string} chatId - Chat ID
     * @returns {boolean} 是否有效
     */
    static validateChatIdFormat(chatId) {
        if (typeof chatId !== 'string') return false;
        // Chat ID 可以是数字或以@开头的用户名
        return /^(-?\d+|@[a-zA-Z0-9_]+)$/.test(chatId);
    }

    /**
     * 验证 URL 格式
     * @param {string} url - URL
     * @returns {boolean} 是否有效
     */
    static validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 验证测试通知请求
     * @param {Object} data - 测试通知数据
     * @returns {Object} 验证结果
     */
    static validateTestNotification(data) {
        const errors = [];
        const cleanData = {};

        if (!data || typeof data !== 'object') {
            return createValidationResult(false, ['请求数据必须是对象']);
        }

        // 验证 chatIds（可选，如果不提供则使用配置中的默认值）
        if (data.chatIds !== undefined) {
            const chatIdsResult = this.validateChatIds(data.chatIds);
            if (!chatIdsResult.isValid) {
                errors.push(...chatIdsResult.errors);
            } else {
                cleanData.chatIds = chatIdsResult.data;
            }
        }

        // 验证消息内容（可选）
        if (data.message !== undefined) {
            if (typeof data.message !== 'string') {
                errors.push('消息内容必须是字符串');
            } else if (data.message.length > 4096) {
                errors.push('消息内容不能超过4096个字符');
            } else {
                cleanData.message = data.message;
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }
}

module.exports = TelegramValidator;
