/**
 * 验证器模块入口文件
 * 统一导出所有验证器组件
 */
"use strict";

const TelegramValidator = require('./telegram-validator');
const EmailValidator = require('./email-validator');
const TasksValidator = require('./tasks-validator');

module.exports = {
    // 验证器类
    TelegramValidator,
    EmailValidator,
    TasksValidator,
    
    // 便捷访问验证方法
    telegram: {
        validateConfig: TelegramValidator.validateConfig.bind(TelegramValidator),
        validateChatIds: TelegramValidator.validateChatIds.bind(TelegramValidator),
        validateNotificationTypes: TelegramValidator.validateNotificationTypes.bind(TelegramValidator),
        validateTrafficThresholds: TelegramValidator.validateTrafficThresholds.bind(TelegramValidator),
        validateTestNotification: TelegramValidator.validateTestNotification.bind(TelegramValidator),
        validateTokenFormat: TelegramValidator.validateTokenFormat.bind(TelegramValidator),
        validateChatIdFormat: TelegramValidator.validateChatIdFormat.bind(TelegramValidator)
    },
    
    email: {
        validateConfig: EmailValidator.validateConfig.bind(EmailValidator),
        validateEmailList: EmailValidator.validateEmailList.bind(EmailValidator),
        validateNotificationTypes: EmailValidator.validateNotificationTypes.bind(EmailValidator),
        validateTestEmail: EmailValidator.validateTestEmail.bind(EmailValidator),
        validateLogQuery: EmailValidator.validateLogQuery.bind(EmailValidator),
        validateLogCleanup: EmailValidator.validateLogCleanup.bind(EmailValidator)
    },
    
    tasks: {
        validateTask: TasksValidator.validateTask.bind(TasksValidator),
        normalizeThresholds: TasksValidator.normalizeThresholds.bind(TasksValidator),
        validateTaskId: TasksValidator.validateTaskId.bind(TasksValidator),
        validateServerId: TasksValidator.validateServerId.bind(TasksValidator),
        validateToggleTask: TasksValidator.validateToggleTask.bind(TasksValidator),
        validateTaskQuery: TasksValidator.validateTaskQuery.bind(TasksValidator),
        validateBatchOperation: TasksValidator.validateBatchOperation.bind(TasksValidator)
    }
};
