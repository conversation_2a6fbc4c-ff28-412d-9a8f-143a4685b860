/**
 * 任务验证器
 * 验证自定义通知任务数据格式
 */
"use strict";

const { createValidationResult, validators } = require('../middleware/validation-middleware');

class TasksValidator {
    /**
     * 验证任务创建/更新数据
     * @param {Object} taskData - 任务数据对象
     * @returns {Object} 验证结果
     */
    static validateTask(taskData) {
        const errors = [];
        const cleanData = {};

        if (!taskData || typeof taskData !== 'object') {
            return createValidationResult(false, ['任务数据必须是对象']);
        }

        // 验证服务器ID
        const sidError = validators.required(taskData.sid, 'sid');
        if (sidError) {
            errors.push(sidError);
        } else if (typeof taskData.sid !== 'string') {
            errors.push('服务器ID必须是字符串');
        } else {
            cleanData.sid = taskData.sid.trim();
        }

        // 验证周期
        const validPeriods = ['daily', 'weekly', 'monthly'];
        if (taskData.period !== undefined) {
            if (!validPeriods.includes(taskData.period)) {
                errors.push(`周期必须是以下值之一: ${validPeriods.join(', ')}`);
            } else {
                cleanData.period = taskData.period;
            }
        } else {
            cleanData.period = 'monthly'; // 默认月度
        }

        // 验证流量上限
        if (taskData.traffic_limit !== undefined && taskData.traffic_limit !== null) {
            if (typeof taskData.traffic_limit !== 'number' || taskData.traffic_limit < 0) {
                errors.push('流量上限必须是非负数');
            } else {
                cleanData.traffic_limit = taskData.traffic_limit;
            }
        } else {
            cleanData.traffic_limit = null;
        }

        // 验证和归一化阈值
        const thresholdsResult = this.normalizeThresholds(taskData.thresholds);
        if (!thresholdsResult.isValid) {
            errors.push(...thresholdsResult.errors);
        } else {
            cleanData.thresholds = thresholdsResult.data;
        }

        // 验证方向
        const validDirections = ['both', 'in', 'out', 'max'];
        if (taskData.direction !== undefined) {
            if (!validDirections.includes(taskData.direction)) {
                errors.push(`方向必须是以下值之一: ${validDirections.join(', ')}`);
            } else {
                cleanData.direction = taskData.direction;
            }
        } else {
            cleanData.direction = 'both'; // 默认双向
        }

        // 验证重置日期
        if (taskData.reset_day !== undefined && taskData.reset_day !== null) {
            const resetDay = parseInt(taskData.reset_day, 10);
            if (isNaN(resetDay) || resetDay < 1 || resetDay > 31) {
                errors.push('重置日期必须是1-31之间的整数');
            } else {
                cleanData.reset_day = resetDay;
            }
        } else {
            cleanData.reset_day = null;
        }

        // 验证启用状态
        if (taskData.enabled !== undefined) {
            if (typeof taskData.enabled !== 'boolean') {
                errors.push('启用状态必须是布尔值');
            } else {
                cleanData.enabled = taskData.enabled;
            }
        } else {
            cleanData.enabled = true; // 默认启用
        }

        // 验证任务类型
        const validTypes = ['traffic'];
        if (taskData.type !== undefined) {
            if (!validTypes.includes(taskData.type)) {
                errors.push(`任务类型必须是以下值之一: ${validTypes.join(', ')}`);
            } else {
                cleanData.type = taskData.type;
            }
        } else {
            cleanData.type = 'traffic'; // 默认流量类型
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }

    /**
     * 归一化和验证阈值数组
     * @param {Array|string} thresholds - 阈值数组或字符串
     * @returns {Object} 验证结果
     */
    static normalizeThresholds(thresholds) {
        const errors = [];
        let cleanThresholds = [];

        if (!thresholds) {
            return createValidationResult(true, [], []);
        }

        // 如果是字符串，尝试解析
        if (typeof thresholds === 'string') {
            try {
                // 支持逗号分隔的字符串
                thresholds = thresholds.split(',').map(t => {
                    const num = parseFloat(t.trim());
                    if (isNaN(num)) {
                        throw new Error(`无效的阈值: ${t.trim()}`);
                    }
                    return num;
                });
            } catch (e) {
                return createValidationResult(false, [e.message]);
            }
        }

        if (!Array.isArray(thresholds)) {
            return createValidationResult(false, ['阈值必须是数组或逗号分隔的字符串']);
        }

        for (const threshold of thresholds) {
            let num;
            
            if (typeof threshold === 'string') {
                num = parseFloat(threshold.trim());
                if (isNaN(num)) {
                    errors.push(`无效的阈值: ${threshold}`);
                    continue;
                }
            } else if (typeof threshold === 'number') {
                num = threshold;
            } else {
                errors.push(`阈值必须是数字: ${threshold}`);
                continue;
            }

            // 验证阈值范围
            if (num <= 0 || num > 100) {
                errors.push(`阈值必须在0-100之间: ${num}`);
            } else {
                cleanThresholds.push(num);
            }
        }

        // 去重并排序
        cleanThresholds = [...new Set(cleanThresholds)].sort((a, b) => a - b);

        return createValidationResult(errors.length === 0, errors, cleanThresholds);
    }

    /**
     * 验证任务ID
     * @param {string} taskId - 任务ID
     * @returns {Object} 验证结果
     */
    static validateTaskId(taskId) {
        const errors = [];

        if (!taskId) {
            errors.push('任务ID是必填字段');
        } else if (typeof taskId !== 'string') {
            errors.push('任务ID必须是字符串');
        } else if (taskId.trim().length === 0) {
            errors.push('任务ID不能为空');
        }

        return createValidationResult(errors.length === 0, errors, taskId?.trim());
    }

    /**
     * 验证服务器ID
     * @param {string} serverId - 服务器ID
     * @returns {Object} 验证结果
     */
    static validateServerId(serverId) {
        const errors = [];

        if (!serverId) {
            errors.push('服务器ID是必填字段');
        } else if (typeof serverId !== 'string') {
            errors.push('服务器ID必须是字符串');
        } else if (serverId.trim().length === 0) {
            errors.push('服务器ID不能为空');
        }

        return createValidationResult(errors.length === 0, errors, serverId?.trim());
    }

    /**
     * 验证任务切换状态请求
     * @param {Object} data - 切换状态数据
     * @returns {Object} 验证结果
     */
    static validateToggleTask(data) {
        const errors = [];
        const cleanData = {};

        if (!data || typeof data !== 'object') {
            return createValidationResult(false, ['请求数据必须是对象']);
        }

        // 验证任务ID
        const taskIdResult = this.validateTaskId(data.id);
        if (!taskIdResult.isValid) {
            errors.push(...taskIdResult.errors);
        } else {
            cleanData.id = taskIdResult.data;
        }

        // 验证启用状态（可选，如果不提供则切换当前状态）
        if (data.enabled !== undefined) {
            if (typeof data.enabled !== 'boolean') {
                errors.push('启用状态必须是布尔值');
            } else {
                cleanData.enabled = data.enabled;
            }
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }

    /**
     * 验证任务查询参数
     * @param {Object} query - 查询参数
     * @returns {Object} 验证结果
     */
    static validateTaskQuery(query) {
        const errors = [];
        const cleanQuery = {};

        if (!query || typeof query !== 'object') {
            return createValidationResult(false, ['查询参数必须是对象']);
        }

        // 验证服务器ID过滤（可选）
        if (query.sid !== undefined) {
            const sidResult = this.validateServerId(query.sid);
            if (!sidResult.isValid) {
                errors.push(...sidResult.errors);
            } else {
                cleanQuery.sid = sidResult.data;
            }
        }

        // 验证启用状态过滤（可选）
        if (query.enabled !== undefined) {
            if (query.enabled === 'true' || query.enabled === true) {
                cleanQuery.enabled = true;
            } else if (query.enabled === 'false' || query.enabled === false) {
                cleanQuery.enabled = false;
            } else {
                errors.push('启用状态过滤必须是布尔值');
            }
        }

        // 验证任务类型过滤（可选）
        if (query.type !== undefined) {
            const validTypes = ['traffic'];
            if (!validTypes.includes(query.type)) {
                errors.push(`任务类型必须是以下值之一: ${validTypes.join(', ')}`);
            } else {
                cleanQuery.type = query.type;
            }
        }

        // 验证排序字段（可选）
        if (query.sortBy !== undefined) {
            const validSortFields = ['created_at', 'updated_at', 'sid', 'period'];
            if (!validSortFields.includes(query.sortBy)) {
                errors.push(`排序字段必须是以下值之一: ${validSortFields.join(', ')}`);
            } else {
                cleanQuery.sortBy = query.sortBy;
            }
        } else {
            cleanQuery.sortBy = 'created_at';
        }

        // 验证排序方向（可选）
        if (query.sortOrder !== undefined) {
            const validSortOrders = ['asc', 'desc'];
            if (!validSortOrders.includes(query.sortOrder.toLowerCase())) {
                errors.push(`排序方向必须是以下值之一: ${validSortOrders.join(', ')}`);
            } else {
                cleanQuery.sortOrder = query.sortOrder.toLowerCase();
            }
        } else {
            cleanQuery.sortOrder = 'desc';
        }

        return createValidationResult(errors.length === 0, errors, cleanQuery);
    }

    /**
     * 验证批量操作请求
     * @param {Object} data - 批量操作数据
     * @returns {Object} 验证结果
     */
    static validateBatchOperation(data) {
        const errors = [];
        const cleanData = {};

        if (!data || typeof data !== 'object') {
            return createValidationResult(false, ['请求数据必须是对象']);
        }

        // 验证任务ID列表
        if (!Array.isArray(data.taskIds)) {
            errors.push('任务ID列表必须是数组');
        } else if (data.taskIds.length === 0) {
            errors.push('任务ID列表不能为空');
        } else {
            const cleanTaskIds = [];
            for (const taskId of data.taskIds) {
                const taskIdResult = this.validateTaskId(taskId);
                if (!taskIdResult.isValid) {
                    errors.push(...taskIdResult.errors);
                } else {
                    cleanTaskIds.push(taskIdResult.data);
                }
            }
            cleanData.taskIds = cleanTaskIds;
        }

        // 验证操作类型
        const validOperations = ['enable', 'disable', 'delete'];
        if (!data.operation) {
            errors.push('操作类型是必填字段');
        } else if (!validOperations.includes(data.operation)) {
            errors.push(`操作类型必须是以下值之一: ${validOperations.join(', ')}`);
        } else {
            cleanData.operation = data.operation;
        }

        return createValidationResult(errors.length === 0, errors, cleanData);
    }
}

module.exports = TasksValidator;
