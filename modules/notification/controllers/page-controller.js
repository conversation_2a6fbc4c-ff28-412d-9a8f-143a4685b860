/**
 * 页面控制器
 * 处理页面渲染逻辑
 */
"use strict";

const { asyncErrorHandler } = require('../middleware/error-middleware');

class PageController {
    constructor(configService) {
        this.configService = configService;
    }

    /**
     * 渲染通知设置页面
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async renderNotificationPage(req, res) {
        try {
            // 聚合所有配置数据
            const allConfigs = await this.configService.getAllConfigs();
            
            // 获取配置统计信息
            const configStats = await this.configService.getConfigStats();
            
            // 渲染页面
            res.render('admin/notification', {
                setting: allConfigs.telegram || {},
                emailConfig: allConfigs.email || {},
                tasks: allConfigs.tasks || [],
                servers: allConfigs.servers || [],
                configStats: configStats,
                currentPage: 'notification',
                timestamp: allConfigs.timestamp
            });
        } catch (error) {
            console.error('[页面控制器] 渲染通知设置页面失败:', error);
            res.status(500).render('error', {
                error: {
                    status: 500,
                    message: '加载通知设置页面失败'
                }
            });
        }
    }

    /**
     * 获取页面数据（AJAX接口）
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getPageData(req, res) {
        try {
            // 聚合所有配置数据
            const allConfigs = await this.configService.getAllConfigs();
            
            // 获取配置统计信息
            const configStats = await this.configService.getConfigStats();
            
            res.json({
                code: 1,
                data: {
                    configs: allConfigs,
                    stats: configStats
                }
            });
        } catch (error) {
            console.error('[页面控制器] 获取页面数据失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取页面数据失败',
                error: error.message
            });
        }
    }

    /**
     * 获取服务器列表
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getServers(req, res) {
        try {
            const servers = await this.configService.getServers();
            
            res.json({
                code: 1,
                data: servers
            });
        } catch (error) {
            console.error('[页面控制器] 获取服务器列表失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取服务器列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取配置概览
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getConfigOverview(req, res) {
        try {
            const configStats = await this.configService.getConfigStats();
            
            res.json({
                code: 1,
                data: configStats
            });
        } catch (error) {
            console.error('[页面控制器] 获取配置概览失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取配置概览失败',
                error: error.message
            });
        }
    }

    /**
     * 健康检查接口
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async healthCheck(req, res) {
        try {
            // 检查各个服务的健康状态
            const health = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                services: {
                    config: 'healthy',
                    telegram: 'healthy',
                    email: 'healthy',
                    tasks: 'healthy'
                }
            };

            // 尝试获取配置以验证服务状态
            try {
                await this.configService.getConfigStats();
            } catch (error) {
                health.status = 'unhealthy';
                health.services.config = 'unhealthy';
                health.error = error.message;
            }

            const statusCode = health.status === 'healthy' ? 200 : 503;
            res.status(statusCode).json({
                code: health.status === 'healthy' ? 1 : 0,
                data: health
            });
        } catch (error) {
            console.error('[页面控制器] 健康检查失败:', error);
            res.status(503).json({
                code: 0,
                msg: '健康检查失败',
                error: error.message
            });
        }
    }

    /**
     * 获取系统信息
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getSystemInfo(req, res) {
        try {
            const systemInfo = {
                version: process.env.npm_package_version || '1.0.0',
                nodeVersion: process.version,
                platform: process.platform,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            };

            res.json({
                code: 1,
                data: systemInfo
            });
        } catch (error) {
            console.error('[页面控制器] 获取系统信息失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取系统信息失败',
                error: error.message
            });
        }
    }

    /**
     * 创建路由处理器（包装异步错误处理）
     * @returns {Object} 路由处理器对象
     */
    createRouteHandlers() {
        return {
            renderNotificationPage: asyncErrorHandler(this.renderNotificationPage.bind(this)),
            getPageData: asyncErrorHandler(this.getPageData.bind(this)),
            getServers: asyncErrorHandler(this.getServers.bind(this)),
            getConfigOverview: asyncErrorHandler(this.getConfigOverview.bind(this)),
            healthCheck: asyncErrorHandler(this.healthCheck.bind(this)),
            getSystemInfo: asyncErrorHandler(this.getSystemInfo.bind(this))
        };
    }
}

module.exports = PageController;
