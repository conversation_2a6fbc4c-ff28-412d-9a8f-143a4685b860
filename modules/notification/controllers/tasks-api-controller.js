/**
 * 任务 API 控制器
 * 处理任务 API 端点
 */
"use strict";

const { asyncErrorHandler } = require('../middleware/error-middleware');
const { TasksValidator } = require('../validators');

class TasksApiController {
    constructor(tasksService, validator = TasksValidator) {
        this.tasksService = tasksService;
        this.validator = validator;
    }

    /**
     * 获取任务列表
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async listTasks(req, res) {
        try {
            // 验证查询参数
            const validationResult = this.validator.validateTaskQuery(req.query);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '查询参数验证失败',
                    errors: validationResult.errors
                });
            }

            const query = validationResult.data;
            let tasks;

            // 根据查询参数获取任务
            if (query.sid) {
                tasks = await this.tasksService.listByServer(query.sid, query.enabled);
            } else if (query.enabled === true) {
                tasks = await this.tasksService.listActive();
            } else {
                tasks = await this.tasksService.listAll();
            }

            // 过滤和排序
            if (query.type) {
                tasks = tasks.filter(task => task.type === query.type);
            }

            if (query.enabled !== undefined && query.sid === undefined) {
                tasks = tasks.filter(task => task.enabled === query.enabled);
            }

            // 排序
            tasks.sort((a, b) => {
                const aValue = a[query.sortBy];
                const bValue = b[query.sortBy];
                
                if (query.sortOrder === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            res.json({
                code: 1,
                data: tasks
            });
        } catch (error) {
            console.error('[任务 API] 获取任务列表失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取任务列表失败',
                error: error.message
            });
        }
    }

    /**
     * 获取单个任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getTask(req, res) {
        try {
            const { taskId } = req.params;
            
            // 验证任务ID
            const validationResult = this.validator.validateTaskId(taskId);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务ID验证失败',
                    errors: validationResult.errors
                });
            }

            const task = await this.tasksService.get(validationResult.data);
            
            if (!task) {
                return res.status(404).json({
                    code: 0,
                    msg: '任务不存在'
                });
            }

            res.json({
                code: 1,
                data: task
            });
        } catch (error) {
            console.error('[任务 API] 获取任务失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取任务失败',
                error: error.message
            });
        }
    }

    /**
     * 创建任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async createTask(req, res) {
        try {
            // 验证任务数据
            const validationResult = this.validator.validateTask(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务数据验证失败',
                    errors: validationResult.errors
                });
            }

            // 创建任务
            const taskId = await this.tasksService.create(validationResult.data);
            
            res.status(201).json({
                code: 1,
                msg: '任务创建成功',
                data: { id: taskId }
            });
        } catch (error) {
            console.error('[任务 API] 创建任务失败:', error);
            res.status(500).json({
                code: 0,
                msg: '创建任务失败',
                error: error.message
            });
        }
    }

    /**
     * 更新任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async updateTask(req, res) {
        try {
            const { taskId } = req.params;
            
            // 验证任务ID
            const taskIdValidation = this.validator.validateTaskId(taskId);
            if (!taskIdValidation.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务ID验证失败',
                    errors: taskIdValidation.errors
                });
            }

            // 验证更新数据
            const validationResult = this.validator.validateTask(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务数据验证失败',
                    errors: validationResult.errors
                });
            }

            // 更新任务
            await this.tasksService.update(taskIdValidation.data, validationResult.data);
            
            res.json({
                code: 1,
                msg: '任务更新成功'
            });
        } catch (error) {
            console.error('[任务 API] 更新任务失败:', error);
            res.status(500).json({
                code: 0,
                msg: '更新任务失败',
                error: error.message
            });
        }
    }

    /**
     * 删除任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async deleteTask(req, res) {
        try {
            const { taskId } = req.params;
            
            // 验证任务ID
            const validationResult = this.validator.validateTaskId(taskId);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务ID验证失败',
                    errors: validationResult.errors
                });
            }

            // 删除任务
            await this.tasksService.delete(validationResult.data);
            
            res.json({
                code: 1,
                msg: '任务删除成功'
            });
        } catch (error) {
            console.error('[任务 API] 删除任务失败:', error);
            res.status(500).json({
                code: 0,
                msg: '删除任务失败',
                error: error.message
            });
        }
    }

    /**
     * 切换任务状态
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async toggleTask(req, res) {
        try {
            // 验证切换数据
            const validationResult = this.validator.validateToggleTask(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '切换数据验证失败',
                    errors: validationResult.errors
                });
            }

            const { id, enabled } = validationResult.data;

            // 如果没有提供enabled状态，则获取当前任务状态并切换
            let targetEnabled = enabled;
            if (targetEnabled === undefined) {
                const currentTask = await this.tasksService.get(id);
                if (!currentTask) {
                    return res.status(404).json({
                        code: 0,
                        msg: '任务不存在'
                    });
                }
                targetEnabled = !currentTask.enabled;
            }

            // 切换任务状态
            await this.tasksService.toggle(id, targetEnabled);
            
            res.json({
                code: 1,
                msg: `任务已${targetEnabled ? '启用' : '禁用'}`,
                data: { enabled: targetEnabled }
            });
        } catch (error) {
            console.error('[任务 API] 切换任务状态失败:', error);
            res.status(500).json({
                code: 0,
                msg: '切换任务状态失败',
                error: error.message
            });
        }
    }

    /**
     * 获取任务统计
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getStats(req, res) {
        try {
            const stats = await this.tasksService.getStats();
            
            res.json({
                code: 1,
                data: stats
            });
        } catch (error) {
            console.error('[任务 API] 获取任务统计失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取任务统计失败',
                error: error.message
            });
        }
    }

    /**
     * 批量操作任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async batchOperation(req, res) {
        try {
            // 验证批量操作数据
            const validationResult = this.validator.validateBatchOperation(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '批量操作数据验证失败',
                    errors: validationResult.errors
                });
            }

            const { taskIds, operation } = validationResult.data;

            // 执行批量操作
            const results = await this.tasksService.batchOperation(taskIds, operation);
            
            res.json({
                code: 1,
                msg: '批量操作完成',
                data: results
            });
        } catch (error) {
            console.error('[任务 API] 批量操作失败:', error);
            res.status(500).json({
                code: 0,
                msg: '批量操作失败',
                error: error.message
            });
        }
    }

    /**
     * 复制任务
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async duplicateTask(req, res) {
        try {
            const { taskId } = req.params;
            
            // 验证任务ID
            const taskIdValidation = this.validator.validateTaskId(taskId);
            if (!taskIdValidation.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '任务ID验证失败',
                    errors: taskIdValidation.errors
                });
            }

            // 复制任务
            const newTaskId = await this.tasksService.duplicate(taskIdValidation.data, req.body);
            
            res.status(201).json({
                code: 1,
                msg: '任务复制成功',
                data: { id: newTaskId }
            });
        } catch (error) {
            console.error('[任务 API] 复制任务失败:', error);
            res.status(500).json({
                code: 0,
                msg: '复制任务失败',
                error: error.message
            });
        }
    }

    /**
     * 创建路由处理器（包装异步错误处理）
     * @returns {Object} 路由处理器对象
     */
    createRouteHandlers() {
        return {
            listTasks: asyncErrorHandler(this.listTasks.bind(this)),
            getTask: asyncErrorHandler(this.getTask.bind(this)),
            createTask: asyncErrorHandler(this.createTask.bind(this)),
            updateTask: asyncErrorHandler(this.updateTask.bind(this)),
            deleteTask: asyncErrorHandler(this.deleteTask.bind(this)),
            toggleTask: asyncErrorHandler(this.toggleTask.bind(this)),
            getStats: asyncErrorHandler(this.getStats.bind(this)),
            batchOperation: asyncErrorHandler(this.batchOperation.bind(this)),
            duplicateTask: asyncErrorHandler(this.duplicateTask.bind(this))
        };
    }
}

module.exports = TasksApiController;
