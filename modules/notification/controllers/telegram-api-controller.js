/**
 * Telegram API 控制器
 * 处理 Telegram API 端点
 */
"use strict";

const { asyncErrorHandler } = require('../middleware/error-middleware');
const { TelegramValidator } = require('../validators');

class TelegramApiController {
    constructor(telegramService, validator = TelegramValidator) {
        this.telegramService = telegramService;
        this.validator = validator;
    }

    /**
     * 获取 Telegram 配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getConfig(req, res) {
        try {
            const config = await this.telegramService.getConfig();
            
            res.json({
                code: 1,
                data: config
            });
        } catch (error) {
            console.error('[Telegram API] 获取配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取配置失败',
                error: error.message
            });
        }
    }

    /**
     * 保存 Telegram 配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async saveConfig(req, res) {
        try {
            // 验证配置数据
            const validationResult = this.validator.validateConfig(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '配置验证失败',
                    errors: validationResult.errors
                });
            }

            // 保存配置
            await this.telegramService.saveConfig(validationResult.data);
            
            res.json({
                code: 1,
                msg: '配置保存成功'
            });
        } catch (error) {
            console.error('[Telegram API] 保存配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '保存配置失败',
                error: error.message
            });
        }
    }

    /**
     * 发送测试通知
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async testNotification(req, res) {
        try {
            // 验证测试通知数据
            const validationResult = this.validator.validateTestNotification(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '测试数据验证失败',
                    errors: validationResult.errors
                });
            }

            // 发送测试通知
            const result = await this.telegramService.testNotification(
                validationResult.data.chatIds,
                validationResult.data.message
            );

            if (result.success) {
                res.json({
                    code: 1,
                    msg: result.message,
                    data: {
                        chatIds: result.chatIds
                    }
                });
            } else {
                res.status(400).json({
                    code: 0,
                    msg: result.error,
                    details: result.details
                });
            }
        } catch (error) {
            console.error('[Telegram API] 测试通知失败:', error);
            res.status(500).json({
                code: 0,
                msg: '测试通知失败',
                error: error.message
            });
        }
    }

    /**
     * 验证 Bot Token
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async validateToken(req, res) {
        try {
            const { token } = req.body;
            
            if (!token) {
                return res.status(400).json({
                    code: 0,
                    msg: 'Token 不能为空'
                });
            }

            const result = await this.telegramService.validateBotToken(token);
            
            res.json({
                code: result.valid ? 1 : 0,
                msg: result.valid ? 'Token 格式有效' : result.error,
                data: { valid: result.valid }
            });
        } catch (error) {
            console.error('[Telegram API] 验证 Token 失败:', error);
            res.status(500).json({
                code: 0,
                msg: '验证 Token 失败',
                error: error.message
            });
        }
    }

    /**
     * 验证 Chat ID
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async validateChatId(req, res) {
        try {
            const { chatId } = req.body;
            
            if (!chatId) {
                return res.status(400).json({
                    code: 0,
                    msg: 'Chat ID 不能为空'
                });
            }

            const result = await this.telegramService.validateChatId(chatId);
            
            res.json({
                code: result.valid ? 1 : 0,
                msg: result.valid ? 'Chat ID 格式有效' : result.error,
                data: { valid: result.valid }
            });
        } catch (error) {
            console.error('[Telegram API] 验证 Chat ID 失败:', error);
            res.status(500).json({
                code: 0,
                msg: '验证 Chat ID 失败',
                error: error.message
            });
        }
    }

    /**
     * 获取 Bot 信息
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getBotInfo(req, res) {
        try {
            const botInfo = await this.telegramService.getBotInfo();
            
            res.json({
                code: 1,
                data: botInfo
            });
        } catch (error) {
            console.error('[Telegram API] 获取 Bot 信息失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取 Bot 信息失败',
                error: error.message
            });
        }
    }

    /**
     * 获取通知统计
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getStats(req, res) {
        try {
            const stats = await this.telegramService.getNotificationStats();
            
            res.json({
                code: 1,
                data: stats
            });
        } catch (error) {
            console.error('[Telegram API] 获取通知统计失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取通知统计失败',
                error: error.message
            });
        }
    }

    /**
     * 更新通知类型配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async updateNotificationTypes(req, res) {
        try {
            // 验证通知类型配置
            const validationResult = this.validator.validateNotificationTypes(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '通知类型配置验证失败',
                    errors: validationResult.errors
                });
            }

            // 更新通知类型配置
            await this.telegramService.updateNotificationTypes(validationResult.data);
            
            res.json({
                code: 1,
                msg: '通知类型配置更新成功'
            });
        } catch (error) {
            console.error('[Telegram API] 更新通知类型配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '更新通知类型配置失败',
                error: error.message
            });
        }
    }

    /**
     * 批量发送通知
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async batchNotifications(req, res) {
        try {
            const { notifications } = req.body;
            
            if (!Array.isArray(notifications) || notifications.length === 0) {
                return res.status(400).json({
                    code: 0,
                    msg: '通知列表不能为空'
                });
            }

            // 验证每个通知
            for (const notification of notifications) {
                const validationResult = this.validator.validateTestNotification(notification);
                if (!validationResult.isValid) {
                    return res.status(400).json({
                        code: 0,
                        msg: '通知数据验证失败',
                        errors: validationResult.errors
                    });
                }
            }

            // 批量发送通知
            const results = await this.telegramService.sendBatchNotifications(notifications);
            
            res.json({
                code: 1,
                msg: '批量通知发送完成',
                data: results
            });
        } catch (error) {
            console.error('[Telegram API] 批量发送通知失败:', error);
            res.status(500).json({
                code: 0,
                msg: '批量发送通知失败',
                error: error.message
            });
        }
    }

    /**
     * 创建路由处理器（包装异步错误处理）
     * @returns {Object} 路由处理器对象
     */
    createRouteHandlers() {
        return {
            getConfig: asyncErrorHandler(this.getConfig.bind(this)),
            saveConfig: asyncErrorHandler(this.saveConfig.bind(this)),
            testNotification: asyncErrorHandler(this.testNotification.bind(this)),
            validateToken: asyncErrorHandler(this.validateToken.bind(this)),
            validateChatId: asyncErrorHandler(this.validateChatId.bind(this)),
            getBotInfo: asyncErrorHandler(this.getBotInfo.bind(this)),
            getStats: asyncErrorHandler(this.getStats.bind(this)),
            updateNotificationTypes: asyncErrorHandler(this.updateNotificationTypes.bind(this)),
            batchNotifications: asyncErrorHandler(this.batchNotifications.bind(this))
        };
    }
}

module.exports = TelegramApiController;
