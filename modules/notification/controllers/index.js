/**
 * 控制器模块入口文件
 * 统一导出所有控制器组件
 */
"use strict";

const PageController = require('./page-controller');
const TelegramApiController = require('./telegram-api-controller');
const EmailApiController = require('./email-api-controller');
const TasksApiController = require('./tasks-api-controller');

/**
 * 控制器工厂函数
 * 创建并初始化所有控制器实例
 * @param {Object} services - 服务实例集合
 * @param {Object} services.configService - 配置服务
 * @param {Object} services.telegramService - Telegram服务
 * @param {Object} services.emailService - 邮件服务
 * @param {Object} services.tasksService - 任务服务
 * @returns {Object} 控制器实例集合
 */
function createControllers(services) {
    if (!services) {
        throw new Error('服务实例集合是必需的');
    }

    const { configService, telegramService, emailService, tasksService } = services;

    if (!configService || !telegramService || !emailService || !tasksService) {
        throw new Error('所有服务实例都是必需的');
    }

    // 创建控制器实例
    const pageController = new PageController(configService);
    const telegramApiController = new TelegramApiController(telegramService);
    const emailApiController = new EmailApiController(emailService);
    const tasksApiController = new TasksApiController(tasksService);

    return {
        pageController,
        telegramApiController,
        emailApiController,
        tasksApiController
    };
}

module.exports = {
    // 控制器类
    PageController,
    TelegramApiController,
    EmailApiController,
    TasksApiController,
    
    // 控制器工厂函数
    createControllers,
    
    // 便捷访问
    controllers: {
        PageController,
        TelegramApiController,
        EmailApiController,
        TasksApiController
    }
};
