/**
 * 邮件 API 控制器
 * 处理邮件 API 端点
 */
"use strict";

const { asyncErrorHandler } = require('../middleware/error-middleware');
const { EmailValidator } = require('../validators');

class EmailApiController {
    constructor(emailService, validator = EmailValidator) {
        this.emailService = emailService;
        this.validator = validator;
    }

    /**
     * 获取邮件配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getConfig(req, res) {
        try {
            const config = await this.emailService.getConfig();
            
            res.json({
                code: 1,
                data: config
            });
        } catch (error) {
            console.error('[邮件 API] 获取配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取配置失败',
                error: error.message
            });
        }
    }

    /**
     * 保存邮件配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async saveConfig(req, res) {
        try {
            // 验证配置数据
            const validationResult = this.validator.validateConfig(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '配置验证失败',
                    errors: validationResult.errors
                });
            }

            // 保存配置
            await this.emailService.saveConfig(validationResult.data);
            
            res.json({
                code: 1,
                msg: '配置保存成功'
            });
        } catch (error) {
            console.error('[邮件 API] 保存配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '保存配置失败',
                error: error.message
            });
        }
    }

    /**
     * 发送测试邮件
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async testEmail(req, res) {
        try {
            // 验证测试邮件数据
            const validationResult = this.validator.validateTestEmail(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '测试数据验证失败',
                    errors: validationResult.errors
                });
            }

            // 发送测试邮件
            const result = await this.emailService.testEmail(
                validationResult.data.to,
                validationResult.data.subject,
                validationResult.data.content
            );

            if (result.success) {
                res.json({
                    code: 1,
                    msg: result.message,
                    data: {
                        to: result.to
                    }
                });
            } else {
                res.status(400).json({
                    code: 0,
                    msg: result.error
                });
            }
        } catch (error) {
            console.error('[邮件 API] 测试邮件失败:', error);
            res.status(500).json({
                code: 0,
                msg: '测试邮件失败',
                error: error.message
            });
        }
    }

    /**
     * 获取邮件日志
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getLogs(req, res) {
        try {
            // 验证查询参数
            const validationResult = this.validator.validateLogQuery(req.query);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '查询参数验证失败',
                    errors: validationResult.errors
                });
            }

            // 获取邮件日志
            const logs = await this.emailService.getLogs(
                validationResult.data.page,
                validationResult.data.pageSize
            );
            
            res.json({
                code: 1,
                data: logs
            });
        } catch (error) {
            console.error('[邮件 API] 获取邮件日志失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取邮件日志失败',
                error: error.message
            });
        }
    }

    /**
     * 清理邮件日志
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async cleanupLogs(req, res) {
        try {
            // 验证清理参数
            const validationResult = this.validator.validateLogCleanup(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '清理参数验证失败',
                    errors: validationResult.errors
                });
            }

            // 清理邮件日志
            await this.emailService.cleanupLogs(validationResult.data.days);
            
            res.json({
                code: 1,
                msg: '日志清理完成'
            });
        } catch (error) {
            console.error('[邮件 API] 清理邮件日志失败:', error);
            res.status(500).json({
                code: 0,
                msg: '清理邮件日志失败',
                error: error.message
            });
        }
    }

    /**
     * 验证 SMTP 连接
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async validateSmtp(req, res) {
        try {
            const result = await this.emailService.validateSmtpConnection(req.body);
            
            res.json({
                code: result.valid ? 1 : 0,
                msg: result.valid ? 'SMTP 配置有效' : result.error,
                data: { valid: result.valid }
            });
        } catch (error) {
            console.error('[邮件 API] 验证 SMTP 连接失败:', error);
            res.status(500).json({
                code: 0,
                msg: '验证 SMTP 连接失败',
                error: error.message
            });
        }
    }

    /**
     * 获取邮件统计
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getStats(req, res) {
        try {
            const stats = await this.emailService.getEmailStats();
            
            res.json({
                code: 1,
                data: stats
            });
        } catch (error) {
            console.error('[邮件 API] 获取邮件统计失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取邮件统计失败',
                error: error.message
            });
        }
    }

    /**
     * 更新通知类型配置
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async updateNotificationTypes(req, res) {
        try {
            // 验证通知类型配置
            const validationResult = this.validator.validateNotificationTypes(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    code: 0,
                    msg: '通知类型配置验证失败',
                    errors: validationResult.errors
                });
            }

            // 更新通知类型配置
            await this.emailService.updateNotificationTypes(validationResult.data);
            
            res.json({
                code: 1,
                msg: '通知类型配置更新成功'
            });
        } catch (error) {
            console.error('[邮件 API] 更新通知类型配置失败:', error);
            res.status(500).json({
                code: 0,
                msg: '更新通知类型配置失败',
                error: error.message
            });
        }
    }

    /**
     * 批量发送邮件
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async batchEmails(req, res) {
        try {
            const { emails } = req.body;
            
            if (!Array.isArray(emails) || emails.length === 0) {
                return res.status(400).json({
                    code: 0,
                    msg: '邮件列表不能为空'
                });
            }

            // 验证每个邮件
            for (const email of emails) {
                const validationResult = this.validator.validateTestEmail(email);
                if (!validationResult.isValid) {
                    return res.status(400).json({
                        code: 0,
                        msg: '邮件数据验证失败',
                        errors: validationResult.errors
                    });
                }
            }

            // 批量发送邮件
            const results = await this.emailService.sendBatchEmails(emails);
            
            res.json({
                code: 1,
                msg: '批量邮件发送完成',
                data: results
            });
        } catch (error) {
            console.error('[邮件 API] 批量发送邮件失败:', error);
            res.status(500).json({
                code: 0,
                msg: '批量发送邮件失败',
                error: error.message
            });
        }
    }

    /**
     * 获取邮件模板
     * @param {Object} req - Express请求对象
     * @param {Object} res - Express响应对象
     */
    async getTemplate(req, res) {
        try {
            const { type } = req.params;
            
            if (!type) {
                return res.status(400).json({
                    code: 0,
                    msg: '模板类型不能为空'
                });
            }

            const template = await this.emailService.getEmailTemplate(type);
            
            res.json({
                code: 1,
                data: template
            });
        } catch (error) {
            console.error('[邮件 API] 获取邮件模板失败:', error);
            res.status(500).json({
                code: 0,
                msg: '获取邮件模板失败',
                error: error.message
            });
        }
    }

    /**
     * 创建路由处理器（包装异步错误处理）
     * @returns {Object} 路由处理器对象
     */
    createRouteHandlers() {
        return {
            getConfig: asyncErrorHandler(this.getConfig.bind(this)),
            saveConfig: asyncErrorHandler(this.saveConfig.bind(this)),
            testEmail: asyncErrorHandler(this.testEmail.bind(this)),
            getLogs: asyncErrorHandler(this.getLogs.bind(this)),
            cleanupLogs: asyncErrorHandler(this.cleanupLogs.bind(this)),
            validateSmtp: asyncErrorHandler(this.validateSmtp.bind(this)),
            getStats: asyncErrorHandler(this.getStats.bind(this)),
            updateNotificationTypes: asyncErrorHandler(this.updateNotificationTypes.bind(this)),
            batchEmails: asyncErrorHandler(this.batchEmails.bind(this)),
            getTemplate: asyncErrorHandler(this.getTemplate.bind(this))
        };
    }
}

module.exports = EmailApiController;
