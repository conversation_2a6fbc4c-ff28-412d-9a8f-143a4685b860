/**
 * 配置服务
 * 聚合所有配置数据，提供统一的配置管理
 */
"use strict";

class ConfigService {
    constructor(db, telegramService, emailService, tasksService) {
        this.db = db;
        this.telegramService = telegramService;
        this.emailService = emailService;
        this.tasksService = tasksService;
        
        // 配置缓存
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.lastCacheTime = new Map();
        
        // 配置变更监听器
        this.changeListeners = new Set();
    }

    /**
     * 获取所有配置数据
     * @returns {Promise<Object>} 聚合的配置数据
     */
    async getAllConfigs() {
        try {
            const cacheKey = 'all_configs';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            // 并行获取所有配置
            const [telegramConfig, emailConfig, tasks, servers] = await Promise.all([
                this.telegramService.getConfig(),
                this.emailService.getConfig(),
                this.tasksService.listAll(),
                this.getServers()
            ]);

            const allConfigs = {
                telegram: telegramConfig,
                email: emailConfig,
                tasks: tasks,
                servers: servers,
                timestamp: new Date().toISOString()
            };

            this.setCache(cacheKey, allConfigs);
            return allConfigs;
        } catch (error) {
            console.error('[配置服务] 获取所有配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取 Telegram 配置
     * @returns {Promise<Object>} Telegram 配置
     */
    async getTelegramConfig() {
        try {
            const cacheKey = 'telegram_config';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            const config = await this.telegramService.getConfig();
            this.setCache(cacheKey, config);
            return config;
        } catch (error) {
            console.error('[配置服务] 获取 Telegram 配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取邮件配置
     * @returns {Promise<Object>} 邮件配置
     */
    async getEmailConfig() {
        try {
            const cacheKey = 'email_config';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            const config = await this.emailService.getConfig();
            this.setCache(cacheKey, config);
            return config;
        } catch (error) {
            console.error('[配置服务] 获取邮件配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取任务列表
     * @returns {Promise<Array>} 任务列表
     */
    async getTasks() {
        try {
            const cacheKey = 'tasks';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            const tasks = await this.tasksService.listAll();
            this.setCache(cacheKey, tasks);
            return tasks;
        } catch (error) {
            console.error('[配置服务] 获取任务列表失败:', error);
            throw error;
        }
    }

    /**
     * 获取服务器列表
     * @returns {Promise<Array>} 服务器列表
     */
    async getServers() {
        try {
            const cacheKey = 'servers';
            const cached = this.getFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            let servers = [];
            try {
                servers = await this.db.getServers();
            } catch (e) {
                console.warn('[配置服务] 读取服务器列表失败:', e.message);
            }

            this.setCache(cacheKey, servers);
            return servers;
        } catch (error) {
            console.error('[配置服务] 获取服务器列表失败:', error);
            throw error;
        }
    }

    /**
     * 更新 Telegram 配置
     * @param {Object} config - 新的配置
     * @returns {Promise<void>}
     */
    async updateTelegramConfig(config) {
        try {
            await this.telegramService.saveConfig(config);
            this.invalidateCache(['telegram_config', 'all_configs']);
            this.notifyConfigChange('telegram', config);
        } catch (error) {
            console.error('[配置服务] 更新 Telegram 配置失败:', error);
            throw error;
        }
    }

    /**
     * 更新邮件配置
     * @param {Object} config - 新的配置
     * @returns {Promise<void>}
     */
    async updateEmailConfig(config) {
        try {
            await this.emailService.saveConfig(config);
            this.invalidateCache(['email_config', 'all_configs']);
            this.notifyConfigChange('email', config);
        } catch (error) {
            console.error('[配置服务] 更新邮件配置失败:', error);
            throw error;
        }
    }

    /**
     * 创建任务
     * @param {Object} taskData - 任务数据
     * @returns {Promise<string>} 任务ID
     */
    async createTask(taskData) {
        try {
            const taskId = await this.tasksService.create(taskData);
            this.invalidateCache(['tasks', 'all_configs']);
            this.notifyConfigChange('tasks', { action: 'create', taskId, data: taskData });
            return taskId;
        } catch (error) {
            console.error('[配置服务] 创建任务失败:', error);
            throw error;
        }
    }

    /**
     * 删除任务
     * @param {string} taskId - 任务ID
     * @returns {Promise<void>}
     */
    async deleteTask(taskId) {
        try {
            await this.tasksService.delete(taskId);
            this.invalidateCache(['tasks', 'all_configs']);
            this.notifyConfigChange('tasks', { action: 'delete', taskId });
        } catch (error) {
            console.error('[配置服务] 删除任务失败:', error);
            throw error;
        }
    }

    /**
     * 切换任务状态
     * @param {string} taskId - 任务ID
     * @param {boolean} enabled - 启用状态
     * @returns {Promise<void>}
     */
    async toggleTask(taskId, enabled) {
        try {
            await this.tasksService.update(taskId, { enabled });
            this.invalidateCache(['tasks', 'all_configs']);
            this.notifyConfigChange('tasks', { action: 'toggle', taskId, enabled });
        } catch (error) {
            console.error('[配置服务] 切换任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 从缓存获取数据
     * @param {string} key - 缓存键
     * @returns {*} 缓存的数据或null
     */
    getFromCache(key) {
        const lastTime = this.lastCacheTime.get(key);
        if (!lastTime || Date.now() - lastTime > this.cacheTimeout) {
            this.cache.delete(key);
            this.lastCacheTime.delete(key);
            return null;
        }
        return this.cache.get(key);
    }

    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     */
    setCache(key, value) {
        this.cache.set(key, value);
        this.lastCacheTime.set(key, Date.now());
    }

    /**
     * 使缓存失效
     * @param {Array<string>} keys - 要失效的缓存键列表
     */
    invalidateCache(keys) {
        for (const key of keys) {
            this.cache.delete(key);
            this.lastCacheTime.delete(key);
        }
    }

    /**
     * 清空所有缓存
     */
    clearCache() {
        this.cache.clear();
        this.lastCacheTime.clear();
    }

    /**
     * 添加配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addChangeListener(listener) {
        this.changeListeners.add(listener);
    }

    /**
     * 移除配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeChangeListener(listener) {
        this.changeListeners.delete(listener);
    }

    /**
     * 通知配置变更
     * @param {string} type - 配置类型
     * @param {*} data - 变更数据
     */
    notifyConfigChange(type, data) {
        for (const listener of this.changeListeners) {
            try {
                listener(type, data);
            } catch (error) {
                console.error('[配置服务] 配置变更监听器执行失败:', error);
            }
        }
    }

    /**
     * 获取配置统计信息
     * @returns {Promise<Object>} 配置统计
     */
    async getConfigStats() {
        try {
            const [telegramConfig, emailConfig, tasks] = await Promise.all([
                this.getTelegramConfig(),
                this.getEmailConfig(),
                this.getTasks()
            ]);

            return {
                telegram: {
                    enabled: telegramConfig.enabled || false,
                    configured: !!(telegramConfig.token && telegramConfig.chatIds?.length > 0)
                },
                email: {
                    enabled: emailConfig.enabled || false,
                    configured: !!(emailConfig.from_address && emailConfig.to_addresses?.length > 0)
                },
                tasks: {
                    total: tasks.length,
                    enabled: tasks.filter(t => t.enabled).length,
                    disabled: tasks.filter(t => !t.enabled).length
                }
            };
        } catch (error) {
            console.error('[配置服务] 获取配置统计失败:', error);
            throw error;
        }
    }
}

module.exports = ConfigService;
