/**
 * 任务服务
 * 处理自定义通知任务业务逻辑
 */
"use strict";

class TasksService {
    constructor(db) {
        this.db = db;
    }

    /**
     * 获取所有任务
     * @returns {Promise<Array>} 任务列表
     */
    async listAll() {
        try {
            const tasks = await this.db.notificationTasks.listAll();
            return tasks;
        } catch (error) {
            console.error('[任务服务] 获取任务列表失败:', error);
            throw error;
        }
    }

    /**
     * 根据服务器ID获取任务
     * @param {string} serverId - 服务器ID
     * @param {boolean} activeOnly - 是否只获取启用的任务
     * @returns {Promise<Array>} 任务列表
     */
    async listByServer(serverId, activeOnly = false) {
        try {
            if (activeOnly) {
                return await this.db.notificationTasks.listActiveByServer(serverId);
            } else {
                const allTasks = await this.listAll();
                return allTasks.filter(task => task.sid === serverId);
            }
        } catch (error) {
            console.error('[任务服务] 根据服务器获取任务失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有启用的任务
     * @returns {Promise<Array>} 启用的任务列表
     */
    async listActive() {
        try {
            return await this.db.notificationTasks.listActiveAll();
        } catch (error) {
            console.error('[任务服务] 获取启用任务失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取任务
     * @param {string} taskId - 任务ID
     * @returns {Promise<Object|null>} 任务对象
     */
    async get(taskId) {
        try {
            return await this.db.notificationTasks.get(taskId);
        } catch (error) {
            console.error('[任务服务] 获取任务失败:', error);
            throw error;
        }
    }

    /**
     * 创建任务
     * @param {Object} taskData - 任务数据
     * @returns {Promise<string>} 任务ID
     */
    async create(taskData) {
        try {
            // 验证服务器是否存在
            const serverValid = await this.validateServer(taskData.sid);
            if (!serverValid) {
                throw new Error(`服务器 ${taskData.sid} 不存在或无效`);
            }

            // 创建任务
            const taskId = await this.db.notificationTasks.create(taskData);
            console.log(`[任务服务] 任务创建成功: ${taskId}`);
            return taskId;
        } catch (error) {
            console.error('[任务服务] 创建任务失败:', error);
            throw error;
        }
    }

    /**
     * 更新任务
     * @param {string} taskId - 任务ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<void>}
     */
    async update(taskId, updateData) {
        try {
            // 检查任务是否存在
            const existingTask = await this.get(taskId);
            if (!existingTask) {
                throw new Error(`任务 ${taskId} 不存在`);
            }

            // 如果更新了服务器ID，验证新的服务器
            if (updateData.sid && updateData.sid !== existingTask.sid) {
                const serverValid = await this.validateServer(updateData.sid);
                if (!serverValid) {
                    throw new Error(`服务器 ${updateData.sid} 不存在或无效`);
                }
            }

            // 更新任务
            await this.db.notificationTasks.update(taskId, updateData);
            console.log(`[任务服务] 任务更新成功: ${taskId}`);
        } catch (error) {
            console.error('[任务服务] 更新任务失败:', error);
            throw error;
        }
    }

    /**
     * 删除任务
     * @param {string} taskId - 任务ID
     * @returns {Promise<void>}
     */
    async delete(taskId) {
        try {
            // 检查任务是否存在
            const existingTask = await this.get(taskId);
            if (!existingTask) {
                throw new Error(`任务 ${taskId} 不存在`);
            }

            // 删除任务
            await this.db.notificationTasks.delete(taskId);
            console.log(`[任务服务] 任务删除成功: ${taskId}`);
        } catch (error) {
            console.error('[任务服务] 删除任务失败:', error);
            throw error;
        }
    }

    /**
     * 切换任务状态
     * @param {string} taskId - 任务ID
     * @param {boolean} enabled - 启用状态
     * @returns {Promise<void>}
     */
    async toggle(taskId, enabled) {
        try {
            await this.update(taskId, { enabled });
            console.log(`[任务服务] 任务状态切换成功: ${taskId} -> ${enabled ? '启用' : '禁用'}`);
        } catch (error) {
            console.error('[任务服务] 切换任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 验证服务器是否存在
     * @param {string} serverId - 服务器ID
     * @returns {Promise<boolean>} 是否有效
     */
    async validateServer(serverId) {
        try {
            if (!serverId) {
                return false;
            }

            // 从数据库获取服务器列表
            const servers = await this.db.getServers();
            return servers.some(server => server.sid === serverId);
        } catch (error) {
            console.error('[任务服务] 验证服务器失败:', error);
            return false;
        }
    }

    /**
     * 获取任务统计
     * @returns {Promise<Object>} 任务统计
     */
    async getStats() {
        try {
            const allTasks = await this.listAll();
            const activeTasks = allTasks.filter(task => task.enabled);
            
            // 按服务器分组统计
            const serverStats = {};
            for (const task of allTasks) {
                if (!serverStats[task.sid]) {
                    serverStats[task.sid] = { total: 0, enabled: 0 };
                }
                serverStats[task.sid].total++;
                if (task.enabled) {
                    serverStats[task.sid].enabled++;
                }
            }

            // 按周期分组统计
            const periodStats = {};
            for (const task of allTasks) {
                const period = task.period || 'monthly';
                if (!periodStats[period]) {
                    periodStats[period] = { total: 0, enabled: 0 };
                }
                periodStats[period].total++;
                if (task.enabled) {
                    periodStats[period].enabled++;
                }
            }

            return {
                total: allTasks.length,
                enabled: activeTasks.length,
                disabled: allTasks.length - activeTasks.length,
                serverStats,
                periodStats,
                serverCount: Object.keys(serverStats).length
            };
        } catch (error) {
            console.error('[任务服务] 获取任务统计失败:', error);
            throw error;
        }
    }

    /**
     * 批量操作任务
     * @param {Array} taskIds - 任务ID列表
     * @param {string} operation - 操作类型 (enable, disable, delete)
     * @returns {Promise<Object>} 操作结果
     */
    async batchOperation(taskIds, operation) {
        try {
            if (!Array.isArray(taskIds) || taskIds.length === 0) {
                throw new Error('任务ID列表不能为空');
            }

            const validOperations = ['enable', 'disable', 'delete'];
            if (!validOperations.includes(operation)) {
                throw new Error(`无效的操作类型: ${operation}`);
            }

            const results = {
                success: [],
                failed: [],
                total: taskIds.length
            };

            for (const taskId of taskIds) {
                try {
                    switch (operation) {
                        case 'enable':
                            await this.toggle(taskId, true);
                            break;
                        case 'disable':
                            await this.toggle(taskId, false);
                            break;
                        case 'delete':
                            await this.delete(taskId);
                            break;
                    }
                    results.success.push(taskId);
                } catch (error) {
                    results.failed.push({ taskId, error: error.message });
                }
            }

            console.log(`[任务服务] 批量操作完成: ${operation}, 成功: ${results.success.length}, 失败: ${results.failed.length}`);
            return results;
        } catch (error) {
            console.error('[任务服务] 批量操作失败:', error);
            throw error;
        }
    }

    /**
     * 标记任务已通知
     * @param {string} taskId - 任务ID
     * @param {number} threshold - 触发阈值
     * @returns {Promise<void>}
     */
    async markNotified(taskId, threshold) {
        try {
            await this.db.notificationTasks.markNotified(taskId, threshold);
            console.log(`[任务服务] 任务通知标记成功: ${taskId}, 阈值: ${threshold}`);
        } catch (error) {
            console.error('[任务服务] 标记任务通知失败:', error);
            throw error;
        }
    }

    /**
     * 确保任务周期边界
     * @param {Object} task - 任务对象
     * @returns {Promise<Object>} 更新后的任务对象
     */
    async ensurePeriodBoundary(task) {
        try {
            return await this.db.notificationTasks.ensurePeriodBoundary(task);
        } catch (error) {
            console.error('[任务服务] 确保周期边界失败:', error);
            throw error;
        }
    }

    /**
     * 获取需要评估的任务
     * @param {string} serverId - 服务器ID（可选）
     * @returns {Promise<Array>} 需要评估的任务列表
     */
    async getTasksForEvaluation(serverId = null) {
        try {
            if (serverId) {
                return await this.listByServer(serverId, true);
            } else {
                return await this.listActive();
            }
        } catch (error) {
            console.error('[任务服务] 获取评估任务失败:', error);
            throw error;
        }
    }

    /**
     * 复制任务
     * @param {string} taskId - 源任务ID
     * @param {Object} overrides - 覆盖的字段
     * @returns {Promise<string>} 新任务ID
     */
    async duplicate(taskId, overrides = {}) {
        try {
            const sourceTask = await this.get(taskId);
            if (!sourceTask) {
                throw new Error(`源任务 ${taskId} 不存在`);
            }

            // 复制任务数据，排除ID和时间戳字段
            const { id, created_at, updated_at, last_notified, last_period_start, ...taskData } = sourceTask;
            
            // 应用覆盖字段
            const newTaskData = {
                ...taskData,
                ...overrides,
                enabled: overrides.enabled !== undefined ? overrides.enabled : false // 默认禁用复制的任务
            };

            const newTaskId = await this.create(newTaskData);
            console.log(`[任务服务] 任务复制成功: ${taskId} -> ${newTaskId}`);
            return newTaskId;
        } catch (error) {
            console.error('[任务服务] 复制任务失败:', error);
            throw error;
        }
    }
}

module.exports = TasksService;
