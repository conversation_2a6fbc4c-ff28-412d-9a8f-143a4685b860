/**
 * 邮件服务
 * 处理邮件相关业务逻辑
 */
"use strict";

class EmailService {
    constructor(db, notificationManager) {
        this.db = db;
        this.notificationManager = notificationManager;
    }

    /**
     * 获取邮件配置
     * @returns {Promise<Object>} 邮件配置
     */
    async getConfig() {
        try {
            const config = await this.db.emailConfig.get();
            
            // 返回默认配置结构
            const defaultConfig = {
                enabled: false,
                host: '',
                port: 587,
                secure: false,
                auth_user: '',
                auth_pass: '',
                from_address: '',
                from_name: '',
                to_addresses: [],
                cc_addresses: [],
                bcc_addresses: [],
                notification_types: {
                    serverOnline: true,
                    serverOffline: true,
                    trafficLimit: true,
                    testNotification: true,
                    statusSummary: true
                }
            };

            if (!config) {
                return defaultConfig;
            }

            // 合并配置，确保所有字段都存在
            return {
                ...defaultConfig,
                ...config,
                notification_types: {
                    ...defaultConfig.notification_types,
                    ...(config.notification_types || {})
                }
            };
        } catch (error) {
            console.error('[邮件服务] 获取配置失败:', error);
            throw error;
        }
    }

    /**
     * 保存邮件配置
     * @param {Object} config - 配置对象
     * @returns {Promise<void>}
     */
    async saveConfig(config) {
        try {
            // 保存到数据库
            await this.db.emailConfig.set(config);
            
            // 如果启用了邮件且配置有效，重新初始化邮件提供者
            if (config.enabled && this.isConfigValid(config)) {
                await this.reinitializeEmailProvider(config);
            }
            
            console.log('[邮件服务] 配置保存成功');
        } catch (error) {
            console.error('[邮件服务] 保存配置失败:', error);
            throw error;
        }
    }

    /**
     * 验证配置是否有效
     * @param {Object} config - 邮件配置
     * @returns {boolean} 是否有效
     */
    isConfigValid(config) {
        return !!(
            config.host &&
            config.port &&
            config.auth_user &&
            config.auth_pass &&
            config.from_address &&
            (config.to_addresses?.length > 0 || config.cc_addresses?.length > 0 || config.bcc_addresses?.length > 0)
        );
    }

    /**
     * 重新初始化邮件提供者
     * @param {Object} config - 邮件配置
     * @returns {Promise<void>}
     */
    async reinitializeEmailProvider(config) {
        try {
            if (!this.notificationManager) {
                console.warn('[邮件服务] 通知管理器未初始化，跳过邮件提供者重新初始化');
                return;
            }

            // 通知管理器重新初始化邮件提供者
            if (typeof this.notificationManager.reinitializeEmailProvider === 'function') {
                await this.notificationManager.reinitializeEmailProvider(config);
                console.log('[邮件服务] 邮件提供者重新初始化成功');
            } else {
                console.warn('[邮件服务] 通知管理器不支持邮件提供者重新初始化');
            }
        } catch (error) {
            console.error('[邮件服务] 邮件提供者重新初始化失败:', error);
            throw error;
        }
    }

    /**
     * 发送测试邮件
     * @param {Array} to - 收件人列表（可选）
     * @param {string} subject - 邮件主题（可选）
     * @param {string} content - 邮件内容（可选）
     * @returns {Promise<Object>} 发送结果
     */
    async testEmail(to = null, subject = null, content = null) {
        try {
            if (!this.notificationManager || !this.notificationManager.emailProvider) {
                throw new Error('邮件通道未初始化');
            }

            // 获取当前配置
            const config = await this.getConfig();
            
            if (!config.enabled) {
                throw new Error('邮件通知未启用');
            }

            if (!this.isConfigValid(config)) {
                throw new Error('邮件配置不完整');
            }

            // 构建测试邮件数据
            const emailData = {
                type: 'test',
                title: subject || '测试通知',
                content: content || '这是一封测试邮件，如果您收到这封邮件，说明邮件通知系统工作正常。',
                meta: { 
                    site: this.notificationManager.siteConfig || {},
                    to: to || config.to_addresses
                }
            };

            // 发送测试邮件
            console.log('[邮件服务] 发送测试邮件...');
            const result = await this.notificationManager.emailProvider.send(emailData);

            if (result.ok) {
                console.log('[邮件服务] 测试邮件发送成功');
                return { 
                    success: true, 
                    message: '测试邮件已发送',
                    to: emailData.meta.to
                };
            } else {
                console.error('[邮件服务] 测试邮件发送失败:', result.reason);
                return { 
                    success: false, 
                    error: result.reason || '发送失败'
                };
            }
        } catch (error) {
            console.error('[邮件服务] 测试邮件发送异常:', error);
            return { 
                success: false, 
                error: error.message 
            };
        }
    }

    /**
     * 获取邮件日志
     * @param {number} page - 页码
     * @param {number} pageSize - 页面大小
     * @returns {Promise<Object>} 日志列表
     */
    async getLogs(page = 1, pageSize = 20) {
        try {
            const logs = await this.db.emailLogs.list(page, pageSize);
            return logs;
        } catch (error) {
            console.error('[邮件服务] 获取邮件日志失败:', error);
            throw error;
        }
    }

    /**
     * 清理邮件日志
     * @param {number} days - 保留天数
     * @returns {Promise<void>}
     */
    async cleanupLogs(days = 30) {
        try {
            await this.db.emailLogs.cleanup(days);
            console.log(`[邮件服务] 邮件日志清理完成，保留最近 ${days} 天的记录`);
        } catch (error) {
            console.error('[邮件服务] 清理邮件日志失败:', error);
            throw error;
        }
    }

    /**
     * 验证邮箱地址格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmailFormat(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证 SMTP 连接
     * @param {Object} config - SMTP 配置
     * @returns {Promise<Object>} 验证结果
     */
    async validateSmtpConnection(config) {
        try {
            // 这里可以实现实际的 SMTP 连接测试
            // 暂时只做基本验证
            if (!config.host || !config.port || !config.auth_user || !config.auth_pass) {
                return { valid: false, error: 'SMTP 配置不完整' };
            }

            if (!this.validateEmailFormat(config.from_address)) {
                return { valid: false, error: '发件人邮箱格式不正确' };
            }

            return { valid: true };
        } catch (error) {
            console.error('[邮件服务] SMTP 连接验证失败:', error);
            return { valid: false, error: error.message };
        }
    }

    /**
     * 获取邮件统计
     * @returns {Promise<Object>} 邮件统计
     */
    async getEmailStats() {
        try {
            const config = await this.getConfig();
            
            // 获取最近的日志统计
            let recentLogs = [];
            try {
                const logsResult = await this.getLogs(1, 10);
                recentLogs = logsResult.list || [];
            } catch (e) {
                console.warn('[邮件服务] 获取最近日志失败:', e.message);
            }

            return {
                enabled: config.enabled,
                configured: this.isConfigValid(config),
                toCount: config.to_addresses?.length || 0,
                ccCount: config.cc_addresses?.length || 0,
                bccCount: config.bcc_addresses?.length || 0,
                enabledNotificationTypes: Object.values(config.notification_types || {}).filter(Boolean).length,
                totalNotificationTypes: Object.keys(config.notification_types || {}).length,
                recentLogsCount: recentLogs.length
            };
        } catch (error) {
            console.error('[邮件服务] 获取邮件统计失败:', error);
            throw error;
        }
    }

    /**
     * 更新通知类型配置
     * @param {Object} notificationTypes - 通知类型配置
     * @returns {Promise<void>}
     */
    async updateNotificationTypes(notificationTypes) {
        try {
            const config = await this.getConfig();
            config.notification_types = {
                ...config.notification_types,
                ...notificationTypes
            };
            
            await this.saveConfig(config);
            console.log('[邮件服务] 通知类型配置更新成功');
        } catch (error) {
            console.error('[邮件服务] 更新通知类型配置失败:', error);
            throw error;
        }
    }

    /**
     * 批量发送邮件
     * @param {Array} emails - 邮件列表
     * @returns {Promise<Array>} 发送结果列表
     */
    async sendBatchEmails(emails) {
        try {
            if (!Array.isArray(emails) || emails.length === 0) {
                return [];
            }

            const results = [];
            for (const email of emails) {
                try {
                    const result = await this.testEmail(
                        email.to,
                        email.subject,
                        email.content
                    );
                    results.push({
                        ...email,
                        result
                    });
                } catch (error) {
                    results.push({
                        ...email,
                        result: { success: false, error: error.message }
                    });
                }
            }

            return results;
        } catch (error) {
            console.error('[邮件服务] 批量发送邮件失败:', error);
            throw error;
        }
    }

    /**
     * 获取邮件模板
     * @param {string} type - 模板类型
     * @returns {Promise<Object>} 邮件模板
     */
    async getEmailTemplate(type) {
        try {
            // 这里可以从数据库或文件系统获取邮件模板
            // 暂时返回基本模板
            const templates = {
                test: {
                    subject: '测试通知',
                    content: '这是一封测试邮件，如果您收到这封邮件，说明邮件通知系统工作正常。'
                },
                serverOffline: {
                    subject: '服务器离线通知',
                    content: '服务器 {{serverName}} 已离线，请及时检查。'
                },
                serverOnline: {
                    subject: '服务器恢复通知',
                    content: '服务器 {{serverName}} 已恢复在线。'
                },
                trafficLimit: {
                    subject: '流量超限通知',
                    content: '服务器 {{serverName}} 流量使用已达到 {{percentage}}%。'
                }
            };

            return templates[type] || templates.test;
        } catch (error) {
            console.error('[邮件服务] 获取邮件模板失败:', error);
            throw error;
        }
    }
}

module.exports = EmailService;
