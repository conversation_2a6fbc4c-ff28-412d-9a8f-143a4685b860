/**
 * Telegram 服务
 * 处理 Telegram 相关业务逻辑
 */
"use strict";

class TelegramService {
    constructor(db, notificationManager) {
        this.db = db;
        this.notificationManager = notificationManager;
    }

    /**
     * 获取 Telegram 配置
     * @returns {Promise<Object>} Telegram 配置
     */
    async getConfig() {
        try {
            const setting = await this.db.setting.get('telegram');
            
            // 返回默认配置结构
            const defaultConfig = {
                enabled: false,
                token: '',
                chatIds: [],
                webhook: false,
                webhookPort: 3001,
                baseApiUrl: '',
                notificationTypes: {
                    serverOnline: true,
                    serverOffline: true,
                    trafficLimit: true,
                    testNotification: true,
                    statusSummary: true,
                    newServerDiscovered: true,
                    serverApproved: true
                },
                offlineNotificationDelay: 300,
                trafficThresholds: [80, 90, 95]
            };

            if (!setting) {
                return defaultConfig;
            }

            // 合并配置，确保所有字段都存在
            return {
                ...defaultConfig,
                ...setting,
                notificationTypes: {
                    ...defaultConfig.notificationTypes,
                    ...(setting.notificationTypes || {})
                }
            };
        } catch (error) {
            console.error('[Telegram服务] 获取配置失败:', error);
            throw error;
        }
    }

    /**
     * 保存 Telegram 配置
     * @param {Object} config - 配置对象
     * @returns {Promise<void>}
     */
    async saveConfig(config) {
        try {
            // 保存到数据库
            await this.db.setting.set('telegram', config);
            
            // 如果启用了 Telegram 且配置有效，重新初始化 Bot
            if (config.enabled && config.token && config.chatIds?.length > 0) {
                await this.reinitializeBot(config);
            }
            
            console.log('[Telegram服务] 配置保存成功');
        } catch (error) {
            console.error('[Telegram服务] 保存配置失败:', error);
            throw error;
        }
    }

    /**
     * 重新初始化 Bot
     * @param {Object} config - Telegram 配置
     * @returns {Promise<void>}
     */
    async reinitializeBot(config) {
        try {
            if (!this.notificationManager) {
                console.warn('[Telegram服务] 通知管理器未初始化，跳过 Bot 重新初始化');
                return;
            }

            // 通知管理器重新初始化 Bot
            if (typeof this.notificationManager.reinitializeBot === 'function') {
                await this.notificationManager.reinitializeBot(config);
                console.log('[Telegram服务] Bot 重新初始化成功');
            } else {
                console.warn('[Telegram服务] 通知管理器不支持 Bot 重新初始化');
            }
        } catch (error) {
            console.error('[Telegram服务] Bot 重新初始化失败:', error);
            throw error;
        }
    }

    /**
     * 发送测试通知
     * @param {Array} chatIds - Chat ID 列表（可选，不提供则使用配置中的默认值）
     * @param {string} message - 自定义消息（可选）
     * @returns {Promise<Object>} 发送结果
     */
    async testNotification(chatIds = null, message = null) {
        try {
            if (!this.notificationManager) {
                throw new Error('通知管理器未初始化');
            }

            // 获取当前配置
            const config = await this.getConfig();
            
            if (!config.enabled) {
                throw new Error('Telegram 通知未启用');
            }

            if (!config.token) {
                throw new Error('未配置 Telegram Bot Token');
            }

            // 使用提供的 chatIds 或配置中的默认值
            const targetChatIds = chatIds || config.chatIds;
            if (!targetChatIds || targetChatIds.length === 0) {
                throw new Error('未配置 Chat ID');
            }

            // 使用自定义消息或默认测试消息
            const testMessage = message || '这是一条测试通知，如果您收到这条消息，说明通知系统工作正常。';

            // 发送测试通知
            console.log('[Telegram服务] 发送测试通知...');
            const result = await this.notificationManager.sendNotification(
                '测试通知',
                testMessage,
                targetChatIds,
                { priority: 'normal' }
            );

            if (result.success) {
                console.log('[Telegram服务] 测试通知发送成功');
                return { 
                    success: true, 
                    message: '测试通知已发送',
                    chatIds: targetChatIds
                };
            } else {
                console.error('[Telegram服务] 测试通知发送失败:', result.error);
                return { 
                    success: false, 
                    error: result.error,
                    details: result.details
                };
            }
        } catch (error) {
            console.error('[Telegram服务] 测试通知发送异常:', error);
            return { 
                success: false, 
                error: error.message 
            };
        }
    }

    /**
     * 验证 Bot Token
     * @param {string} token - Bot Token
     * @returns {Promise<Object>} 验证结果
     */
    async validateBotToken(token) {
        try {
            if (!token) {
                return { valid: false, error: 'Token 不能为空' };
            }

            // 基本格式验证
            const tokenRegex = /^bot\d+:[A-Za-z0-9_-]+$/;
            if (!tokenRegex.test(token)) {
                return { valid: false, error: 'Token 格式不正确' };
            }

            // 这里可以添加更多验证逻辑，比如调用 Telegram API 验证 Token 有效性
            // 暂时只做格式验证
            return { valid: true };
        } catch (error) {
            console.error('[Telegram服务] Token 验证失败:', error);
            return { valid: false, error: error.message };
        }
    }

    /**
     * 验证 Chat ID
     * @param {string} chatId - Chat ID
     * @returns {Promise<Object>} 验证结果
     */
    async validateChatId(chatId) {
        try {
            if (!chatId) {
                return { valid: false, error: 'Chat ID 不能为空' };
            }

            // Chat ID 可以是数字或以@开头的用户名
            const chatIdRegex = /^(-?\d+|@[a-zA-Z0-9_]+)$/;
            if (!chatIdRegex.test(chatId)) {
                return { valid: false, error: 'Chat ID 格式不正确' };
            }

            return { valid: true };
        } catch (error) {
            console.error('[Telegram服务] Chat ID 验证失败:', error);
            return { valid: false, error: error.message };
        }
    }

    /**
     * 获取 Bot 信息
     * @returns {Promise<Object>} Bot 信息
     */
    async getBotInfo() {
        try {
            const config = await this.getConfig();
            
            if (!config.enabled || !config.token) {
                return { 
                    configured: false, 
                    message: 'Telegram Bot 未配置' 
                };
            }

            // 这里可以调用 Telegram API 获取 Bot 详细信息
            // 暂时返回基本信息
            return {
                configured: true,
                enabled: config.enabled,
                chatIds: config.chatIds,
                webhook: config.webhook,
                notificationTypes: config.notificationTypes
            };
        } catch (error) {
            console.error('[Telegram服务] 获取 Bot 信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取通知统计
     * @returns {Promise<Object>} 通知统计
     */
    async getNotificationStats() {
        try {
            // 这里可以从数据库或日志中获取通知统计信息
            // 暂时返回基本统计
            const config = await this.getConfig();
            
            return {
                enabled: config.enabled,
                configured: !!(config.token && config.chatIds?.length > 0),
                chatCount: config.chatIds?.length || 0,
                enabledNotificationTypes: Object.values(config.notificationTypes || {}).filter(Boolean).length,
                totalNotificationTypes: Object.keys(config.notificationTypes || {}).length
            };
        } catch (error) {
            console.error('[Telegram服务] 获取通知统计失败:', error);
            throw error;
        }
    }

    /**
     * 更新通知类型配置
     * @param {Object} notificationTypes - 通知类型配置
     * @returns {Promise<void>}
     */
    async updateNotificationTypes(notificationTypes) {
        try {
            const config = await this.getConfig();
            config.notificationTypes = {
                ...config.notificationTypes,
                ...notificationTypes
            };
            
            await this.saveConfig(config);
            console.log('[Telegram服务] 通知类型配置更新成功');
        } catch (error) {
            console.error('[Telegram服务] 更新通知类型配置失败:', error);
            throw error;
        }
    }

    /**
     * 批量发送通知
     * @param {Array} notifications - 通知列表
     * @returns {Promise<Array>} 发送结果列表
     */
    async sendBatchNotifications(notifications) {
        try {
            if (!Array.isArray(notifications) || notifications.length === 0) {
                return [];
            }

            const results = [];
            for (const notification of notifications) {
                try {
                    const result = await this.testNotification(
                        notification.chatIds,
                        notification.message
                    );
                    results.push({
                        ...notification,
                        result
                    });
                } catch (error) {
                    results.push({
                        ...notification,
                        result: { success: false, error: error.message }
                    });
                }
            }

            return results;
        } catch (error) {
            console.error('[Telegram服务] 批量发送通知失败:', error);
            throw error;
        }
    }
}

module.exports = TelegramService;
