/**
 * 服务层模块入口文件
 * 统一导出所有服务组件
 */
"use strict";

const ConfigService = require('./config-service');
const TelegramService = require('./telegram-service');
const EmailService = require('./email-service');
const TasksService = require('./tasks-service');

/**
 * 服务工厂函数
 * 创建并初始化所有服务实例
 * @param {Object} dependencies - 依赖对象
 * @param {Object} dependencies.db - 数据库实例
 * @param {Object} dependencies.notificationManager - 通知管理器实例
 * @returns {Object} 服务实例集合
 */
function createServices({ db, notificationManager }) {
    if (!db) {
        throw new Error('数据库实例是必需的');
    }

    // 创建基础服务实例
    const telegramService = new TelegramService(db, notificationManager);
    const emailService = new EmailService(db, notificationManager);
    const tasksService = new TasksService(db);
    
    // 创建配置服务实例（依赖其他服务）
    const configService = new ConfigService(db, telegramService, emailService, tasksService);

    return {
        configService,
        telegramService,
        emailService,
        tasksService
    };
}

module.exports = {
    // 服务类
    ConfigService,
    TelegramService,
    EmailService,
    TasksService,
    
    // 服务工厂函数
    createServices,
    
    // 便捷访问
    services: {
        ConfigService,
        TelegramService,
        EmailService,
        TasksService
    }
};
