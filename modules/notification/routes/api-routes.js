/**
 * 通知设置 API 路由
 * 处理所有 API 端点
 */
"use strict";

const express = require('express');
const { requireAdmin } = require('../middleware/auth-middleware');
const { handleApiError } = require('../middleware/error-middleware');
const { validateRequest, validateQuery, validateParams } = require('../middleware/validation-middleware');
const { TelegramValidator, EmailValidator, TasksValidator } = require('../validators');

/**
 * 创建 API 路由
 * @param {Object} controllers - 控制器实例集合
 * @returns {express.Router} Express路由器
 */
function createApiRoutes(controllers) {
    const router = express.Router();
    const { telegramApiController, emailApiController, tasksApiController } = controllers;

    if (!telegramApiController || !emailApiController || !tasksApiController) {
        throw new Error('所有 API 控制器都是必需的');
    }

    // 获取路由处理器
    const telegramHandlers = telegramApiController.createRouteHandlers();
    const emailHandlers = emailApiController.createRouteHandlers();
    const tasksHandlers = tasksApiController.createRouteHandlers();

    // ===================
    // Telegram API 路由
    // ===================
    
    // 获取 Telegram 配置
    router.get('/telegram/config',
        requireAdmin,
        telegramHandlers.getConfig
    );

    // 保存 Telegram 配置
    router.post('/telegram/config',
        requireAdmin,
        validateRequest(TelegramValidator.validateConfig.bind(TelegramValidator)),
        telegramHandlers.saveConfig
    );

    // 发送测试通知
    router.post('/telegram/test',
        requireAdmin,
        validateRequest(TelegramValidator.validateTestNotification.bind(TelegramValidator)),
        telegramHandlers.testNotification
    );

    // 验证 Bot Token
    router.post('/telegram/validate-token',
        requireAdmin,
        telegramHandlers.validateToken
    );

    // 验证 Chat ID
    router.post('/telegram/validate-chatid',
        requireAdmin,
        telegramHandlers.validateChatId
    );

    // 获取 Bot 信息
    router.get('/telegram/info',
        requireAdmin,
        telegramHandlers.getBotInfo
    );

    // 获取通知统计
    router.get('/telegram/stats',
        requireAdmin,
        telegramHandlers.getStats
    );

    // 更新通知类型配置
    router.post('/telegram/notification-types',
        requireAdmin,
        validateRequest(TelegramValidator.validateNotificationTypes.bind(TelegramValidator)),
        telegramHandlers.updateNotificationTypes
    );

    // 批量发送通知
    router.post('/telegram/batch',
        requireAdmin,
        telegramHandlers.batchNotifications
    );

    // ===================
    // 邮件 API 路由
    // ===================
    
    // 获取邮件配置
    router.get('/email/config',
        requireAdmin,
        emailHandlers.getConfig
    );

    // 保存邮件配置
    router.post('/email/config',
        requireAdmin,
        validateRequest(EmailValidator.validateConfig.bind(EmailValidator)),
        emailHandlers.saveConfig
    );

    // 发送测试邮件
    router.post('/email/test',
        requireAdmin,
        validateRequest(EmailValidator.validateTestEmail.bind(EmailValidator)),
        emailHandlers.testEmail
    );

    // 获取邮件日志
    router.get('/email/logs',
        requireAdmin,
        validateQuery(EmailValidator.validateLogQuery.bind(EmailValidator)),
        emailHandlers.getLogs
    );

    // 清理邮件日志
    router.post('/email/logs/cleanup',
        requireAdmin,
        validateRequest(EmailValidator.validateLogCleanup.bind(EmailValidator)),
        emailHandlers.cleanupLogs
    );

    // 验证 SMTP 连接
    router.post('/email/validate-smtp',
        requireAdmin,
        emailHandlers.validateSmtp
    );

    // 获取邮件统计
    router.get('/email/stats',
        requireAdmin,
        emailHandlers.getStats
    );

    // 更新通知类型配置
    router.post('/email/notification-types',
        requireAdmin,
        validateRequest(EmailValidator.validateNotificationTypes.bind(EmailValidator)),
        emailHandlers.updateNotificationTypes
    );

    // 批量发送邮件
    router.post('/email/batch',
        requireAdmin,
        emailHandlers.batchEmails
    );

    // 获取邮件模板
    router.get('/email/template/:type',
        requireAdmin,
        emailHandlers.getTemplate
    );

    // ===================
    // 任务 API 路由
    // ===================
    
    // 获取任务列表
    router.get('/tasks',
        requireAdmin,
        validateQuery(TasksValidator.validateTaskQuery.bind(TasksValidator)),
        tasksHandlers.listTasks
    );

    // 获取单个任务
    router.get('/tasks/:taskId',
        requireAdmin,
        validateParams(TasksValidator.validateTaskId.bind(TasksValidator)),
        tasksHandlers.getTask
    );

    // 创建任务
    router.post('/tasks',
        requireAdmin,
        validateRequest(TasksValidator.validateTask.bind(TasksValidator)),
        tasksHandlers.createTask
    );

    // 更新任务
    router.put('/tasks/:taskId',
        requireAdmin,
        validateParams(TasksValidator.validateTaskId.bind(TasksValidator)),
        validateRequest(TasksValidator.validateTask.bind(TasksValidator)),
        tasksHandlers.updateTask
    );

    // 删除任务
    router.delete('/tasks/:taskId',
        requireAdmin,
        validateParams(TasksValidator.validateTaskId.bind(TasksValidator)),
        tasksHandlers.deleteTask
    );

    // 切换任务状态
    router.post('/tasks/toggle',
        requireAdmin,
        validateRequest(TasksValidator.validateToggleTask.bind(TasksValidator)),
        tasksHandlers.toggleTask
    );

    // 获取任务统计
    router.get('/tasks/stats',
        requireAdmin,
        tasksHandlers.getStats
    );

    // 批量操作任务
    router.post('/tasks/batch',
        requireAdmin,
        validateRequest(TasksValidator.validateBatchOperation.bind(TasksValidator)),
        tasksHandlers.batchOperation
    );

    // 复制任务
    router.post('/tasks/:taskId/duplicate',
        requireAdmin,
        validateParams(TasksValidator.validateTaskId.bind(TasksValidator)),
        tasksHandlers.duplicateTask
    );

    // ===================
    // 通用 API 路由
    // ===================
    
    // 获取所有配置
    router.get('/config',
        requireAdmin,
        async (req, res) => {
            try {
                // 这里可以调用配置服务获取所有配置
                const allConfigs = await req.app.locals.configService?.getAllConfigs();
                res.json({
                    code: 1,
                    data: allConfigs || {}
                });
            } catch (error) {
                console.error('[API] 获取所有配置失败:', error);
                res.status(500).json({
                    code: 0,
                    msg: '获取配置失败',
                    error: error.message
                });
            }
        }
    );

    // 发送测试通知（通用）
    router.post('/test',
        requireAdmin,
        async (req, res) => {
            try {
                // 这里可以调用通知管理器发送测试通知
                const results = [];
                
                // 发送 Telegram 测试通知
                try {
                    const telegramResult = await telegramHandlers.testNotification(req, { json: () => {} });
                    results.push({ type: 'telegram', ...telegramResult });
                } catch (error) {
                    results.push({ type: 'telegram', success: false, error: error.message });
                }

                // 发送邮件测试通知
                try {
                    const emailResult = await emailHandlers.testEmail(req, { json: () => {} });
                    results.push({ type: 'email', ...emailResult });
                } catch (error) {
                    results.push({ type: 'email', success: false, error: error.message });
                }

                const successCount = results.filter(r => r.success).length;
                
                res.json({
                    code: successCount > 0 ? 1 : 0,
                    msg: `测试通知完成，成功 ${successCount}/${results.length} 个通道`,
                    data: results
                });
            } catch (error) {
                console.error('[API] 发送测试通知失败:', error);
                res.status(500).json({
                    code: 0,
                    msg: '发送测试通知失败',
                    error: error.message
                });
            }
        }
    );

    // 错误处理中间件
    router.use(handleApiError);

    return router;
}

module.exports = createApiRoutes;
