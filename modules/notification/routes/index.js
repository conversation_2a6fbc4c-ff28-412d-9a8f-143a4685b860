/**
 * 通知模块路由入口文件
 * 统一管理所有路由
 */
"use strict";

const express = require('express');
const createPageRoutes = require('./page-routes');
const createApiRoutes = require('./api-routes');

/**
 * 创建通知模块路由
 * @param {Object} dependencies - 依赖对象
 * @param {Object} dependencies.controllers - 控制器实例集合
 * @param {Object} dependencies.services - 服务实例集合
 * @returns {express.Router} Express路由器
 */
function createNotificationRoutes(dependencies) {
    const { controllers, services } = dependencies;
    
    if (!controllers) {
        throw new Error('控制器实例集合是必需的');
    }

    const router = express.Router();

    try {
        // 创建页面路由
        const pageRoutes = createPageRoutes(controllers);
        router.use('/admin', pageRoutes);

        // 创建 API 路由
        const apiRoutes = createApiRoutes(controllers);
        router.use('/admin/notification', apiRoutes);

        // 添加服务到 app.locals 以便在路由中访问
        router.use((req, res, next) => {
            if (services) {
                Object.assign(req.app.locals, services);
            }
            next();
        });

        console.log('[路由] 通知模块路由初始化完成');
        
    } catch (error) {
        console.error('[路由] 通知模块路由初始化失败:', error);
        throw error;
    }

    return router;
}

/**
 * 路由工厂函数
 * 创建并配置所有路由
 * @param {Object} app - Express应用实例
 * @param {Object} dependencies - 依赖对象
 */
function setupNotificationRoutes(app, dependencies) {
    try {
        const notificationRoutes = createNotificationRoutes(dependencies);
        app.use(notificationRoutes);
        
        console.log('[路由] 通知模块路由设置完成');
    } catch (error) {
        console.error('[路由] 通知模块路由设置失败:', error);
        throw error;
    }
}

module.exports = {
    createNotificationRoutes,
    setupNotificationRoutes,
    createPageRoutes,
    createApiRoutes
};
