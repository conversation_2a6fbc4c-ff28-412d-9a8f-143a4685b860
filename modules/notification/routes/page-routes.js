/**
 * 通知设置页面路由
 * 处理页面渲染相关的路由
 */
"use strict";

const express = require('express');
const { requireAdmin } = require('../middleware/auth-middleware');
const { handlePageError } = require('../middleware/error-middleware');

/**
 * 创建页面路由
 * @param {Object} controllers - 控制器实例集合
 * @returns {express.Router} Express路由器
 */
function createPageRoutes(controllers) {
    const router = express.Router();
    const { pageController } = controllers;

    if (!pageController) {
        throw new Error('PageController 是必需的');
    }

    // 获取路由处理器
    const handlers = pageController.createRouteHandlers();

    // 通知设置主页面
    router.get('/notification', 
        requireAdmin,
        handlers.renderNotificationPage
    );

    // 获取页面数据（AJAX）
    router.get('/notification/data',
        requireAdmin,
        handlers.getPageData
    );

    // 获取服务器列表
    router.get('/notification/servers',
        requireAdmin,
        handlers.getServers
    );

    // 获取配置概览
    router.get('/notification/overview',
        requireAdmin,
        handlers.getConfigOverview
    );

    // 健康检查
    router.get('/notification/health',
        requireAdmin,
        handlers.healthCheck
    );

    // 系统信息
    router.get('/notification/system',
        requireAdmin,
        handlers.getSystemInfo
    );

    // 错误处理中间件
    router.use(handlePageError);

    return router;
}

module.exports = createPageRoutes;
