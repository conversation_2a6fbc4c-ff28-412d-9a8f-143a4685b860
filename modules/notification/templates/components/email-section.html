<!-- 邮件通知设置卡片 - 折叠 -->
<div class="admin-card">
    <!-- 头部：可折叠，含状态与操作 -->
    <div class="admin-list-header flex items-center justify-between gap-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-200 rounded-lg -mx-2 px-2" onclick="toggleCard('email')" title="点击展开/收起邮件配置">
        <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-rose-100 to-rose-200 dark:from-rose-900/40 dark:to-rose-800/30 rounded-lg shadow-sm flex-shrink-0 border border-rose-200/50 dark:border-rose-700/30 flex items-center justify-center">
                <i class="ti ti-mail text-base text-rose-600 dark:text-rose-400"></i>
            </div>
            <div>
                <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">邮件通知设置</h2>
                <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置SMTP邮件通知功能</p>
            </div>
        </div>
        <div class="flex items-center gap-3">
            <span class="inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border email-status-badge">
                <span class="w-2 h-2 rounded-full email-status-dot"></span>
                <span class="email-status-text">未配置</span>
            </span>
            <div class="flex items-center gap-1 text-slate-400 dark:text-slate-500">
                <i class="ti ti-chevron-down text-sm transition-transform duration-200" id="email-chevron"></i>
                <span class="text-xs hidden sm:inline">展开</span>
            </div>
        </div>
    </div>

    <!-- 内容区域：可折叠 -->
    <div id="email-content" class="hidden">
        <div class="p-6 space-y-6">
            <!-- 启用开关 -->
            <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-rose-100 to-rose-200 dark:from-rose-900/40 dark:to-rose-800/30 rounded-lg flex items-center justify-center">
                        <i class="ti ti-power text-lg text-rose-600 dark:text-rose-400"></i>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-slate-800 dark:text-slate-200">启用邮件通知</h3>
                        <p class="text-xs text-slate-500 dark:text-slate-400">开启后将通过邮件发送通知消息</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="email-enabled" class="sr-only peer">
                    <div class="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-rose-300 dark:peer-focus:ring-rose-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-slate-600 peer-checked:bg-rose-600"></div>
                </label>
            </div>

            <!-- SMTP 服务器配置 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-server mr-1"></i>SMTP 服务器
                    </label>
                    <input type="text" id="email-host"
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                           placeholder="smtp.gmail.com">
                </div>
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-plug mr-1"></i>端口
                    </label>
                    <input type="number" id="email-port" min="1" max="65535"
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                           placeholder="587">
                </div>
            </div>

            <!-- 安全连接 -->
            <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <div>
                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300">安全连接 (SSL/TLS)</h4>
                    <p class="text-xs text-slate-500 dark:text-slate-400">使用加密连接发送邮件</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="email-secure" class="sr-only peer">
                    <div class="w-9 h-5 bg-slate-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-rose-300 dark:peer-focus:ring-rose-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-slate-600 peer-checked:bg-rose-600"></div>
                </label>
            </div>

            <!-- 认证信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-user mr-1"></i>用户名
                    </label>
                    <input type="text" id="email-auth-user"
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                           placeholder="<EMAIL>">
                </div>
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-key mr-1"></i>密码
                    </label>
                    <div class="relative">
                        <input type="password" id="email-auth-pass"
                               class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                               placeholder="应用专用密码">
                        <button type="button" onclick="togglePasswordVisibility('email-auth-pass')"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors">
                            <i class="ti ti-eye text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 发件人信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-mail mr-1"></i>发件人邮箱
                    </label>
                    <input type="email" id="email-from-address"
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                           placeholder="<EMAIL>">
                </div>
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                        <i class="ti ti-signature mr-1"></i>发件人名称
                    </label>
                    <input type="text" id="email-from-name"
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200"
                           placeholder="系统通知">
                </div>
            </div>

            <!-- 收件人配置 -->
            <div class="space-y-4">
                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700 pb-2">
                    <i class="ti ti-users mr-1"></i>收件人配置
                </h4>
                
                <div class="space-y-3">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">收件人 (To)</label>
                    <textarea id="email-to-addresses" rows="2"
                              class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent transition-all duration-200 resize-none"
                              placeholder="<EMAIL>, <EMAIL>"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">抄送 (CC)</label>
                        <textarea id="email-cc-addresses" rows="2"
                                  class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent resize-none"
                                  placeholder="可选，多个邮箱用逗号分隔"></textarea>
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">密送 (BCC)</label>
                        <textarea id="email-bcc-addresses" rows="2"
                                  class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-transparent resize-none"
                                  placeholder="可选，多个邮箱用逗号分隔"></textarea>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
                <div class="flex flex-wrap items-center gap-3">
                    <button onclick="saveEmailConfig()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200">
                        <i class="ti ti-device-floppy text-sm"></i>
                        保存配置
                    </button>
                    <button onclick="testEmail()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                        <i class="ti ti-send text-sm"></i>
                        发送测试
                    </button>
                    <button onclick="showEmailLogs()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                        <i class="ti ti-history text-sm"></i>
                        查看日志
                    </button>
                </div>
                <div class="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
                    <i class="ti ti-info-circle"></i>
                    <span>建议使用应用专用密码</span>
                </div>
            </div>
        </div>
    </div>
</div>
