<!-- 通知设置页面组件化模板 -->
<!-- 这个文件包含所有组件的引用，用于重构后的模块化页面 -->

<!-- 页面标题卡片 -->
<div class="admin-card">
    <div class="admin-card-header">
        <div class="admin-page-header">
            <div class="admin-page-title">
                <div class="admin-title-icon admin-icon-purple">
                    <i class="ti ti-bell text-lg text-purple-600 dark:text-purple-400"></i>
                </div>
                <div class="admin-title-text">
                    <h1>通知设置</h1>
                    <p class="text-xs">Notification Settings</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通知类型统一管理区 -->
{% include 'modules/notification/templates/components/notification-types-section.html' %}

<!-- Telegram 通知设置 -->
{% include 'modules/notification/templates/components/telegram-section.html' %}

<!-- 邮件通知设置 -->
{% include 'modules/notification/templates/components/email-section.html' %}

<!-- 自定义通知任务 -->
{% include 'modules/notification/templates/components/tasks-section.html' %}

<!-- 页面底部信息 -->
<div class="admin-card">
    <div class="p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-lg flex items-center justify-center">
                    <i class="ti ti-info-circle text-slate-600 dark:text-slate-400"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-slate-800 dark:text-slate-200">配置说明</h3>
                    <p class="text-xs text-slate-500 dark:text-slate-400">通知系统配置和使用指南</p>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-2">
                <a href="#" onclick="showHelp()" class="inline-flex items-center gap-1 px-3 py-1 text-xs text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">
                    <i class="ti ti-help"></i>
                    帮助文档
                </a>
                <a href="#" onclick="showApiDocs()" class="inline-flex items-center gap-1 px-3 py-1 text-xs text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">
                    <i class="ti ti-api"></i>
                    API 文档
                </a>
                <a href="#" onclick="showChangelog()" class="inline-flex items-center gap-1 px-3 py-1 text-xs text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">
                    <i class="ti ti-history"></i>
                    更新日志
                </a>
            </div>
        </div>

        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    <i class="ti ti-brand-telegram text-cyan-500 mr-1"></i>
                    Telegram 配置
                </h4>
                <ul class="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                    <li>• 从 @BotFather 获取 Bot Token</li>
                    <li>• 添加机器人到群组或频道</li>
                    <li>• 获取 Chat ID 并配置</li>
                    <li>• 测试通知确保正常工作</li>
                </ul>
            </div>
            <div class="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    <i class="ti ti-mail text-rose-500 mr-1"></i>
                    邮件配置
                </h4>
                <ul class="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                    <li>• 配置 SMTP 服务器信息</li>
                    <li>• 使用应用专用密码</li>
                    <li>• 设置收件人列表</li>
                    <li>• 发送测试邮件验证</li>
                </ul>
            </div>
            <div class="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    <i class="ti ti-bell-ringing text-amber-500 mr-1"></i>
                    自定义任务
                </h4>
                <ul class="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                    <li>• 为每个服务器创建监控任务</li>
                    <li>• 设置流量阈值和周期</li>
                    <li>• 配置通知触发条件</li>
                    <li>• 管理任务启用状态</li>
                </ul>
            </div>
        </div>

        <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/30">
            <div class="flex items-start gap-3">
                <i class="ti ti-lightbulb text-blue-600 dark:text-blue-400 mt-0.5"></i>
                <div>
                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">使用提示</h4>
                    <ul class="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                        <li>• 建议同时配置多个通知通道以确保消息送达</li>
                        <li>• 定期测试通知功能确保配置正确</li>
                        <li>• 根据实际需求调整通知类型和阈值</li>
                        <li>• 查看邮件日志了解发送状态</li>
                        <li>• 使用自定义任务实现精细化监控</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 全局模态框和提示 -->
<div id="global-loading" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="h-full w-full flex items-center justify-center">
        <div class="bg-white dark:bg-slate-800 rounded-lg p-6 shadow-xl">
            <div class="flex items-center gap-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                <span class="text-slate-700 dark:text-slate-300">处理中...</span>
            </div>
        </div>
    </div>
</div>

<div id="global-toast" class="fixed top-4 right-4 z-50 hidden">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 p-4 max-w-sm">
        <div class="flex items-center gap-3">
            <div id="toast-icon" class="flex-shrink-0"></div>
            <div class="flex-1">
                <div id="toast-title" class="text-sm font-medium text-slate-800 dark:text-slate-200"></div>
                <div id="toast-message" class="text-xs text-slate-500 dark:text-slate-400 mt-1"></div>
            </div>
            <button onclick="hideToast()" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300">
                <i class="ti ti-x"></i>
            </button>
        </div>
    </div>
</div>
