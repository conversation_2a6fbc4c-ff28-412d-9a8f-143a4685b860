<!-- Telegram通知设置卡片 - 折叠 -->
<div class="admin-card">
    <!-- 头部：可折叠，含状态与操作 -->
    <div class="admin-list-header flex items-center justify-between gap-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-200 rounded-lg -mx-2 px-2" onclick="toggleCard('tg')" title="点击展开/收起 Telegram 配置">
        <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-cyan-100 to-cyan-200 dark:from-cyan-900/40 dark:to-cyan-800/30 rounded-lg shadow-sm flex-shrink-0 border border-cyan-200/50 dark:border-cyan-700/30 flex items-center justify-center">
                <i class="ti ti-brand-telegram text-base text-cyan-600 dark:text-cyan-400"></i>
            </div>
            <div>
                <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">Telegram 通知设置</h2>
                <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置Telegram机器人通知功能</p>
            </div>
        </div>
        <div class="flex items-center gap-3">
            <span class="inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border
                {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}
                    text-emerald-600 border-emerald-300 bg-emerald-50 dark:text-emerald-400 dark:border-emerald-700/40 dark:bg-emerald-900/20
                {% else %}
                    text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30
                {% endif %}">
                <span class="w-2 h-2 rounded-full
                    {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}bg-emerald-500{% else %}bg-slate-400{% endif %}"></span>
                {% if setting.telegram and setting.telegram.enabled and setting.telegram.token %}已配置{% else %}未配置{% endif %}
            </span>
            <div class="flex items-center gap-1 text-slate-400 dark:text-slate-500">
                <i class="ti ti-chevron-down text-sm transition-transform duration-200" id="tg-chevron"></i>
                <span class="text-xs hidden sm:inline">展开</span>
            </div>
        </div>
    </div>

    <!-- 内容区域：可折叠 -->
    <div id="tg-content" class="hidden">
        <div class="p-6 space-y-6">
            <!-- 启用开关 -->
            <div class="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-cyan-100 to-cyan-200 dark:from-cyan-900/40 dark:to-cyan-800/30 rounded-lg flex items-center justify-center">
                        <i class="ti ti-power text-lg text-cyan-600 dark:text-cyan-400"></i>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-slate-800 dark:text-slate-200">启用 Telegram 通知</h3>
                        <p class="text-xs text-slate-500 dark:text-slate-400">开启后将通过Telegram发送通知消息</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="telegram-enabled" class="sr-only peer" 
                           {% if setting.telegram and setting.telegram.enabled %}checked{% endif %}>
                    <div class="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-300 dark:peer-focus:ring-cyan-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-slate-600 peer-checked:bg-cyan-600"></div>
                </label>
            </div>

            <!-- Bot Token 配置 -->
            <div class="space-y-3">
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                    <i class="ti ti-key mr-1"></i>Bot Token
                </label>
                <div class="relative">
                    <input type="password" id="telegram-token" 
                           class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                           placeholder="请输入 Telegram Bot Token"
                           value="{% if setting.telegram and setting.telegram.token %}{{ setting.telegram.token }}{% endif %}">
                    <button type="button" onclick="togglePasswordVisibility('telegram-token')" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors">
                        <i class="ti ti-eye text-lg"></i>
                    </button>
                </div>
                <p class="text-xs text-slate-500 dark:text-slate-400">
                    <i class="ti ti-info-circle mr-1"></i>
                    从 <a href="https://t.me/BotFather" target="_blank" class="text-cyan-600 hover:text-cyan-700 dark:text-cyan-400 dark:hover:text-cyan-300">@BotFather</a> 获取 Bot Token
                </p>
            </div>

            <!-- Chat IDs 配置 -->
            <div class="space-y-3">
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                    <i class="ti ti-users mr-1"></i>Chat IDs
                </label>
                <textarea id="telegram-chatids" rows="3"
                          class="w-full px-4 py-3 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-xl text-sm placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none"
                          placeholder="请输入 Chat IDs，多个用逗号分隔">{% if setting.telegram and setting.telegram.chatIds %}{{ setting.telegram.chatIds | join(', ') }}{% endif %}</textarea>
                <p class="text-xs text-slate-500 dark:text-slate-400">
                    <i class="ti ti-info-circle mr-1"></i>
                    支持用户ID（数字）或频道用户名（@开头），多个用逗号分隔
                </p>
            </div>

            <!-- 高级设置 -->
            <div class="border-t border-slate-200 dark:border-slate-700 pt-6">
                <div class="flex items-center gap-2 mb-4 cursor-pointer" onclick="toggleAdvanced('telegram')">
                    <i class="ti ti-chevron-right text-sm text-slate-400 transition-transform duration-200" id="telegram-advanced-chevron"></i>
                    <span class="text-sm font-medium text-slate-700 dark:text-slate-300">高级设置</span>
                </div>
                
                <div id="telegram-advanced" class="hidden space-y-4">
                    <!-- Webhook 设置 -->
                    <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                        <div>
                            <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300">Webhook 模式</h4>
                            <p class="text-xs text-slate-500 dark:text-slate-400">使用 Webhook 接收消息（实验性功能）</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="telegram-webhook" class="sr-only peer"
                                   {% if setting.telegram and setting.telegram.webhook %}checked{% endif %}>
                            <div class="w-9 h-5 bg-slate-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 dark:peer-focus:ring-cyan-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-slate-600 peer-checked:bg-cyan-600"></div>
                        </label>
                    </div>

                    <!-- Webhook 端口 -->
                    <div class="space-y-2" id="webhook-port-section" style="display: {% if setting.telegram and setting.telegram.webhook %}block{% else %}none{% endif %};">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">Webhook 端口</label>
                        <input type="number" id="telegram-webhook-port" min="1" max="65535"
                               class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               value="{% if setting.telegram and setting.telegram.webhookPort %}{{ setting.telegram.webhookPort }}{% else %}3001{% endif %}">
                    </div>

                    <!-- API Base URL -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">API Base URL</label>
                        <input type="url" id="telegram-base-url"
                               class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               placeholder="https://api.telegram.org"
                               value="{% if setting.telegram and setting.telegram.baseApiUrl %}{{ setting.telegram.baseApiUrl }}{% endif %}">
                        <p class="text-xs text-slate-500 dark:text-slate-400">留空使用默认 API 地址</p>
                    </div>

                    <!-- 离线通知延迟 -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">离线通知延迟（秒）</label>
                        <input type="number" id="telegram-offline-delay" min="0" max="3600"
                               class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               value="{% if setting.telegram and setting.telegram.offlineNotificationDelay %}{{ setting.telegram.offlineNotificationDelay }}{% else %}300{% endif %}">
                        <p class="text-xs text-slate-500 dark:text-slate-400">服务器离线多少秒后发送通知</p>
                    </div>

                    <!-- 流量阈值 -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">流量阈值（%）</label>
                        <input type="text" id="telegram-traffic-thresholds"
                               class="w-full px-3 py-2 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               placeholder="80,90,95"
                               value="{% if setting.telegram and setting.telegram.trafficThresholds %}{{ setting.telegram.trafficThresholds | join(',') }}{% else %}80,90,95{% endif %}">
                        <p class="text-xs text-slate-500 dark:text-slate-400">流量使用达到这些百分比时发送通知，用逗号分隔</p>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
                <div class="flex flex-wrap items-center gap-3">
                    <button onclick="saveTelegramConfig()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200">
                        <i class="ti ti-device-floppy text-sm"></i>
                        保存配置
                    </button>
                    <button onclick="testTelegramNotification()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                        <i class="ti ti-send text-sm"></i>
                        发送测试
                    </button>
                </div>
                <div class="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
                    <i class="ti ti-info-circle"></i>
                    <span>保存后自动重新初始化机器人</span>
                </div>
            </div>
        </div>
    </div>
</div>
