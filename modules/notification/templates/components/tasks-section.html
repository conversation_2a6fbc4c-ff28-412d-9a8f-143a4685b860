<!-- 自定义通知任务卡片 - 折叠 -->
<div class="admin-card">
    <!-- 头部：可折叠，含状态与操作 -->
    <div class="admin-list-header flex items-center justify-between gap-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-200 rounded-lg -mx-2 px-2" onclick="toggleCard('tasks')" title="点击展开/收起任务管理">
        <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/40 dark:to-amber-800/30 rounded-lg shadow-sm flex-shrink-0 border border-amber-200/50 dark:border-amber-700/30 flex items-center justify-center">
                <i class="ti ti-bell-ringing text-base text-amber-600 dark:text-amber-400"></i>
            </div>
            <div>
                <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">自定义通知任务</h2>
                <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">配置服务器流量监控和自定义通知规则</p>
            </div>
        </div>
        <div class="flex items-center gap-3">
            <span class="inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border tasks-status-badge">
                <span class="w-2 h-2 rounded-full tasks-status-dot"></span>
                <span class="tasks-status-text">0个任务</span>
            </span>
            <div class="flex items-center gap-1 text-slate-400 dark:text-slate-500">
                <i class="ti ti-chevron-down text-sm transition-transform duration-200" id="tasks-chevron"></i>
                <span class="text-xs hidden sm:inline">展开</span>
            </div>
        </div>
    </div>

    <!-- 内容区域：可折叠 -->
    <div id="tasks-content" class="hidden">
        <div class="p-6 space-y-6">
            <!-- 任务统计 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 text-center">
                    <div class="text-lg font-semibold text-slate-800 dark:text-slate-200" id="total-tasks">0</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">总任务</div>
                </div>
                <div class="bg-emerald-50 dark:bg-emerald-900/20 rounded-lg p-3 text-center">
                    <div class="text-lg font-semibold text-emerald-600 dark:text-emerald-400" id="enabled-tasks">0</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">已启用</div>
                </div>
                <div class="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-3 text-center">
                    <div class="text-lg font-semibold text-slate-600 dark:text-slate-400" id="disabled-tasks">0</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">已禁用</div>
                </div>
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center">
                    <div class="text-lg font-semibold text-blue-600 dark:text-blue-400" id="server-count">0</div>
                    <div class="text-xs text-slate-500 dark:text-slate-400">服务器</div>
                </div>
            </div>

            <!-- 创建任务按钮 -->
            <div class="flex items-center justify-between">
                <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300">任务列表</h3>
                <button onclick="showCreateTaskModal()" class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                    <i class="ti ti-plus text-sm"></i>
                    创建任务
                </button>
            </div>

            <!-- 任务过滤器 -->
            <div class="flex flex-wrap items-center gap-3 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <div class="flex items-center gap-2">
                    <label class="text-xs font-medium text-slate-600 dark:text-slate-400">服务器:</label>
                    <select id="task-filter-server" onchange="filterTasks()" class="px-2 py-1 text-xs bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-amber-500">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="flex items-center gap-2">
                    <label class="text-xs font-medium text-slate-600 dark:text-slate-400">状态:</label>
                    <select id="task-filter-status" onchange="filterTasks()" class="px-2 py-1 text-xs bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-amber-500">
                        <option value="">全部</option>
                        <option value="enabled">已启用</option>
                        <option value="disabled">已禁用</option>
                    </select>
                </div>
                <div class="flex items-center gap-2">
                    <label class="text-xs font-medium text-slate-600 dark:text-slate-400">周期:</label>
                    <select id="task-filter-period" onchange="filterTasks()" class="px-2 py-1 text-xs bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-amber-500">
                        <option value="">全部</option>
                        <option value="daily">每日</option>
                        <option value="weekly">每周</option>
                        <option value="monthly">每月</option>
                    </select>
                </div>
                <button onclick="clearTaskFilters()" class="text-xs text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300">
                    <i class="ti ti-x"></i> 清除
                </button>
            </div>

            <!-- 任务列表 -->
            <div class="space-y-3" id="tasks-list">
                <!-- 任务项将通过 JavaScript 动态生成 -->
                <div class="text-center py-8 text-slate-500 dark:text-slate-400" id="no-tasks-message">
                    <i class="ti ti-bell-off text-3xl mb-2"></i>
                    <p>暂无自定义通知任务</p>
                    <p class="text-xs mt-1">点击"创建任务"开始配置</p>
                </div>
            </div>

            <!-- 批量操作 -->
            <div class="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700" id="batch-operations" style="display: none;">
                <div class="flex items-center gap-2">
                    <input type="checkbox" id="select-all-tasks" onchange="toggleSelectAllTasks()" class="rounded border-slate-300 dark:border-slate-600 text-amber-600 focus:ring-amber-500">
                    <label for="select-all-tasks" class="text-sm text-slate-600 dark:text-slate-400">全选</label>
                    <span class="text-xs text-slate-500 dark:text-slate-400" id="selected-count">已选择 0 个任务</span>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="batchEnableTasks()" class="px-3 py-1 text-xs bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 rounded hover:bg-emerald-200 dark:hover:bg-emerald-900/50 transition-colors">
                        <i class="ti ti-check"></i> 启用
                    </button>
                    <button onclick="batchDisableTasks()" class="px-3 py-1 text-xs bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 rounded hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">
                        <i class="ti ti-x"></i> 禁用
                    </button>
                    <button onclick="batchDeleteTasks()" class="px-3 py-1 text-xs bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors">
                        <i class="ti ti-trash"></i> 删除
                    </button>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 pt-6 border-t border-slate-200 dark:border-slate-700">
                <div class="flex flex-wrap items-center gap-3">
                    <button onclick="refreshTasks()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                        <i class="ti ti-refresh text-sm"></i>
                        刷新列表
                    </button>
                    <button onclick="exportTasks()" class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-xl border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200">
                        <i class="ti ti-download text-sm"></i>
                        导出配置
                    </button>
                </div>
                <div class="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
                    <i class="ti ti-info-circle"></i>
                    <span>任务按创建时间排序</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建任务模态框 -->
<div id="create-task-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 p-4">
    <div class="h-full w-full flex items-center justify-center">
        <div class="bg-white dark:bg-slate-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">创建通知任务</h3>
                <button onclick="hideCreateTaskModal()" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300">
                    <i class="ti ti-x text-xl"></i>
                </button>
            </div>

            <form id="create-task-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">服务器</label>
                        <select id="task-server" required class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                            <option value="">请选择服务器</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">监控周期</label>
                        <select id="task-period" required class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                            <option value="monthly">每月</option>
                            <option value="weekly">每周</option>
                            <option value="daily">每日</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">流量方向</label>
                        <select id="task-direction" class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                            <option value="both">双向</option>
                            <option value="in">入站</option>
                            <option value="out">出站</option>
                            <option value="max">最大值</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">重置日期</label>
                        <input type="number" id="task-reset-day" min="1" max="31" placeholder="1-31"
                               class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                        <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">留空使用默认值</p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">流量上限 (GB)</label>
                    <input type="number" id="task-traffic-limit" min="0" step="0.1" placeholder="可选"
                           class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                    <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">留空则使用服务器配置的流量限制</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">通知阈值 (%)</label>
                    <input type="text" id="task-thresholds" placeholder="80,90,95"
                           class="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-amber-500">
                    <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">多个阈值用逗号分隔，如：80,90,95</p>
                </div>

                <div class="flex items-center gap-3 p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                    <input type="checkbox" id="task-enabled" checked class="rounded border-slate-300 dark:border-slate-600 text-amber-600 focus:ring-amber-500">
                    <label for="task-enabled" class="text-sm text-slate-700 dark:text-slate-300">创建后立即启用</label>
                </div>

                <div class="flex items-center justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <button type="button" onclick="hideCreateTaskModal()" class="px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                        <i class="ti ti-plus text-sm mr-1"></i>
                        创建任务
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>
</div>
