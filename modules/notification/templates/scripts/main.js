/**
 * 通知设置页面主入口文件
 * 协调各个模块的初始化和交互
 */

class NotificationMain {
    constructor() {
        this.modules = {};
        this.config = {};
        this.isInitialized = false;
        this.init();
    }

    /**
     * 初始化主控制器
     */
    async init() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initModules());
            } else {
                await this.initModules();
            }
        } catch (error) {
            console.error('初始化通知设置页面失败:', error);
        }
    }

    /**
     * 初始化各个模块
     */
    async initModules() {
        try {
            // 等待所有模块加载完成
            await this.waitForModules();

            // 初始化模块引用
            this.modules = {
                telegram: window.telegramClient,
                email: window.emailClient,
                tasks: window.tasksClient,
                utils: window.notificationUtils
            };

            // 绑定全局事件
            this.bindGlobalEvents();

            // 加载初始配置
            await this.loadInitialConfig();

            // 初始化通知类型管理
            this.initNotificationTypes();

            // 更新状态显示
            this.updateGlobalStatus();

            this.isInitialized = true;
            console.log('通知设置页面初始化完成');

        } catch (error) {
            console.error('模块初始化失败:', error);
            this.showError('页面初始化失败，请刷新重试');
        }
    }

    /**
     * 等待模块加载完成
     */
    async waitForModules() {
        const maxWait = 10000; // 最大等待10秒
        const checkInterval = 100; // 每100ms检查一次
        let waited = 0;

        return new Promise((resolve, reject) => {
            const checkModules = () => {
                if (window.telegramClient && window.emailClient && window.tasksClient && window.notificationUtils) {
                    resolve();
                } else if (waited >= maxWait) {
                    reject(new Error('模块加载超时'));
                } else {
                    waited += checkInterval;
                    setTimeout(checkModules, checkInterval);
                }
            };
            checkModules();
        });
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                this.refreshAllData();
            }
        });

        // 窗口焦点变化时刷新数据
        window.addEventListener('focus', () => {
            if (this.isInitialized) {
                this.refreshAllData();
            }
        });

        // 监听配置变更事件
        this.bindConfigChangeEvents();
    }

    /**
     * 绑定配置变更事件
     */
    bindConfigChangeEvents() {
        // 监听各模块的配置变更
        document.addEventListener('configChanged', (event) => {
            const { module, config } = event.detail;
            this.onConfigChanged(module, config);
        });
    }

    /**
     * 加载初始配置
     */
    async loadInitialConfig() {
        try {
            const response = await fetch('/admin/notification/config');
            const result = await response.json();
            
            if (result.code === 1) {
                this.config = result.data;
            } else {
                console.warn('加载初始配置失败:', result.msg);
            }
        } catch (error) {
            console.error('加载初始配置失败:', error);
        }
    }

    /**
     * 初始化通知类型管理
     */
    initNotificationTypes() {
        this.renderNotificationTypesMatrix();
        this.bindNotificationTypesEvents();
    }

    /**
     * 渲染通知类型矩阵
     */
    renderNotificationTypesMatrix() {
        const container = document.getElementById('unified-notification-matrix');
        if (!container) return;

        const notificationTypes = [
            { key: 'serverOnline', name: '服务器上线', icon: 'ti-circle-check', color: 'emerald' },
            { key: 'serverOffline', name: '服务器下线', icon: 'ti-circle-x', color: 'red' },
            { key: 'trafficLimit', name: '流量超限', icon: 'ti-gauge', color: 'amber' },
            { key: 'testNotification', name: '测试通知', icon: 'ti-bell', color: 'blue' },
            { key: 'statusSummary', name: '状态汇总', icon: 'ti-report', color: 'purple' },
            { key: 'newServerDiscovered', name: '新服务器发现', icon: 'ti-search', color: 'indigo' },
            { key: 'serverApproved', name: '服务器审批', icon: 'ti-check', color: 'green' }
        ];

        const matrixHtml = notificationTypes.map(type => {
            const telegramEnabled = this.config.telegram?.notificationTypes?.[type.key] || false;
            const emailEnabled = this.config.email?.notification_types?.[type.key] || false;

            return `
                <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                    <td class="py-3 px-4">
                        <div class="flex items-center gap-2">
                            <i class="ti ${type.icon} text-${type.color}-500"></i>
                            <span class="text-sm font-medium text-slate-700 dark:text-slate-300">${type.name}</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer notification-type-toggle" 
                                   data-module="telegram" data-type="${type.key}" ${telegramEnabled ? 'checked' : ''}>
                            <div class="w-8 h-4 bg-slate-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-cyan-300 dark:peer-focus:ring-cyan-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all dark:border-slate-600 peer-checked:bg-cyan-600"></div>
                        </label>
                    </td>
                    <td class="py-3 px-4 text-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer notification-type-toggle" 
                                   data-module="email" data-type="${type.key}" ${emailEnabled ? 'checked' : ''}>
                            <div class="w-8 h-4 bg-slate-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-rose-300 dark:peer-focus:ring-rose-800 rounded-full peer dark:bg-slate-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all dark:border-slate-600 peer-checked:bg-rose-600"></div>
                        </label>
                    </td>
                    <td class="py-3 px-4 text-center">
                        <button onclick="window.notificationMain.toggleNotificationType('${type.key}')" 
                                class="px-2 py-1 text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors">
                            <i class="ti ti-toggle-left"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        container.innerHTML = matrixHtml;
    }

    /**
     * 绑定通知类型事件
     */
    bindNotificationTypesEvents() {
        // 监听通知类型切换
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('notification-type-toggle')) {
                const module = e.target.dataset.module;
                const type = e.target.dataset.type;
                const enabled = e.target.checked;
                this.updateNotificationType(module, type, enabled);
            }
        });
    }

    /**
     * 更新通知类型
     */
    async updateNotificationType(module, type, enabled) {
        try {
            const endpoint = `/admin/notification/${module}/notification-types`;
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ [type]: enabled })
            });

            const result = await response.json();
            
            if (result.code === 1) {
                // 更新本地配置
                if (!this.config[module]) this.config[module] = {};
                if (!this.config[module].notificationTypes && !this.config[module].notification_types) {
                    this.config[module][module === 'telegram' ? 'notificationTypes' : 'notification_types'] = {};
                }
                
                const typesKey = module === 'telegram' ? 'notificationTypes' : 'notification_types';
                this.config[module][typesKey][type] = enabled;

                this.showSuccess(`${module === 'telegram' ? 'Telegram' : '邮件'}通知类型更新成功`);
            } else {
                this.showError('更新通知类型失败: ' + result.msg);
                // 恢复开关状态
                const toggle = document.querySelector(`[data-module="${module}"][data-type="${type}"]`);
                if (toggle) toggle.checked = !enabled;
            }
        } catch (error) {
            console.error('更新通知类型失败:', error);
            this.showError('更新通知类型失败');
            // 恢复开关状态
            const toggle = document.querySelector(`[data-module="${module}"][data-type="${type}"]`);
            if (toggle) toggle.checked = !enabled;
        }
    }

    /**
     * 切换通知类型（同时切换所有通道）
     */
    toggleNotificationType(type) {
        const telegramToggle = document.querySelector(`[data-module="telegram"][data-type="${type}"]`);
        const emailToggle = document.querySelector(`[data-module="email"][data-type="${type}"]`);

        // 获取当前状态
        const telegramEnabled = telegramToggle?.checked || false;
        const emailEnabled = emailToggle?.checked || false;

        // 如果任一通道启用，则全部禁用；否则全部启用
        const newState = !(telegramEnabled || emailEnabled);

        if (telegramToggle) {
            telegramToggle.checked = newState;
            telegramToggle.dispatchEvent(new Event('change'));
        }

        if (emailToggle) {
            emailToggle.checked = newState;
            emailToggle.dispatchEvent(new Event('change'));
        }
    }

    /**
     * 配置变更处理
     */
    onConfigChanged(module, config) {
        this.config[module] = config;
        this.updateGlobalStatus();
    }

    /**
     * 刷新所有数据
     */
    async refreshAllData() {
        try {
            await Promise.all([
                this.modules.telegram?.loadConfig(),
                this.modules.email?.loadConfig(),
                this.modules.tasks?.loadData()
            ]);
            
            await this.loadInitialConfig();
            this.updateGlobalStatus();
        } catch (error) {
            console.error('刷新数据失败:', error);
        }
    }

    /**
     * 更新全局状态显示
     */
    updateGlobalStatus() {
        this.updateActiveChannelsCount();
        this.renderNotificationTypesMatrix();
    }

    /**
     * 更新活跃通道数量
     */
    updateActiveChannelsCount() {
        const countElement = document.getElementById('active-channels-count');
        if (!countElement) return;

        let activeCount = 0;
        
        if (this.config.telegram?.enabled && this.config.telegram?.token) {
            activeCount++;
        }
        
        if (this.config.email?.enabled && this.config.email?.from_address) {
            activeCount++;
        }

        countElement.textContent = activeCount;
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (this.modules.utils) {
            this.modules.utils.showToast('success', '成功', message);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (this.modules.utils) {
            this.modules.utils.showToast('error', '错误', message);
        }
    }
}

// 全局函数，供 HTML 调用
window.updateNotificationStatus = function() {
    if (window.notificationMain) {
        window.notificationMain.updateGlobalStatus();
    }
};

window.saveUnifiedNotificationTypes = function() {
    if (window.notificationMain) {
        window.notificationMain.showSuccess('统一通知类型设置已保存');
    }
};

window.enableAllNotifications = function() {
    const toggles = document.querySelectorAll('.notification-type-toggle');
    toggles.forEach(toggle => {
        if (!toggle.checked) {
            toggle.checked = true;
            toggle.dispatchEvent(new Event('change'));
        }
    });
};

window.disableAllNotifications = function() {
    const toggles = document.querySelectorAll('.notification-type-toggle');
    toggles.forEach(toggle => {
        if (toggle.checked) {
            toggle.checked = false;
            toggle.dispatchEvent(new Event('change'));
        }
    });
};

window.enableCriticalOnly = function() {
    const criticalTypes = ['serverOffline', 'trafficLimit'];
    const toggles = document.querySelectorAll('.notification-type-toggle');
    
    toggles.forEach(toggle => {
        const type = toggle.dataset.type;
        const shouldEnable = criticalTypes.includes(type);
        
        if (toggle.checked !== shouldEnable) {
            toggle.checked = shouldEnable;
            toggle.dispatchEvent(new Event('change'));
        }
    });
};

window.resetToDefaults = function() {
    const defaultTypes = ['serverOnline', 'serverOffline', 'trafficLimit', 'testNotification', 'statusSummary'];
    const toggles = document.querySelectorAll('.notification-type-toggle');
    
    toggles.forEach(toggle => {
        const type = toggle.dataset.type;
        const shouldEnable = defaultTypes.includes(type);
        
        if (toggle.checked !== shouldEnable) {
            toggle.checked = shouldEnable;
            toggle.dispatchEvent(new Event('change'));
        }
    });
};

// 初始化主控制器
window.notificationMain = new NotificationMain();
