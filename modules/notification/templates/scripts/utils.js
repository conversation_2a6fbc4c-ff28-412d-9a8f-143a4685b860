/**
 * 通用工具模块
 * 提供全局通用功能和UI组件
 */

class NotificationUtils {
    constructor() {
        this.init();
    }

    /**
     * 初始化工具模块
     */
    init() {
        this.bindGlobalEvents();
        this.initTooltips();
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // ESC 键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // 点击模态框背景关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('fixed') && e.target.classList.contains('inset-0')) {
                this.closeAllModals();
            }
        });
    }

    /**
     * 初始化提示工具
     */
    initTooltips() {
        // 为所有带有 title 属性的元素添加提示
        const elementsWithTitle = document.querySelectorAll('[title]');
        elementsWithTitle.forEach(element => {
            this.addTooltip(element);
        });
    }

    /**
     * 添加提示
     */
    addTooltip(element) {
        let tooltip = null;

        element.addEventListener('mouseenter', () => {
            const title = element.getAttribute('title');
            if (!title) return;

            tooltip = document.createElement('div');
            tooltip.className = 'fixed z-50 px-2 py-1 text-xs bg-slate-800 text-white rounded shadow-lg pointer-events-none';
            tooltip.textContent = title;
            document.body.appendChild(tooltip);

            // 移除 title 属性避免浏览器默认提示
            element.removeAttribute('title');
            element.dataset.originalTitle = title;
        });

        element.addEventListener('mousemove', (e) => {
            if (tooltip) {
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY - 30 + 'px';
            }
        });

        element.addEventListener('mouseleave', () => {
            if (tooltip) {
                tooltip.remove();
                tooltip = null;
            }
            // 恢复 title 属性
            if (element.dataset.originalTitle) {
                element.setAttribute('title', element.dataset.originalTitle);
            }
        });
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.fixed.inset-0');
        modals.forEach(modal => {
            if (modal.id && modal.id.includes('modal')) {
                modal.remove();
            }
        });
    }

    /**
     * 显示全局加载状态
     */
    showGlobalLoading(show) {
        const loading = document.getElementById('global-loading');
        if (loading) {
            if (show) {
                loading.classList.remove('hidden');
            } else {
                loading.classList.add('hidden');
            }
        }
    }

    /**
     * 显示提示消息
     */
    showToast(type, title, message, duration = 5000) {
        const toast = document.getElementById('global-toast');
        const toastIcon = document.getElementById('toast-icon');
        const toastTitle = document.getElementById('toast-title');
        const toastMessage = document.getElementById('toast-message');

        if (!toast || !toastIcon || !toastTitle || !toastMessage) return;

        // 设置图标
        const icons = {
            success: '<i class="ti ti-check text-emerald-500"></i>',
            error: '<i class="ti ti-x text-red-500"></i>',
            warning: '<i class="ti ti-alert-triangle text-amber-500"></i>',
            info: '<i class="ti ti-info-circle text-blue-500"></i>'
        };
        toastIcon.innerHTML = icons[type] || icons.info;

        // 设置内容
        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // 显示提示
        toast.classList.remove('hidden');

        // 自动隐藏
        setTimeout(() => {
            this.hideToast();
        }, duration);
    }

    /**
     * 隐藏提示消息
     */
    hideToast() {
        const toast = document.getElementById('global-toast');
        if (toast) {
            toast.classList.add('hidden');
        }
    }

    /**
     * 切换卡片展开/收起
     */
    toggleCard(cardId) {
        const content = document.getElementById(`${cardId}-content`);
        const chevron = document.getElementById(`${cardId}-chevron`);

        if (content && chevron) {
            const isHidden = content.classList.contains('hidden');
            
            if (isHidden) {
                content.classList.remove('hidden');
                chevron.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                chevron.classList.remove('rotate-180');
            }
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const button = input?.nextElementSibling;
        const icon = button?.querySelector('i');

        if (input && icon) {
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ti ti-eye-off text-lg';
            } else {
                input.type = 'password';
                icon.className = 'ti ti-eye text-lg';
            }
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }

        // 小于1小时
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        }

        // 小于1天
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        }

        // 小于7天
        if (diff < 604800000) {
            return Math.floor(diff / 86400000) + '天前';
        }

        // 超过7天显示具体日期
        return date.toLocaleDateString('zh-CN');
    }

    /**
     * 复制到剪贴板
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('success', '成功', '已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('error', '错误', '复制失败');
        }
    }

    /**
     * 验证邮箱格式
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证URL格式
     */
    validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深度克隆对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 获取查询参数
     */
    getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    /**
     * 设置查询参数
     */
    setQueryParam(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.replaceState({}, '', url);
    }

    /**
     * 滚动到元素
     */
    scrollToElement(elementId, offset = 0) {
        const element = document.getElementById(elementId);
        if (element) {
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    }

    /**
     * 检查元素是否在视口中
     */
    isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
}

// 全局工具实例
window.notificationUtils = new NotificationUtils();

// 全局函数，供 HTML 调用
window.showGlobalLoading = function(show) {
    window.notificationUtils.showGlobalLoading(show);
};

window.showToast = function(type, title, message, duration) {
    window.notificationUtils.showToast(type, title, message, duration);
};

window.hideToast = function() {
    window.notificationUtils.hideToast();
};

window.toggleCard = function(cardId) {
    window.notificationUtils.toggleCard(cardId);
};

window.togglePasswordVisibility = function(inputId) {
    window.notificationUtils.togglePasswordVisibility(inputId);
};

window.copyToClipboard = function(text) {
    window.notificationUtils.copyToClipboard(text);
};
