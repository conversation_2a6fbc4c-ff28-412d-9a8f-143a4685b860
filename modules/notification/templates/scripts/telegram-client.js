/**
 * Telegram 客户端模块
 * 处理 Telegram 相关的前端逻辑
 */

class TelegramClient {
    constructor() {
        this.config = {};
        this.isLoading = false;
        this.init();
    }

    /**
     * 初始化模块
     */
    init() {
        this.bindEvents();
        this.loadConfig();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 启用开关变化
        const enabledCheckbox = document.getElementById('telegram-enabled');
        if (enabledCheckbox) {
            enabledCheckbox.addEventListener('change', (e) => {
                this.onEnabledChange(e.target.checked);
            });
        }

        // Webhook 开关变化
        const webhookCheckbox = document.getElementById('telegram-webhook');
        if (webhookCheckbox) {
            webhookCheckbox.addEventListener('change', (e) => {
                this.onWebhookChange(e.target.checked);
            });
        }

        // 高级设置切换
        const advancedToggle = document.querySelector('[onclick="toggleAdvanced(\'telegram\')"]');
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => {
                this.toggleAdvanced();
            });
        }

        // 表单验证
        this.bindValidation();
    }

    /**
     * 绑定表单验证
     */
    bindValidation() {
        const tokenInput = document.getElementById('telegram-token');
        const chatIdsInput = document.getElementById('telegram-chatids');

        if (tokenInput) {
            tokenInput.addEventListener('blur', () => {
                this.validateToken(tokenInput.value);
            });
        }

        if (chatIdsInput) {
            chatIdsInput.addEventListener('blur', () => {
                this.validateChatIds(chatIdsInput.value);
            });
        }
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const response = await fetch('/admin/notification/telegram/config');
            const result = await response.json();
            
            if (result.code === 1) {
                this.config = result.data;
                this.populateForm();
            } else {
                this.showError('加载配置失败: ' + result.msg);
            }
        } catch (error) {
            console.error('加载 Telegram 配置失败:', error);
            this.showError('加载配置失败');
        }
    }

    /**
     * 填充表单
     */
    populateForm() {
        const elements = {
            'telegram-enabled': this.config.enabled,
            'telegram-token': this.config.token,
            'telegram-chatids': this.config.chatIds?.join(', '),
            'telegram-webhook': this.config.webhook,
            'telegram-webhook-port': this.config.webhookPort,
            'telegram-base-url': this.config.baseApiUrl,
            'telegram-offline-delay': this.config.offlineNotificationDelay,
            'telegram-traffic-thresholds': this.config.trafficThresholds?.join(',')
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = Boolean(value);
                } else {
                    element.value = value || '';
                }
            }
        });

        // 更新 Webhook 端口显示
        this.onWebhookChange(this.config.webhook);
    }

    /**
     * 启用状态变化处理
     */
    onEnabledChange(enabled) {
        this.config.enabled = enabled;
        this.updateStatusDisplay();
    }

    /**
     * Webhook 状态变化处理
     */
    onWebhookChange(enabled) {
        const portSection = document.getElementById('webhook-port-section');
        if (portSection) {
            portSection.style.display = enabled ? 'block' : 'none';
        }
    }

    /**
     * 切换高级设置
     */
    toggleAdvanced() {
        const advancedSection = document.getElementById('telegram-advanced');
        const chevron = document.getElementById('telegram-advanced-chevron');
        
        if (advancedSection && chevron) {
            const isHidden = advancedSection.classList.contains('hidden');
            
            if (isHidden) {
                advancedSection.classList.remove('hidden');
                chevron.classList.add('rotate-90');
            } else {
                advancedSection.classList.add('hidden');
                chevron.classList.remove('rotate-90');
            }
        }
    }

    /**
     * 验证 Token
     */
    async validateToken(token) {
        if (!token) return;

        try {
            const response = await fetch('/admin/notification/telegram/validate-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ token })
            });

            const result = await response.json();
            this.showValidationResult('telegram-token', result.code === 1, result.msg);
        } catch (error) {
            console.error('验证 Token 失败:', error);
        }
    }

    /**
     * 验证 Chat IDs
     */
    async validateChatIds(chatIds) {
        if (!chatIds) return;

        const ids = chatIds.split(',').map(id => id.trim()).filter(id => id);
        let allValid = true;
        let invalidIds = [];

        for (const chatId of ids) {
            try {
                const response = await fetch('/admin/notification/telegram/validate-chatid', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ chatId })
                });

                const result = await response.json();
                if (result.code !== 1) {
                    allValid = false;
                    invalidIds.push(chatId);
                }
            } catch (error) {
                console.error('验证 Chat ID 失败:', error);
                allValid = false;
                invalidIds.push(chatId);
            }
        }

        const message = allValid ? 'Chat IDs 格式有效' : `无效的 Chat IDs: ${invalidIds.join(', ')}`;
        this.showValidationResult('telegram-chatids', allValid, message);
    }

    /**
     * 显示验证结果
     */
    showValidationResult(elementId, isValid, message) {
        const element = document.getElementById(elementId);
        if (!element) return;

        // 移除之前的验证样式
        element.classList.remove('border-green-500', 'border-red-500');
        
        // 添加新的验证样式
        if (isValid) {
            element.classList.add('border-green-500');
        } else {
            element.classList.add('border-red-500');
        }

        // 显示验证消息
        this.showToast(isValid ? 'success' : 'error', message);
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading(true);

            const formData = this.collectFormData();
            
            const response = await fetch('/admin/notification/telegram/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.config = formData;
                this.showSuccess('Telegram 配置保存成功');
                this.updateStatusDisplay();
            } else {
                this.showError('保存失败: ' + result.msg);
                if (result.errors) {
                    console.error('验证错误:', result.errors);
                }
            }
        } catch (error) {
            console.error('保存 Telegram 配置失败:', error);
            this.showError('保存配置失败');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const chatIds = document.getElementById('telegram-chatids').value;
        const thresholds = document.getElementById('telegram-traffic-thresholds').value;

        return {
            enabled: document.getElementById('telegram-enabled').checked,
            token: document.getElementById('telegram-token').value.trim(),
            chatIds: chatIds ? chatIds.split(',').map(id => id.trim()).filter(id => id) : [],
            webhook: document.getElementById('telegram-webhook').checked,
            webhookPort: parseInt(document.getElementById('telegram-webhook-port').value) || 3001,
            baseApiUrl: document.getElementById('telegram-base-url').value.trim(),
            offlineNotificationDelay: parseInt(document.getElementById('telegram-offline-delay').value) || 300,
            trafficThresholds: thresholds ? thresholds.split(',').map(t => parseFloat(t.trim())).filter(t => !isNaN(t)) : [80, 90, 95]
        };
    }

    /**
     * 发送测试通知
     */
    async testNotification() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading(true);

            const response = await fetch('/admin/notification/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess('测试通知发送成功');
            } else {
                this.showError('测试通知发送失败: ' + result.msg);
            }
        } catch (error) {
            console.error('发送测试通知失败:', error);
            this.showError('发送测试通知失败');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        // 这个方法会被全局的状态更新函数调用
        if (window.updateNotificationStatus) {
            window.updateNotificationStatus();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        if (window.showGlobalLoading) {
            window.showGlobalLoading(show);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.showToast) {
            window.showToast('success', '成功', message);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.showToast) {
            window.showToast('error', '错误', message);
        }
    }

    /**
     * 显示提示消息
     */
    showToast(type, message) {
        if (window.showToast) {
            window.showToast(type, type === 'success' ? '成功' : '提示', message);
        }
    }
}

// 全局函数，供 HTML 调用
window.saveTelegramConfig = function() {
    if (window.telegramClient) {
        window.telegramClient.saveConfig();
    }
};

window.testTelegramNotification = function() {
    if (window.telegramClient) {
        window.telegramClient.testNotification();
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    window.telegramClient = new TelegramClient();
});
