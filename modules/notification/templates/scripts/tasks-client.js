/**
 * 任务管理客户端模块
 * 处理自定义通知任务相关的前端逻辑
 */

class TasksClient {
    constructor() {
        this.tasks = [];
        this.servers = [];
        this.filteredTasks = [];
        this.selectedTasks = new Set();
        this.isLoading = false;
        this.init();
    }

    /**
     * 初始化模块
     */
    init() {
        this.bindEvents();
        this.loadData();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 创建任务表单提交
        const createForm = document.getElementById('create-task-form');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createTask();
            });
        }

        // 过滤器变化
        ['task-filter-server', 'task-filter-status', 'task-filter-period'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.filterTasks());
            }
        });

        // 全选复选框
        const selectAllCheckbox = document.getElementById('select-all-tasks');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAllTasks(e.target.checked);
            });
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        await Promise.all([
            this.loadTasks(),
            this.loadServers()
        ]);
        this.updateDisplay();
    }

    /**
     * 加载任务列表
     */
    async loadTasks() {
        try {
            const response = await fetch('/admin/notification/tasks');
            const result = await response.json();
            
            if (result.code === 1) {
                this.tasks = result.data;
                this.filteredTasks = [...this.tasks];
            } else {
                this.showError('加载任务列表失败: ' + result.msg);
            }
        } catch (error) {
            console.error('加载任务列表失败:', error);
            this.showError('加载任务列表失败');
        }
    }

    /**
     * 加载服务器列表
     */
    async loadServers() {
        try {
            const response = await fetch('/admin/notification/servers');
            const result = await response.json();
            
            if (result.code === 1) {
                this.servers = result.data;
                this.populateServerSelects();
            } else {
                this.showError('加载服务器列表失败: ' + result.msg);
            }
        } catch (error) {
            console.error('加载服务器列表失败:', error);
            this.showError('加载服务器列表失败');
        }
    }

    /**
     * 填充服务器选择框
     */
    populateServerSelects() {
        const selects = ['task-filter-server', 'task-server'];
        
        selects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                // 清空现有选项（保留第一个默认选项）
                const firstOption = select.firstElementChild;
                select.innerHTML = '';
                if (firstOption) {
                    select.appendChild(firstOption);
                }

                // 添加服务器选项
                this.servers.forEach(server => {
                    const option = document.createElement('option');
                    option.value = server.sid;
                    option.textContent = `${server.name || server.sid} (${server.sid})`;
                    select.appendChild(option);
                });
            }
        });
    }

    /**
     * 过滤任务
     */
    filterTasks() {
        const serverFilter = document.getElementById('task-filter-server').value;
        const statusFilter = document.getElementById('task-filter-status').value;
        const periodFilter = document.getElementById('task-filter-period').value;

        this.filteredTasks = this.tasks.filter(task => {
            if (serverFilter && task.sid !== serverFilter) return false;
            if (statusFilter === 'enabled' && !task.enabled) return false;
            if (statusFilter === 'disabled' && task.enabled) return false;
            if (periodFilter && task.period !== periodFilter) return false;
            return true;
        });

        this.renderTasksList();
        this.updateBatchOperationsVisibility();
    }

    /**
     * 清除过滤器
     */
    clearFilters() {
        document.getElementById('task-filter-server').value = '';
        document.getElementById('task-filter-status').value = '';
        document.getElementById('task-filter-period').value = '';
        this.filterTasks();
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        this.updateStats();
        this.renderTasksList();
        this.updateStatusDisplay();
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const totalTasks = this.tasks.length;
        const enabledTasks = this.tasks.filter(t => t.enabled).length;
        const disabledTasks = totalTasks - enabledTasks;
        const serverCount = new Set(this.tasks.map(t => t.sid)).size;

        document.getElementById('total-tasks').textContent = totalTasks;
        document.getElementById('enabled-tasks').textContent = enabledTasks;
        document.getElementById('disabled-tasks').textContent = disabledTasks;
        document.getElementById('server-count').textContent = serverCount;
    }

    /**
     * 渲染任务列表
     */
    renderTasksList() {
        const container = document.getElementById('tasks-list');
        const noTasksMessage = document.getElementById('no-tasks-message');
        
        if (this.filteredTasks.length === 0) {
            container.innerHTML = '';
            if (noTasksMessage) {
                noTasksMessage.style.display = 'block';
            }
            return;
        }

        if (noTasksMessage) {
            noTasksMessage.style.display = 'none';
        }

        const tasksHtml = this.filteredTasks.map(task => this.renderTaskItem(task)).join('');
        container.innerHTML = tasksHtml;
    }

    /**
     * 渲染单个任务项
     */
    renderTaskItem(task) {
        const server = this.servers.find(s => s.sid === task.sid);
        const serverName = server ? (server.name || server.sid) : task.sid;
        const thresholds = Array.isArray(task.thresholds) ? task.thresholds.join(', ') : '';
        
        return `
            <div class="task-item p-4 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <input type="checkbox" class="task-checkbox rounded border-slate-300 dark:border-slate-600 text-amber-600 focus:ring-amber-500" 
                               data-task-id="${task.id}" onchange="window.tasksClient.onTaskSelect('${task.id}', this.checked)">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-1">
                                <h4 class="text-sm font-medium text-slate-800 dark:text-slate-200">${serverName}</h4>
                                <span class="px-2 py-0.5 text-xs rounded-full ${task.enabled ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400' : 'bg-slate-100 text-slate-600 dark:bg-slate-800 dark:text-slate-400'}">
                                    ${task.enabled ? '已启用' : '已禁用'}
                                </span>
                                <span class="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                                    ${this.getPeriodText(task.period)}
                                </span>
                            </div>
                            <div class="text-xs text-slate-500 dark:text-slate-400">
                                <span>方向: ${this.getDirectionText(task.direction)}</span>
                                ${task.traffic_limit ? ` | 限制: ${task.traffic_limit}GB` : ''}
                                ${thresholds ? ` | 阈值: ${thresholds}%` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="window.tasksClient.toggleTask('${task.id}')" 
                                class="px-2 py-1 text-xs rounded ${task.enabled ? 'bg-slate-100 text-slate-600 hover:bg-slate-200 dark:bg-slate-700 dark:text-slate-400 dark:hover:bg-slate-600' : 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-400 dark:hover:bg-emerald-900/50'}">
                            ${task.enabled ? '禁用' : '启用'}
                        </button>
                        <button onclick="window.tasksClient.editTask('${task.id}')" 
                                class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50">
                            编辑
                        </button>
                        <button onclick="window.tasksClient.deleteTask('${task.id}')" 
                                class="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取周期文本
     */
    getPeriodText(period) {
        const periodMap = {
            'daily': '每日',
            'weekly': '每周',
            'monthly': '每月'
        };
        return periodMap[period] || period;
    }

    /**
     * 获取方向文本
     */
    getDirectionText(direction) {
        const directionMap = {
            'both': '双向',
            'in': '入站',
            'out': '出站',
            'max': '最大值'
        };
        return directionMap[direction] || direction;
    }

    /**
     * 创建任务
     */
    async createTask() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading(true);

            const formData = this.collectCreateFormData();
            
            const response = await fetch('/admin/notification/tasks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess('任务创建成功');
                this.hideCreateTaskModal();
                await this.loadTasks();
                this.updateDisplay();
            } else {
                this.showError('创建任务失败: ' + result.msg);
                if (result.errors) {
                    console.error('验证错误:', result.errors);
                }
            }
        } catch (error) {
            console.error('创建任务失败:', error);
            this.showError('创建任务失败');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 收集创建表单数据
     */
    collectCreateFormData() {
        const thresholds = document.getElementById('task-thresholds').value;
        const trafficLimit = document.getElementById('task-traffic-limit').value;
        const resetDay = document.getElementById('task-reset-day').value;

        return {
            sid: document.getElementById('task-server').value,
            period: document.getElementById('task-period').value,
            direction: document.getElementById('task-direction').value,
            traffic_limit: trafficLimit ? parseFloat(trafficLimit) : null,
            thresholds: thresholds ? thresholds.split(',').map(t => parseFloat(t.trim())).filter(t => !isNaN(t)) : [80, 90, 95],
            reset_day: resetDay ? parseInt(resetDay) : null,
            enabled: document.getElementById('task-enabled').checked,
            type: 'traffic'
        };
    }

    /**
     * 切换任务状态
     */
    async toggleTask(taskId) {
        try {
            const response = await fetch('/admin/notification/tasks/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: taskId })
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess(result.msg);
                await this.loadTasks();
                this.updateDisplay();
            } else {
                this.showError('切换任务状态失败: ' + result.msg);
            }
        } catch (error) {
            console.error('切换任务状态失败:', error);
            this.showError('切换任务状态失败');
        }
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId) {
        if (!confirm('确定要删除这个任务吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/admin/notification/tasks/${taskId}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess('任务删除成功');
                await this.loadTasks();
                this.updateDisplay();
            } else {
                this.showError('删除任务失败: ' + result.msg);
            }
        } catch (error) {
            console.error('删除任务失败:', error);
            this.showError('删除任务失败');
        }
    }

    /**
     * 任务选择处理
     */
    onTaskSelect(taskId, selected) {
        if (selected) {
            this.selectedTasks.add(taskId);
        } else {
            this.selectedTasks.delete(taskId);
        }
        this.updateSelectedCount();
        this.updateBatchOperationsVisibility();
    }

    /**
     * 切换全选
     */
    toggleSelectAllTasks(selectAll) {
        const checkboxes = document.querySelectorAll('.task-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.onTaskSelect(checkbox.dataset.taskId, selectAll);
        });
    }

    /**
     * 更新选中数量
     */
    updateSelectedCount() {
        const countElement = document.getElementById('selected-count');
        if (countElement) {
            countElement.textContent = `已选择 ${this.selectedTasks.size} 个任务`;
        }
    }

    /**
     * 更新批量操作可见性
     */
    updateBatchOperationsVisibility() {
        const batchOps = document.getElementById('batch-operations');
        if (batchOps) {
            batchOps.style.display = this.selectedTasks.size > 0 ? 'flex' : 'none';
        }
    }

    /**
     * 显示创建任务模态框
     */
    showCreateTaskModal() {
        const modal = document.getElementById('create-task-modal');
        if (modal) {
            modal.classList.remove('hidden');
            // 重置表单
            document.getElementById('create-task-form').reset();
            document.getElementById('task-enabled').checked = true;
        }
    }

    /**
     * 隐藏创建任务模态框
     */
    hideCreateTaskModal() {
        const modal = document.getElementById('create-task-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        const statusBadge = document.querySelector('.tasks-status-badge');
        const statusDot = document.querySelector('.tasks-status-dot');
        const statusText = document.querySelector('.tasks-status-text');

        if (statusBadge && statusDot && statusText) {
            const enabledCount = this.tasks.filter(t => t.enabled).length;
            const totalCount = this.tasks.length;
            
            if (totalCount > 0) {
                statusBadge.className = 'inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-amber-600 border-amber-300 bg-amber-50 dark:text-amber-400 dark:border-amber-700/40 dark:bg-amber-900/20';
                statusDot.className = 'w-2 h-2 rounded-full bg-amber-500';
                statusText.textContent = `${enabledCount}/${totalCount}个任务`;
            } else {
                statusBadge.className = 'inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30';
                statusDot.className = 'w-2 h-2 rounded-full bg-slate-400';
                statusText.textContent = '0个任务';
            }
        }

        // 更新全局状态
        if (window.updateNotificationStatus) {
            window.updateNotificationStatus();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        if (window.showGlobalLoading) {
            window.showGlobalLoading(show);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.showToast) {
            window.showToast('success', '成功', message);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.showToast) {
            window.showToast('error', '错误', message);
        }
    }
}

// 全局函数，供 HTML 调用
window.showCreateTaskModal = function() {
    if (window.tasksClient) {
        window.tasksClient.showCreateTaskModal();
    }
};

window.hideCreateTaskModal = function() {
    if (window.tasksClient) {
        window.tasksClient.hideCreateTaskModal();
    }
};

window.filterTasks = function() {
    if (window.tasksClient) {
        window.tasksClient.filterTasks();
    }
};

window.clearTaskFilters = function() {
    if (window.tasksClient) {
        window.tasksClient.clearFilters();
    }
};

window.refreshTasks = function() {
    if (window.tasksClient) {
        window.tasksClient.loadData();
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    window.tasksClient = new TasksClient();
});
