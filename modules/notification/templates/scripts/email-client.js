/**
 * 邮件客户端模块
 * 处理邮件相关的前端逻辑
 */

class EmailClient {
    constructor() {
        this.config = {};
        this.isLoading = false;
        this.init();
    }

    /**
     * 初始化模块
     */
    init() {
        this.bindEvents();
        this.loadConfig();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 启用开关变化
        const enabledCheckbox = document.getElementById('email-enabled');
        if (enabledCheckbox) {
            enabledCheckbox.addEventListener('change', (e) => {
                this.onEnabledChange(e.target.checked);
            });
        }

        // 表单验证
        this.bindValidation();
    }

    /**
     * 绑定表单验证
     */
    bindValidation() {
        const emailInputs = [
            'email-from-address',
            'email-to-addresses',
            'email-cc-addresses',
            'email-bcc-addresses'
        ];

        emailInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('blur', () => {
                    this.validateEmailField(id, element.value);
                });
            }
        });

        // SMTP 连接测试
        const hostInput = document.getElementById('email-host');
        const portInput = document.getElementById('email-port');
        if (hostInput && portInput) {
            [hostInput, portInput].forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateSmtpConnection();
                });
            });
        }
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const response = await fetch('/admin/notification/email/config');
            const result = await response.json();
            
            if (result.code === 1) {
                this.config = result.data;
                this.populateForm();
                this.updateStatusDisplay();
            } else {
                this.showError('加载配置失败: ' + result.msg);
            }
        } catch (error) {
            console.error('加载邮件配置失败:', error);
            this.showError('加载配置失败');
        }
    }

    /**
     * 填充表单
     */
    populateForm() {
        const elements = {
            'email-enabled': this.config.enabled,
            'email-host': this.config.host,
            'email-port': this.config.port,
            'email-secure': this.config.secure,
            'email-auth-user': this.config.auth_user,
            'email-auth-pass': this.config.auth_pass,
            'email-from-address': this.config.from_address,
            'email-from-name': this.config.from_name,
            'email-to-addresses': this.config.to_addresses?.join(', '),
            'email-cc-addresses': this.config.cc_addresses?.join(', '),
            'email-bcc-addresses': this.config.bcc_addresses?.join(', ')
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = Boolean(value);
                } else {
                    element.value = value || '';
                }
            }
        });
    }

    /**
     * 启用状态变化处理
     */
    onEnabledChange(enabled) {
        this.config.enabled = enabled;
        this.updateStatusDisplay();
    }

    /**
     * 验证邮箱字段
     */
    validateEmailField(fieldId, value) {
        if (!value) return;

        const emails = value.split(',').map(email => email.trim()).filter(email => email);
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        let allValid = true;
        let invalidEmails = [];

        emails.forEach(email => {
            if (!emailRegex.test(email)) {
                allValid = false;
                invalidEmails.push(email);
            }
        });

        const element = document.getElementById(fieldId);
        if (element) {
            element.classList.remove('border-green-500', 'border-red-500');
            
            if (allValid) {
                element.classList.add('border-green-500');
            } else {
                element.classList.add('border-red-500');
                this.showError(`无效的邮箱地址: ${invalidEmails.join(', ')}`);
            }
        }
    }

    /**
     * 验证 SMTP 连接
     */
    async validateSmtpConnection() {
        const host = document.getElementById('email-host').value;
        const port = document.getElementById('email-port').value;
        const user = document.getElementById('email-auth-user').value;
        const pass = document.getElementById('email-auth-pass').value;

        if (!host || !port || !user || !pass) return;

        try {
            const response = await fetch('/admin/notification/email/validate-smtp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    host,
                    port: parseInt(port),
                    auth_user: user,
                    auth_pass: pass,
                    secure: document.getElementById('email-secure').checked
                })
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess('SMTP 连接验证成功');
            } else {
                this.showError('SMTP 连接验证失败: ' + result.msg);
            }
        } catch (error) {
            console.error('验证 SMTP 连接失败:', error);
        }
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading(true);

            const formData = this.collectFormData();
            
            const response = await fetch('/admin/notification/email/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.config = formData;
                this.showSuccess('邮件配置保存成功');
                this.updateStatusDisplay();
            } else {
                this.showError('保存失败: ' + result.msg);
                if (result.errors) {
                    console.error('验证错误:', result.errors);
                }
            }
        } catch (error) {
            console.error('保存邮件配置失败:', error);
            this.showError('保存配置失败');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const toAddresses = document.getElementById('email-to-addresses').value;
        const ccAddresses = document.getElementById('email-cc-addresses').value;
        const bccAddresses = document.getElementById('email-bcc-addresses').value;

        return {
            enabled: document.getElementById('email-enabled').checked,
            host: document.getElementById('email-host').value.trim(),
            port: parseInt(document.getElementById('email-port').value) || 587,
            secure: document.getElementById('email-secure').checked,
            auth_user: document.getElementById('email-auth-user').value.trim(),
            auth_pass: document.getElementById('email-auth-pass').value,
            from_address: document.getElementById('email-from-address').value.trim(),
            from_name: document.getElementById('email-from-name').value.trim(),
            to_addresses: toAddresses ? toAddresses.split(',').map(email => email.trim()).filter(email => email) : [],
            cc_addresses: ccAddresses ? ccAddresses.split(',').map(email => email.trim()).filter(email => email) : [],
            bcc_addresses: bccAddresses ? bccAddresses.split(',').map(email => email.trim()).filter(email => email) : []
        };
    }

    /**
     * 发送测试邮件
     */
    async testEmail() {
        if (this.isLoading) return;

        try {
            this.isLoading = true;
            this.showLoading(true);

            const response = await fetch('/admin/notification/email/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            const result = await response.json();
            
            if (result.code === 1) {
                this.showSuccess('测试邮件发送成功');
            } else {
                this.showError('测试邮件发送失败: ' + result.msg);
            }
        } catch (error) {
            console.error('发送测试邮件失败:', error);
            this.showError('发送测试邮件失败');
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    /**
     * 显示邮件日志
     */
    async showLogs() {
        try {
            const response = await fetch('/admin/notification/email/logs?page=1&pageSize=20');
            const result = await response.json();
            
            if (result.code === 1) {
                this.displayLogsModal(result.data);
            } else {
                this.showError('获取邮件日志失败: ' + result.msg);
            }
        } catch (error) {
            console.error('获取邮件日志失败:', error);
            this.showError('获取邮件日志失败');
        }
    }

    /**
     * 显示日志模态框
     */
    displayLogsModal(logs) {
        // 创建模态框HTML
        const modalHtml = `
            <div id="email-logs-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white dark:bg-slate-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
                    <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">邮件发送日志</h3>
                            <button onclick="this.closest('#email-logs-modal').remove()" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300">
                                <i class="ti ti-x text-xl"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 overflow-y-auto max-h-[60vh]">
                        <div class="space-y-3">
                            ${logs.list?.map(log => `
                                <div class="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-slate-700 dark:text-slate-300">${log.subject || '无主题'}</span>
                                        <span class="text-xs text-slate-500 dark:text-slate-400">${log.created_at}</span>
                                    </div>
                                    <div class="text-xs text-slate-600 dark:text-slate-400">
                                        <div>收件人: ${log.to_addresses || '无'}</div>
                                        <div>状态: <span class="${log.status === 'success' ? 'text-green-600' : 'text-red-600'}">${log.status === 'success' ? '成功' : '失败'}</span></div>
                                        ${log.error ? `<div>错误: ${log.error}</div>` : ''}
                                    </div>
                                </div>
                            `).join('') || '<div class="text-center text-slate-500 dark:text-slate-400">暂无日志记录</div>'}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        const statusBadge = document.querySelector('.email-status-badge');
        const statusDot = document.querySelector('.email-status-dot');
        const statusText = document.querySelector('.email-status-text');

        if (statusBadge && statusDot && statusText) {
            const isConfigured = this.config.enabled && this.config.from_address && this.config.to_addresses?.length > 0;
            
            if (isConfigured) {
                statusBadge.className = 'inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-emerald-600 border-emerald-300 bg-emerald-50 dark:text-emerald-400 dark:border-emerald-700/40 dark:bg-emerald-900/20';
                statusDot.className = 'w-2 h-2 rounded-full bg-emerald-500';
                statusText.textContent = '已配置';
            } else {
                statusBadge.className = 'inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full border text-slate-500 border-slate-300 bg-slate-50 dark:text-slate-400 dark:border-slate-600/40 dark:bg-slate-800/30';
                statusDot.className = 'w-2 h-2 rounded-full bg-slate-400';
                statusText.textContent = '未配置';
            }
        }

        // 更新全局状态
        if (window.updateNotificationStatus) {
            window.updateNotificationStatus();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        if (window.showGlobalLoading) {
            window.showGlobalLoading(show);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.showToast) {
            window.showToast('success', '成功', message);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.showToast) {
            window.showToast('error', '错误', message);
        }
    }
}

// 全局函数，供 HTML 调用
window.saveEmailConfig = function() {
    if (window.emailClient) {
        window.emailClient.saveConfig();
    }
};

window.testEmail = function() {
    if (window.emailClient) {
        window.emailClient.testEmail();
    }
};

window.showEmailLogs = function() {
    if (window.emailClient) {
        window.emailClient.showLogs();
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    window.emailClient = new EmailClient();
});
