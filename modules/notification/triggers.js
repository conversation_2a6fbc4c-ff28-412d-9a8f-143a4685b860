"use strict";

const { logger } = require('../utils/logger');

/**
 * 通知触发器类 - 统一管理所有通知条件检测
 * 职责：检测各种通知条件，但不负责具体的通知发送逻辑
 */
class NotificationTriggers {
  constructor(notificationManager, database) {
    this.notification = notificationManager;
    this.db = database;
        
        // 服务器状态缓存
        this.serverStatusCache = {};
        
    // 流量状态缓存 - 避免重复流量通知
    this.trafficStatusCache = {};
    // 流量阈值通知缓存 - 记录每个服务器已通知到的最高阈值百分比
    this.trafficThresholdCache = {};
        
        // 延迟离线通知管理器
        this.offlineNotificationTimers = new Map();
        
        // 系统初始化阶段标志
        this.initialStatusCollectionComplete = false;
        
        // 系统启动时间（用于优雅期判断）
        this.SYSTEM_START_TIME = Date.now();
        
        // 常量配置
        this.INITIALIZATION_PERIOD = 10 * 1000; // 10秒初始化期（缩短以便更快响应）
        this.STARTUP_GRACE_PERIOD = 2 * 60 * 1000; // 2分钟启动缓冲期（缩短以便更快响应）
        
        // 初始化定时器
        this.setupInitializationTimer();
        
        logger.info('[通知触发器] 已初始化');
    }
    
    /**
     * 设置初始化定时器
     */
    setupInitializationTimer() {
        setTimeout(() => {
            this.initialStatusCollectionComplete = true;
            logger.info('[通知触发器] 初始化阶段完成，开始发送状态变化通知');
        }, this.INITIALIZATION_PERIOD);
    }
    
    /**
     * 初始化服务器状态
     */
    async initializeServerStates() {
        try {
            const servers = await this.db.getServers();

            for (const server of servers) {
                if (server.status > 0) {
                    // 获取服务器当前实际状态
                    const currentStatus = await this.getCurrentServerStatus(server.sid);

                    // 初始化状态缓存为当前实际状态
                    this.serverStatusCache[server.sid] = currentStatus;

                    logger.debug(`[通知触发器] 初始化服务器状态: ${server.name} = ${currentStatus}`);
                }
            }

            logger.info(`[通知触发器] 已初始化 ${servers.length} 个服务器的状态缓存`);
        } catch (error) {
            logger.error('[通知触发器] 初始化服务器状态失败:', error);
        }
    }

    /**
     * 获取服务器当前状态
     * @param {string} sid - 服务器ID
     * @returns {boolean|null} 服务器状态，null表示未知
     */
    async getCurrentServerStatus(sid) {
        try {
            // 尝试从stats模块获取当前状态
            // 这里需要访问全局stats对象，但为了避免循环依赖，我们使用更安全的方法

            // 方法1：检查数据库中的最后在线时间
            const server = await this.db.servers.get(sid);
            if (!server) return null;

            const now = Math.floor(Date.now() / 1000);
            const lastOnline = server.last_online || 0;

            // 如果最后在线时间在5分钟内，认为可能在线
            // 否则认为可能离线，但这只是初始猜测
            const isRecentlyOnline = (now - lastOnline) < 300; // 5分钟

            return isRecentlyOnline ? true : null; // 返回null表示状态未确定，需要等待实际检测
        } catch (error) {
            logger.error(`[通知触发器] 获取服务器 ${sid} 当前状态失败:`, error);
            return null;
        }
    }
    
    /**
     * 检查服务器状态变化并触发通知
     * @param {string} sid - 服务器ID
     * @param {Object} server - 服务器信息
     * @param {boolean} isOnline - 当前是否在线
     * @param {Object} stats - 统计数据（可选）
     */
    async checkServerStatusChange(sid, server, isOnline, stats = null) {
        try {
            const oldStatus = this.serverStatusCache[sid];
            const newStatus = isOnline;

            // 如果这是第一次检测到状态，初始化缓存但不发送通知
            if (oldStatus === undefined) {
                this.serverStatusCache[sid] = newStatus;
                logger.debug(`[通知触发器] 首次检测服务器状态: ${server.name} = ${newStatus}`);
                return;
            }

            // 避免重复通知：如果状态没有变化，直接返回
            if (oldStatus === newStatus) {
                return;
            }

            // 更新状态缓存
            this.serverStatusCache[sid] = newStatus;

            // 判断是否需要发送通知
            const isInitialPeriod = !this.initialStatusCollectionComplete;
            const timeSinceStartup = Date.now() - this.SYSTEM_START_TIME;
            const isAfterGracePeriod = timeSinceStartup > this.STARTUP_GRACE_PERIOD;

            logger.debug(`[通知触发器] 服务器状态变化: ${server.name}, ${oldStatus} -> ${newStatus}, 初始化期: ${isInitialPeriod}, 缓冲期: ${!isAfterGracePeriod}`);

            if (isOnline) {
                // 服务器上线
                // 修复：只要oldStatus明确为false，就应该发送恢复通知（除非在初始化期）
                const shouldNotifyOnline = !isInitialPeriod && (oldStatus === false || oldStatus === null);

                if (shouldNotifyOnline) {
                    logger.info(`[通知触发器] 触发服务器恢复通知: ${server.name}`);
                    await this.handleServerOnlineNotification(sid, server);
                }
            } else {
                // 服务器下线
                // 修复：只要oldStatus明确为true或null（可能在线），就应该发送离线通知
                // 移除启动缓冲期限制，因为它会阻止正常的离线通知
                const shouldNotifyOffline = !isInitialPeriod && (oldStatus === true || oldStatus === null);

                if (shouldNotifyOffline) {
                    logger.info(`[通知触发器] 触发服务器离线通知: ${server.name}`);
                    await this.handleServerOfflineNotification(sid, server);
                }
            }
        } catch (error) {
            logger.error(`[通知触发器] 检查服务器状态变化失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 处理服务器上线通知
     */
    async handleServerOnlineNotification(sid, server) {
        try {
            const telegramSetting = await this.db.setting.get('telegram');
            
            if (telegramSetting?.enabled && 
                telegramSetting?.chatIds?.length > 0 && 
                telegramSetting?.notificationTypes?.serverOnline) {
                
                const message = `#恢复 ${server.name} ${new Date().toLocaleString()}`;
                logger.info(`[通知触发器] 发送服务器恢复通知: ${server.name}`);
                
                // 清理待发送的离线通知
                if (this.offlineNotificationTimers.has(sid)) {
                    clearTimeout(this.offlineNotificationTimers.get(sid));
                    this.offlineNotificationTimers.delete(sid);
                    logger.info(`[通知触发器] 服务器恢复，已取消待发送离线通知: ${server.name}`);
                }
                
                // 发送恢复通知（包含重试机制）
                await this.sendNotificationWithRetry('服务器恢复', message, telegramSetting.chatIds, server.name);
            }
        } catch (error) {
            logger.error(`[通知触发器] 处理服务器上线通知失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 处理服务器下线通知
     */
    async handleServerOfflineNotification(sid, server) {
        try {
            const telegramSetting = await this.db.setting.get('telegram');
            
            if (telegramSetting?.enabled && 
                telegramSetting?.chatIds?.length > 0 && 
                telegramSetting?.notificationTypes?.serverOffline) {
                
                const message = `#掉线 ${server.name} ${new Date().toLocaleString()}`;
                logger.info(`[通知触发器] 发送服务器掉线通知(状态从在线变为离线): ${server.name}`);
                
                // 使用延迟通知机制
                await this.sendDelayedOfflineNotification(sid, server.name, message, telegramSetting);
            }
        } catch (error) {
            logger.error(`[通知触发器] 处理服务器下线通知失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 发送延迟的离线通知
     * @param {string} sid - 服务器ID
     * @param {string} serverName - 服务器名称
     * @param {string} message - 通知消息
     * @param {Object} telegramSetting - Telegram配置
     */
    async sendDelayedOfflineNotification(sid, serverName, message, telegramSetting) {
        // 如果有待发送的通知，先清除
        if (this.offlineNotificationTimers.has(sid)) {
            clearTimeout(this.offlineNotificationTimers.get(sid));
            this.offlineNotificationTimers.delete(sid);
        }
        
        // 读取延迟配置（5-300秒，默认30秒）
        const delay = telegramSetting.offlineNotificationDelay || 30;
        logger.info(`[通知触发器] 设置${delay}秒延迟通知: ${serverName}`);
        
        // 设置延迟通知
        const timer = setTimeout(async () => {
            // 延迟期满后，再次检查服务器状态
            if (this.serverStatusCache[sid] === false) {
                // 服务器仍然离线，发送通知
                logger.info(`[通知触发器] 延迟通知发送: ${serverName}`);
                await this.sendNotificationWithRetry('服务器掉线', message, telegramSetting.chatIds, serverName);
            } else {
                logger.info(`[通知触发器] 服务器已恢复，取消延迟通知: ${serverName}`);
            }
            this.offlineNotificationTimers.delete(sid);
        }, delay * 1000);
        
        this.offlineNotificationTimers.set(sid, timer);
    }
    
    /**
     * 检查流量超限并触发通知
     * @param {string} sid - 服务器ID
     * @param {Object} server - 服务器信息
     * @param {Object} trafficStats - 流量统计数据
     */
  async checkTrafficLimit(sid, server, trafficStats) {
    try {
            if (!server.traffic_limit || server.traffic_limit <= 0) {
                return;
            }

            // 读取Telegram设置与阈值列表
            const telegramSetting = await this.db.setting.get('telegram');
            if (!(telegramSetting?.enabled && telegramSetting?.notificationTypes?.trafficLimit)) {
                return;
            }

            // 解析阈值配置：优先使用全局trafficThresholds，其次回退到服务器单阈值
            const parseNum = (v) => {
                const n = parseInt(v, 10);
                return isNaN(n) ? null : n;
            };
            let thresholds = Array.isArray(telegramSetting.trafficThresholds)
                ? telegramSetting.trafficThresholds.map(parseNum).filter(v => v !== null)
                : [];
            if (!thresholds.length) {
                const single = parseNum(server.traffic_alert_percent || 80);
                thresholds = single ? [single] : [80];
            }
            thresholds = [...new Set(thresholds)]
                .filter(v => v > 0 && v < 100)
                .sort((a, b) => a - b);
            if (!thresholds.length) return;

            const ratio = Number(trafficStats?.ratio || 0);
            // 周期复位：当使用比例低于最小阈值且曾发过通知时，清空已通知阈值
            if (ratio < thresholds[0] && this.trafficThresholdCache[sid] > 0) {
                this.trafficThresholdCache[sid] = 0;
            }

            const lastNotified = this.trafficThresholdCache[sid] || 0;
            const nextThreshold = thresholds.find(t => t > lastNotified && ratio >= t);

            if (nextThreshold) {
                const usedGB = (Number(trafficStats.used || 0) / 1024 / 1024 / 1024).toFixed(2);
                const limitGB = (Number(trafficStats.limit || 0) / 1024 / 1024 / 1024).toFixed(2);
                const message = `#流量告警 ${server.name} 已达${nextThreshold}% (${usedGB}GB/${limitGB}GB)`;
                await this.sendNotificationWithRetry('流量超限', message, telegramSetting.chatIds, server.name);
                logger.info(`[通知触发器] 流量阈值(${nextThreshold}%)通知已发送: ${server.name}`);
                this.trafficThresholdCache[sid] = nextThreshold;
            }

            // 同步旧的状态缓存逻辑（保持兼容，不作为通知触发条件）
            this.trafficStatusCache[sid] = trafficStats.status;
            
        } catch (error) {
            logger.error(`[通知触发器] 流量超限检测失败 ${server.name}:`, error);
        }
    }
    
    /**
     * 带重试机制的通知发送
     * @param {string} title - 通知标题
     * @param {string} message - 通知消息
     * @param {Array} chatIds - 接收者列表
     * @param {string} serverName - 服务器名称（用于日志）
     * @param {number} maxRetries - 最大重试次数
     */
    async sendNotificationWithRetry(title, message, chatIds, serverName, maxRetries = 3) {
        let success = false;
        let retryCount = 0;
        
        while (!success && retryCount < maxRetries) {
            try {
                const notificationResult = await this.notification.sendNotification(title, message, chatIds);
                
                if (notificationResult.success) {
                    success = true;
                    logger.info(`[通知触发器] 通知发送成功: ${serverName}`);
                } else {
                    throw new Error(notificationResult.error || '通知系统返回失败');
                }
            } catch (error) {
                retryCount++;
                if (retryCount < maxRetries) {
                    logger.warn(`[通知触发器] 通知发送失败(尝试 ${retryCount}/${maxRetries}): ${serverName}`, error.message);
                    await this.sleep(2000 * retryCount); // 递增延迟
                } else {
                    logger.error(`[通知触发器] 通知发送失败，已达到最大重试次数: ${serverName}`, error.message);
                }
            }
        }
        
        return success;
    }
    
    /**
     * 清理已删除服务器的缓存
     * @param {Set} activeServerIds - 当前活动的服务器ID集合
     */
    cleanupCache(activeServerIds) {
        // 清理状态缓存
        for (const sid in this.serverStatusCache) {
            if (!activeServerIds.has(sid)) {
                delete this.serverStatusCache[sid];
            }
        }
        
        // 清理流量状态缓存
        for (const sid in this.trafficStatusCache) {
            if (!activeServerIds.has(sid)) {
                delete this.trafficStatusCache[sid];
            }
        }
        
        // 清理待发送的离线通知
        for (const [sid, timer] of this.offlineNotificationTimers.entries()) {
            if (!activeServerIds.has(sid)) {
                clearTimeout(timer);
                this.offlineNotificationTimers.delete(sid);
            }
        }
    }
    
    /**
     * 获取服务器状态
     */
    getServerStatus(sid) {
        return this.serverStatusCache[sid];
    }
    
    /**
     * 工具方法：延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        // 清理所有待发送的通知定时器
        for (const timer of this.offlineNotificationTimers.values()) {
            clearTimeout(timer);
        }
        this.offlineNotificationTimers.clear();
        
        logger.info('[通知触发器] 资源清理完成');
    }
}

module.exports = NotificationTriggers;
