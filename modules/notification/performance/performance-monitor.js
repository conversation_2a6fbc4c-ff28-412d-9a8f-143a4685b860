/**
 * 性能监控器
 * 监控系统性能指标和资源使用情况
 */
"use strict";

const os = require('os');
const process = require('process');

class PerformanceMonitor {
    constructor(options = {}) {
        this.sampleInterval = options.sampleInterval || 5000; // 5秒采样间隔
        this.maxSamples = options.maxSamples || 720; // 保留1小时的数据（720个5秒样本）
        this.metrics = {
            cpu: [],
            memory: [],
            requests: [],
            responses: [],
            errors: [],
            database: [],
            cache: []
        };
        this.isMonitoring = false;
        this.monitoringTimer = null;
        this.requestCounters = {
            total: 0,
            success: 0,
            error: 0,
            pending: 0
        };
        this.responseTimeStats = {
            min: Infinity,
            max: 0,
            sum: 0,
            count: 0
        };
    }

    /**
     * 开始监控
     */
    start() {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;
        this.monitoringTimer = setInterval(() => {
            this.collectMetrics();
        }, this.sampleInterval);

        console.log('[性能监控] 性能监控已启动');
    }

    /**
     * 停止监控
     */
    stop() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }

        console.log('[性能监控] 性能监控已停止');
    }

    /**
     * 收集性能指标
     */
    collectMetrics() {
        const timestamp = Date.now();

        // CPU 使用率
        this.collectCPUMetrics(timestamp);

        // 内存使用情况
        this.collectMemoryMetrics(timestamp);

        // 请求统计
        this.collectRequestMetrics(timestamp);

        // 清理旧数据
        this.cleanupOldMetrics();
    }

    /**
     * 收集CPU指标
     */
    collectCPUMetrics(timestamp) {
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;

        cpus.forEach(cpu => {
            for (const type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });

        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        const usage = 100 - ~~(100 * idle / total);

        this.metrics.cpu.push({
            timestamp,
            usage,
            cores: cpus.length,
            loadAverage: os.loadavg()
        });
    }

    /**
     * 收集内存指标
     */
    collectMemoryMetrics(timestamp) {
        const processMemory = process.memoryUsage();
        const systemMemory = {
            total: os.totalmem(),
            free: os.freemem(),
            used: os.totalmem() - os.freemem()
        };

        this.metrics.memory.push({
            timestamp,
            process: {
                rss: processMemory.rss,
                heapTotal: processMemory.heapTotal,
                heapUsed: processMemory.heapUsed,
                external: processMemory.external,
                arrayBuffers: processMemory.arrayBuffers
            },
            system: {
                total: systemMemory.total,
                free: systemMemory.free,
                used: systemMemory.used,
                usagePercent: (systemMemory.used / systemMemory.total * 100).toFixed(2)
            }
        });
    }

    /**
     * 收集请求指标
     */
    collectRequestMetrics(timestamp) {
        this.metrics.requests.push({
            timestamp,
            counters: { ...this.requestCounters },
            responseTime: {
                min: this.responseTimeStats.min === Infinity ? 0 : this.responseTimeStats.min,
                max: this.responseTimeStats.max,
                avg: this.responseTimeStats.count > 0 
                    ? (this.responseTimeStats.sum / this.responseTimeStats.count).toFixed(2)
                    : 0
            }
        });

        // 重置响应时间统计
        this.responseTimeStats = {
            min: Infinity,
            max: 0,
            sum: 0,
            count: 0
        };
    }

    /**
     * 清理旧的指标数据
     */
    cleanupOldMetrics() {
        for (const metricType in this.metrics) {
            if (this.metrics[metricType].length > this.maxSamples) {
                this.metrics[metricType] = this.metrics[metricType].slice(-this.maxSamples);
            }
        }
    }

    /**
     * 记录请求开始
     */
    recordRequestStart() {
        this.requestCounters.total++;
        this.requestCounters.pending++;
        return Date.now();
    }

    /**
     * 记录请求完成
     */
    recordRequestEnd(startTime, success = true) {
        const duration = Date.now() - startTime;
        
        this.requestCounters.pending--;
        if (success) {
            this.requestCounters.success++;
        } else {
            this.requestCounters.error++;
        }

        // 更新响应时间统计
        this.responseTimeStats.min = Math.min(this.responseTimeStats.min, duration);
        this.responseTimeStats.max = Math.max(this.responseTimeStats.max, duration);
        this.responseTimeStats.sum += duration;
        this.responseTimeStats.count++;

        return duration;
    }

    /**
     * 记录数据库操作
     */
    recordDatabaseOperation(operation, duration, success = true) {
        this.metrics.database.push({
            timestamp: Date.now(),
            operation,
            duration,
            success
        });

        // 只保留最近的数据库操作记录
        if (this.metrics.database.length > 1000) {
            this.metrics.database = this.metrics.database.slice(-1000);
        }
    }

    /**
     * 记录缓存操作
     */
    recordCacheOperation(operation, hit = false) {
        this.metrics.cache.push({
            timestamp: Date.now(),
            operation,
            hit
        });

        // 只保留最近的缓存操作记录
        if (this.metrics.cache.length > 1000) {
            this.metrics.cache = this.metrics.cache.slice(-1000);
        }
    }

    /**
     * 获取性能摘要
     */
    getPerformanceSummary() {
        const now = Date.now();
        const oneMinuteAgo = now - 60000;
        const fiveMinutesAgo = now - 300000;

        return {
            current: this.getCurrentMetrics(),
            last1min: this.getMetricsSummary(oneMinuteAgo),
            last5min: this.getMetricsSummary(fiveMinutesAgo),
            trends: this.getTrends(),
            alerts: this.getPerformanceAlerts()
        };
    }

    /**
     * 获取当前指标
     */
    getCurrentMetrics() {
        const latestCPU = this.metrics.cpu[this.metrics.cpu.length - 1];
        const latestMemory = this.metrics.memory[this.metrics.memory.length - 1];
        const latestRequest = this.metrics.requests[this.metrics.requests.length - 1];

        return {
            cpu: latestCPU ? latestCPU.usage : 0,
            memory: latestMemory ? latestMemory.system.usagePercent : 0,
            requests: this.requestCounters,
            uptime: process.uptime(),
            timestamp: Date.now()
        };
    }

    /**
     * 获取指定时间段的指标摘要
     */
    getMetricsSummary(since) {
        const cpuMetrics = this.metrics.cpu.filter(m => m.timestamp >= since);
        const memoryMetrics = this.metrics.memory.filter(m => m.timestamp >= since);
        const requestMetrics = this.metrics.requests.filter(m => m.timestamp >= since);

        return {
            cpu: this.calculateAverage(cpuMetrics, 'usage'),
            memory: this.calculateAverage(memoryMetrics, m => parseFloat(m.system.usagePercent)),
            requests: {
                total: requestMetrics.reduce((sum, m) => sum + m.counters.total, 0),
                success: requestMetrics.reduce((sum, m) => sum + m.counters.success, 0),
                error: requestMetrics.reduce((sum, m) => sum + m.counters.error, 0)
            }
        };
    }

    /**
     * 计算平均值
     */
    calculateAverage(metrics, accessor) {
        if (metrics.length === 0) return 0;
        
        const sum = metrics.reduce((total, metric) => {
            const value = typeof accessor === 'function' ? accessor(metric) : metric[accessor];
            return total + (value || 0);
        }, 0);
        
        return (sum / metrics.length).toFixed(2);
    }

    /**
     * 获取性能趋势
     */
    getTrends() {
        const recentCPU = this.metrics.cpu.slice(-12); // 最近1分钟
        const recentMemory = this.metrics.memory.slice(-12);

        return {
            cpu: this.calculateTrend(recentCPU, 'usage'),
            memory: this.calculateTrend(recentMemory, m => parseFloat(m.system.usagePercent))
        };
    }

    /**
     * 计算趋势（上升、下降、稳定）
     */
    calculateTrend(metrics, accessor) {
        if (metrics.length < 2) return 'stable';

        const values = metrics.map(m => typeof accessor === 'function' ? accessor(m) : m[accessor]);
        const first = values[0];
        const last = values[values.length - 1];
        const diff = last - first;

        if (Math.abs(diff) < 5) return 'stable';
        return diff > 0 ? 'rising' : 'falling';
    }

    /**
     * 获取性能警报
     */
    getPerformanceAlerts() {
        const alerts = [];
        const current = this.getCurrentMetrics();

        // CPU 使用率警报
        if (current.cpu > 80) {
            alerts.push({
                type: 'cpu',
                level: 'critical',
                message: `CPU使用率过高: ${current.cpu}%`
            });
        } else if (current.cpu > 60) {
            alerts.push({
                type: 'cpu',
                level: 'warning',
                message: `CPU使用率较高: ${current.cpu}%`
            });
        }

        // 内存使用率警报
        if (current.memory > 85) {
            alerts.push({
                type: 'memory',
                level: 'critical',
                message: `内存使用率过高: ${current.memory}%`
            });
        } else if (current.memory > 70) {
            alerts.push({
                type: 'memory',
                level: 'warning',
                message: `内存使用率较高: ${current.memory}%`
            });
        }

        // 错误率警报
        const errorRate = current.requests.total > 0 
            ? (current.requests.error / current.requests.total * 100)
            : 0;

        if (errorRate > 10) {
            alerts.push({
                type: 'error_rate',
                level: 'critical',
                message: `错误率过高: ${errorRate.toFixed(2)}%`
            });
        } else if (errorRate > 5) {
            alerts.push({
                type: 'error_rate',
                level: 'warning',
                message: `错误率较高: ${errorRate.toFixed(2)}%`
            });
        }

        return alerts;
    }

    /**
     * 获取详细的性能报告
     */
    getDetailedReport() {
        return {
            summary: this.getPerformanceSummary(),
            metrics: {
                cpu: this.metrics.cpu.slice(-60), // 最近5分钟
                memory: this.metrics.memory.slice(-60),
                requests: this.metrics.requests.slice(-60)
            },
            database: this.getDatabaseStats(),
            cache: this.getCacheStats(),
            system: {
                platform: os.platform(),
                arch: os.arch(),
                nodeVersion: process.version,
                uptime: process.uptime(),
                pid: process.pid
            }
        };
    }

    /**
     * 获取数据库统计
     */
    getDatabaseStats() {
        const recentOps = this.metrics.database.filter(op => 
            Date.now() - op.timestamp < 300000 // 最近5分钟
        );

        const totalOps = recentOps.length;
        const successOps = recentOps.filter(op => op.success).length;
        const avgDuration = totalOps > 0 
            ? (recentOps.reduce((sum, op) => sum + op.duration, 0) / totalOps).toFixed(2)
            : 0;

        return {
            totalOperations: totalOps,
            successRate: totalOps > 0 ? ((successOps / totalOps) * 100).toFixed(2) : 0,
            averageDuration: avgDuration
        };
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        const recentOps = this.metrics.cache.filter(op => 
            Date.now() - op.timestamp < 300000 // 最近5分钟
        );

        const totalOps = recentOps.length;
        const hits = recentOps.filter(op => op.hit).length;
        const hitRate = totalOps > 0 ? ((hits / totalOps) * 100).toFixed(2) : 0;

        return {
            totalOperations: totalOps,
            hits,
            misses: totalOps - hits,
            hitRate
        };
    }

    /**
     * 重置统计数据
     */
    reset() {
        this.metrics = {
            cpu: [],
            memory: [],
            requests: [],
            responses: [],
            errors: [],
            database: [],
            cache: []
        };
        
        this.requestCounters = {
            total: 0,
            success: 0,
            error: 0,
            pending: 0
        };
        
        this.responseTimeStats = {
            min: Infinity,
            max: 0,
            sum: 0,
            count: 0
        };
    }
}

module.exports = PerformanceMonitor;
