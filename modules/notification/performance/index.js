/**
 * 性能优化和安全加固模块入口
 * 统一管理性能监控、缓存和安全功能
 */
"use strict";

const CacheManager = require('./cache-manager');
const PerformanceMonitor = require('./performance-monitor');
const SecurityManager = require('../security/security-manager');

class OptimizationManager {
    constructor(options = {}) {
        this.options = {
            cache: {
                defaultTTL: 300000, // 5分钟
                maxSize: 1000,
                ...options.cache
            },
            performance: {
                sampleInterval: 5000, // 5秒
                maxSamples: 720, // 1小时数据
                ...options.performance
            },
            security: {
                encryptionKey: options.security?.encryptionKey,
                ...options.security
            }
        };

        this.cache = new CacheManager(this.options.cache);
        this.performance = new PerformanceMonitor(this.options.performance);
        this.security = new SecurityManager(this.options.security);
        
        this.isInitialized = false;
        this.cleanupInterval = null;
    }

    /**
     * 初始化优化管理器
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            // 启动性能监控
            this.performance.start();

            // 设置定期清理
            this.setupCleanupTasks();

            // 设置性能中间件
            this.setupPerformanceMiddleware();

            this.isInitialized = true;
            console.log('[优化] 性能优化和安全加固模块已初始化');

        } catch (error) {
            console.error('[优化] 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 设置清理任务
     */
    setupCleanupTasks() {
        // 每小时执行一次清理
        this.cleanupInterval = setInterval(() => {
            this.performCleanup();
        }, 3600000);
    }

    /**
     * 执行清理任务
     */
    performCleanup() {
        try {
            // 清理过期缓存
            const expiredCount = this.cache.cleanup();
            
            // 清理安全数据
            this.security.cleanup();
            
            console.log(`[优化] 清理完成，移除 ${expiredCount} 个过期缓存项`);
        } catch (error) {
            console.error('[优化] 清理任务失败:', error);
        }
    }

    /**
     * 设置性能中间件
     */
    setupPerformanceMiddleware() {
        this.performanceMiddleware = (req, res, next) => {
            const startTime = this.performance.recordRequestStart();
            
            // 安全检查
            const securityCheck = this.security.validateRequestOrigin(req);
            if (!securityCheck.valid) {
                if (securityCheck.action === 'DENY') {
                    return res.status(403).json({
                        code: 0,
                        msg: '访问被拒绝',
                        reason: securityCheck.reason
                    });
                } else if (securityCheck.action === 'THROTTLE') {
                    return res.status(429).json({
                        code: 0,
                        msg: '请求过于频繁',
                        retryAfter: securityCheck.retryAfter
                    });
                }
            }

            // 添加安全头
            res.set({
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
                'Referrer-Policy': 'strict-origin-when-cross-origin'
            });

            // 监听响应完成
            res.on('finish', () => {
                const success = res.statusCode < 400;
                this.performance.recordRequestEnd(startTime, success);
            });

            next();
        };
    }

    /**
     * 获取性能中间件
     */
    getPerformanceMiddleware() {
        if (!this.isInitialized) {
            throw new Error('优化管理器未初始化');
        }
        return this.performanceMiddleware;
    }

    /**
     * 缓存装饰器
     */
    withCache(key, fn, ttl) {
        return this.cache.getOrSet(key, fn, ttl);
    }

    /**
     * 性能监控装饰器
     */
    withPerformanceMonitoring(operation) {
        return async (fn, ...args) => {
            const startTime = Date.now();
            let success = true;
            
            try {
                const result = await fn(...args);
                return result;
            } catch (error) {
                success = false;
                throw error;
            } finally {
                const duration = Date.now() - startTime;
                this.performance.recordDatabaseOperation(operation, duration, success);
            }
        };
    }

    /**
     * 安全验证装饰器
     */
    withSecurity(validationRules) {
        return (req, res, next) => {
            try {
                // 验证输入
                const validation = this.security.validateInput(req.body, validationRules);
                if (!validation.isValid) {
                    return res.status(400).json({
                        code: 0,
                        msg: '输入验证失败',
                        errors: validation.errors
                    });
                }

                // 清理输入
                if (req.body) {
                    for (const key in req.body) {
                        if (typeof req.body[key] === 'string') {
                            req.body[key] = this.security.sanitizeInput(req.body[key]);
                        }
                    }
                }

                next();
            } catch (error) {
                console.error('[安全] 验证失败:', error);
                res.status(500).json({
                    code: 0,
                    msg: '安全验证失败'
                });
            }
        };
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            cache: this.cache.getStats(),
            performance: this.performance.getPerformanceSummary(),
            security: this.security.getSecurityStats(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 获取详细报告
     */
    getDetailedReport() {
        return {
            cache: {
                stats: this.cache.getStats(),
                keys: this.cache.keys().length,
                memoryUsage: this.cache.getMemoryUsage()
            },
            performance: this.performance.getDetailedReport(),
            security: {
                stats: this.security.getSecurityStats(),
                blockedIPs: Array.from(this.security.blockedIPs),
                recentActivities: this.security.suspiciousActivities.slice(-10)
            },
            system: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                uptime: process.uptime(),
                pid: process.pid
            }
        };
    }

    /**
     * 优化建议
     */
    getOptimizationSuggestions() {
        const suggestions = [];
        const cacheStats = this.cache.getStats();
        const perfSummary = this.performance.getPerformanceSummary();

        // 缓存优化建议
        if (parseFloat(cacheStats.hitRate) < 70) {
            suggestions.push({
                type: 'cache',
                level: 'warning',
                message: `缓存命中率较低 (${cacheStats.hitRate})，建议增加缓存时间或优化缓存策略`
            });
        }

        if (cacheStats.size > cacheStats.maxSize * 0.9) {
            suggestions.push({
                type: 'cache',
                level: 'info',
                message: '缓存使用率较高，建议增加缓存大小限制'
            });
        }

        // 性能优化建议
        const alerts = perfSummary.alerts;
        alerts.forEach(alert => {
            suggestions.push({
                type: 'performance',
                level: alert.level,
                message: alert.message
            });
        });

        // 安全建议
        const securityStats = this.security.getSecurityStats();
        if (securityStats.blockedIPs > 10) {
            suggestions.push({
                type: 'security',
                level: 'warning',
                message: `当前有 ${securityStats.blockedIPs} 个IP被阻止，建议检查安全策略`
            });
        }

        if (securityStats.suspiciousActivities.last24h > 50) {
            suggestions.push({
                type: 'security',
                level: 'warning',
                message: `24小时内检测到 ${securityStats.suspiciousActivities.last24h} 次可疑活动`
            });
        }

        return suggestions;
    }

    /**
     * 关闭优化管理器
     */
    async shutdown() {
        if (!this.isInitialized) {
            return;
        }

        try {
            // 停止性能监控
            this.performance.stop();

            // 清除清理定时器
            if (this.cleanupInterval) {
                clearInterval(this.cleanupInterval);
                this.cleanupInterval = null;
            }

            // 清空缓存
            this.cache.clear();

            this.isInitialized = false;
            console.log('[优化] 性能优化和安全加固模块已关闭');

        } catch (error) {
            console.error('[优化] 关闭失败:', error);
            throw error;
        }
    }

    /**
     * 健康检查
     */
    healthCheck() {
        const status = this.getSystemStatus();
        const suggestions = this.getOptimizationSuggestions();
        
        const criticalIssues = suggestions.filter(s => s.level === 'critical');
        const warningIssues = suggestions.filter(s => s.level === 'warning');

        return {
            healthy: criticalIssues.length === 0,
            status: criticalIssues.length === 0 ? 'healthy' : 'degraded',
            issues: {
                critical: criticalIssues.length,
                warning: warningIssues.length,
                total: suggestions.length
            },
            uptime: status.uptime,
            timestamp: status.timestamp
        };
    }
}

/**
 * 创建优化管理器实例
 */
function createOptimizationManager(options = {}) {
    return new OptimizationManager(options);
}

module.exports = {
    OptimizationManager,
    CacheManager,
    PerformanceMonitor,
    SecurityManager,
    createOptimizationManager
};
