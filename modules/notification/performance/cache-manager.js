/**
 * 缓存管理器
 * 提供高效的缓存机制以提升性能
 */
"use strict";

class CacheManager {
    constructor(options = {}) {
        this.defaultTTL = options.defaultTTL || 300000; // 5分钟默认TTL
        this.maxSize = options.maxSize || 1000; // 最大缓存条目数
        this.cache = new Map();
        this.timers = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0
        };
    }

    /**
     * 获取缓存值
     * @param {string} key - 缓存键
     * @returns {*} 缓存值或undefined
     */
    get(key) {
        if (this.cache.has(key)) {
            const item = this.cache.get(key);
            
            // 检查是否过期
            if (item.expiry && Date.now() > item.expiry) {
                this.delete(key);
                this.stats.misses++;
                return undefined;
            }
            
            // 更新访问时间
            item.lastAccess = Date.now();
            this.stats.hits++;
            return item.value;
        }
        
        this.stats.misses++;
        return undefined;
    }

    /**
     * 设置缓存值
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒）
     */
    set(key, value, ttl = this.defaultTTL) {
        // 检查缓存大小限制
        if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
            this.evictLRU();
        }

        // 清除现有定时器
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
            this.timers.delete(key);
        }

        const expiry = ttl > 0 ? Date.now() + ttl : null;
        const item = {
            value,
            expiry,
            lastAccess: Date.now(),
            createdAt: Date.now()
        };

        this.cache.set(key, item);
        this.stats.sets++;

        // 设置过期定时器
        if (expiry) {
            const timer = setTimeout(() => {
                this.delete(key);
            }, ttl);
            this.timers.set(key, timer);
        }
    }

    /**
     * 删除缓存项
     * @param {string} key - 缓存键
     * @returns {boolean} 是否删除成功
     */
    delete(key) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
            
            if (this.timers.has(key)) {
                clearTimeout(this.timers.get(key));
                this.timers.delete(key);
            }
            
            this.stats.deletes++;
            return true;
        }
        return false;
    }

    /**
     * 检查缓存是否存在
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        if (this.cache.has(key)) {
            const item = this.cache.get(key);
            
            // 检查是否过期
            if (item.expiry && Date.now() > item.expiry) {
                this.delete(key);
                return false;
            }
            
            return true;
        }
        return false;
    }

    /**
     * 清空所有缓存
     */
    clear() {
        // 清除所有定时器
        for (const timer of this.timers.values()) {
            clearTimeout(timer);
        }
        
        this.cache.clear();
        this.timers.clear();
        
        // 重置统计
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0
        };
    }

    /**
     * 获取或设置缓存（如果不存在则调用函数获取值）
     * @param {string} key - 缓存键
     * @param {Function} fn - 获取值的函数
     * @param {number} ttl - 生存时间
     * @returns {Promise<*>} 缓存值
     */
    async getOrSet(key, fn, ttl = this.defaultTTL) {
        let value = this.get(key);
        
        if (value === undefined) {
            value = await fn();
            this.set(key, value, ttl);
        }
        
        return value;
    }

    /**
     * 批量获取缓存
     * @param {string[]} keys - 缓存键数组
     * @returns {Map<string, *>} 键值对映射
     */
    mget(keys) {
        const result = new Map();
        
        for (const key of keys) {
            const value = this.get(key);
            if (value !== undefined) {
                result.set(key, value);
            }
        }
        
        return result;
    }

    /**
     * 批量设置缓存
     * @param {Map<string, *>} entries - 键值对映射
     * @param {number} ttl - 生存时间
     */
    mset(entries, ttl = this.defaultTTL) {
        for (const [key, value] of entries) {
            this.set(key, value, ttl);
        }
    }

    /**
     * 淘汰最近最少使用的缓存项
     */
    evictLRU() {
        let oldestKey = null;
        let oldestTime = Date.now();
        
        for (const [key, item] of this.cache) {
            if (item.lastAccess < oldestTime) {
                oldestTime = item.lastAccess;
                oldestKey = key;
            }
        }
        
        if (oldestKey) {
            this.delete(oldestKey);
            this.stats.evictions++;
        }
    }

    /**
     * 清理过期缓存
     */
    cleanup() {
        const now = Date.now();
        const expiredKeys = [];
        
        for (const [key, item] of this.cache) {
            if (item.expiry && now > item.expiry) {
                expiredKeys.push(key);
            }
        }
        
        for (const key of expiredKeys) {
            this.delete(key);
        }
        
        return expiredKeys.length;
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const hitRate = this.stats.hits + this.stats.misses > 0 
            ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
            : '0.00';

        return {
            ...this.stats,
            hitRate: `${hitRate}%`,
            size: this.cache.size,
            maxSize: this.maxSize,
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 估算内存使用量
     * @returns {Object} 内存使用信息
     */
    getMemoryUsage() {
        let totalSize = 0;
        
        for (const [key, item] of this.cache) {
            // 粗略估算
            totalSize += key.length * 2; // 字符串键
            totalSize += JSON.stringify(item.value).length * 2; // 值的大小
            totalSize += 64; // 元数据开销
        }
        
        return {
            bytes: totalSize,
            kb: (totalSize / 1024).toFixed(2),
            mb: (totalSize / 1024 / 1024).toFixed(2)
        };
    }

    /**
     * 设置缓存键的TTL
     * @param {string} key - 缓存键
     * @param {number} ttl - 新的TTL（毫秒）
     * @returns {boolean} 是否设置成功
     */
    expire(key, ttl) {
        if (!this.cache.has(key)) {
            return false;
        }

        const item = this.cache.get(key);
        
        // 清除现有定时器
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
            this.timers.delete(key);
        }

        // 设置新的过期时间
        item.expiry = Date.now() + ttl;
        
        // 设置新的定时器
        const timer = setTimeout(() => {
            this.delete(key);
        }, ttl);
        this.timers.set(key, timer);
        
        return true;
    }

    /**
     * 获取缓存键的剩余TTL
     * @param {string} key - 缓存键
     * @returns {number} 剩余TTL（毫秒），-1表示永不过期，-2表示键不存在
     */
    ttl(key) {
        if (!this.cache.has(key)) {
            return -2;
        }

        const item = this.cache.get(key);
        
        if (!item.expiry) {
            return -1; // 永不过期
        }

        const remaining = item.expiry - Date.now();
        return remaining > 0 ? remaining : 0;
    }

    /**
     * 获取所有缓存键
     * @returns {string[]} 缓存键数组
     */
    keys() {
        return Array.from(this.cache.keys());
    }

    /**
     * 获取缓存大小
     * @returns {number} 缓存项数量
     */
    size() {
        return this.cache.size;
    }
}

module.exports = CacheManager;
