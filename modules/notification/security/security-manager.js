/**
 * 安全管理器
 * 提供安全加固功能
 */
"use strict";

const crypto = require('crypto');

class SecurityManager {
    constructor(options = {}) {
        this.encryptionKey = options.encryptionKey || this.generateKey();
        this.algorithm = 'aes-256-gcm';
        this.rateLimits = new Map();
        this.blockedIPs = new Set();
        this.suspiciousActivities = [];
        this.maxSuspiciousActivities = 1000;
    }

    /**
     * 生成加密密钥
     * @returns {string} 32字节的十六进制密钥
     */
    generateKey() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * 加密敏感数据
     * @param {string} text - 要加密的文本
     * @returns {Object} 加密结果
     */
    encrypt(text) {
        try {
            const iv = crypto.randomBytes(16);
            const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
            cipher.setAAD(Buffer.from('notification-module'));
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const authTag = cipher.getAuthTag();
            
            return {
                encrypted,
                iv: iv.toString('hex'),
                authTag: authTag.toString('hex')
            };
        } catch (error) {
            console.error('[安全] 加密失败:', error);
            throw new Error('数据加密失败');
        }
    }

    /**
     * 解密敏感数据
     * @param {Object} encryptedData - 加密数据对象
     * @returns {string} 解密后的文本
     */
    decrypt(encryptedData) {
        try {
            const { encrypted, iv, authTag } = encryptedData;
            
            const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
            decipher.setAAD(Buffer.from('notification-module'));
            decipher.setAuthTag(Buffer.from(authTag, 'hex'));
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('[安全] 解密失败:', error);
            throw new Error('数据解密失败');
        }
    }

    /**
     * 生成安全的随机令牌
     * @param {number} length - 令牌长度
     * @returns {string} 随机令牌
     */
    generateToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * 哈希密码
     * @param {string} password - 原始密码
     * @param {string} salt - 盐值
     * @returns {string} 哈希后的密码
     */
    hashPassword(password, salt = null) {
        if (!salt) {
            salt = crypto.randomBytes(16).toString('hex');
        }
        
        const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
        return `${salt}:${hash.toString('hex')}`;
    }

    /**
     * 验证密码
     * @param {string} password - 输入的密码
     * @param {string} hashedPassword - 存储的哈希密码
     * @returns {boolean} 是否匹配
     */
    verifyPassword(password, hashedPassword) {
        try {
            const [salt, hash] = hashedPassword.split(':');
            const newHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
            return hash === newHash.toString('hex');
        } catch (error) {
            console.error('[安全] 密码验证失败:', error);
            return false;
        }
    }

    /**
     * 速率限制检查
     * @param {string} identifier - 标识符（IP、用户ID等）
     * @param {number} maxRequests - 最大请求数
     * @param {number} windowMs - 时间窗口（毫秒）
     * @returns {Object} 限制结果
     */
    checkRateLimit(identifier, maxRequests = 100, windowMs = 60000) {
        const now = Date.now();
        const windowStart = now - windowMs;
        
        if (!this.rateLimits.has(identifier)) {
            this.rateLimits.set(identifier, []);
        }
        
        const requests = this.rateLimits.get(identifier);
        
        // 清理过期的请求记录
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        this.rateLimits.set(identifier, validRequests);
        
        // 检查是否超过限制
        if (validRequests.length >= maxRequests) {
            this.logSuspiciousActivity(identifier, 'RATE_LIMIT_EXCEEDED', {
                requests: validRequests.length,
                maxRequests,
                windowMs
            });
            
            return {
                allowed: false,
                remaining: 0,
                resetTime: windowStart + windowMs,
                retryAfter: Math.ceil((windowStart + windowMs - now) / 1000)
            };
        }
        
        // 记录当前请求
        validRequests.push(now);
        this.rateLimits.set(identifier, validRequests);
        
        return {
            allowed: true,
            remaining: maxRequests - validRequests.length,
            resetTime: windowStart + windowMs,
            retryAfter: 0
        };
    }

    /**
     * 验证输入数据
     * @param {*} data - 输入数据
     * @param {Object} rules - 验证规则
     * @returns {Object} 验证结果
     */
    validateInput(data, rules) {
        const errors = [];
        
        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field];
            
            // 必填检查
            if (rule.required && (value === undefined || value === null || value === '')) {
                errors.push(`${field} 是必填字段`);
                continue;
            }
            
            // 如果字段为空且非必填，跳过其他验证
            if (!rule.required && (value === undefined || value === null || value === '')) {
                continue;
            }
            
            // 类型检查
            if (rule.type && typeof value !== rule.type) {
                errors.push(`${field} 类型错误，期望 ${rule.type}`);
                continue;
            }
            
            // 长度检查
            if (rule.minLength && value.length < rule.minLength) {
                errors.push(`${field} 长度不能少于 ${rule.minLength} 个字符`);
            }
            
            if (rule.maxLength && value.length > rule.maxLength) {
                errors.push(`${field} 长度不能超过 ${rule.maxLength} 个字符`);
            }
            
            // 正则表达式检查
            if (rule.pattern && !rule.pattern.test(value)) {
                errors.push(`${field} 格式不正确`);
            }
            
            // 自定义验证函数
            if (rule.validator && typeof rule.validator === 'function') {
                const result = rule.validator(value);
                if (result !== true) {
                    errors.push(result || `${field} 验证失败`);
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 清理和转义用户输入
     * @param {string} input - 用户输入
     * @returns {string} 清理后的输入
     */
    sanitizeInput(input) {
        if (typeof input !== 'string') {
            return input;
        }
        
        return input
            .replace(/[<>]/g, '') // 移除尖括号
            .replace(/javascript:/gi, '') // 移除javascript协议
            .replace(/on\w+=/gi, '') // 移除事件处理器
            .trim();
    }

    /**
     * 检查IP是否被阻止
     * @param {string} ip - IP地址
     * @returns {boolean} 是否被阻止
     */
    isIPBlocked(ip) {
        return this.blockedIPs.has(ip);
    }

    /**
     * 阻止IP地址
     * @param {string} ip - IP地址
     * @param {number} duration - 阻止时长（毫秒）
     */
    blockIP(ip, duration = 3600000) { // 默认1小时
        this.blockedIPs.add(ip);
        
        this.logSuspiciousActivity(ip, 'IP_BLOCKED', { duration });
        
        // 设置自动解除阻止
        setTimeout(() => {
            this.blockedIPs.delete(ip);
            console.log(`[安全] IP ${ip} 已解除阻止`);
        }, duration);
    }

    /**
     * 记录可疑活动
     * @param {string} identifier - 标识符
     * @param {string} type - 活动类型
     * @param {Object} details - 详细信息
     */
    logSuspiciousActivity(identifier, type, details = {}) {
        const activity = {
            timestamp: new Date().toISOString(),
            identifier,
            type,
            details,
            userAgent: details.userAgent || 'unknown',
            ip: details.ip || identifier
        };
        
        this.suspiciousActivities.push(activity);
        
        // 限制记录数量
        if (this.suspiciousActivities.length > this.maxSuspiciousActivities) {
            this.suspiciousActivities.shift();
        }
        
        console.warn(`[安全] 可疑活动: ${type} - ${identifier}`, details);
        
        // 自动阻止频繁的可疑活动
        this.checkForAutoBlock(identifier, type);
    }

    /**
     * 检查是否需要自动阻止
     * @param {string} identifier - 标识符
     * @param {string} type - 活动类型
     */
    checkForAutoBlock(identifier, type) {
        const recentActivities = this.suspiciousActivities.filter(activity => 
            activity.identifier === identifier &&
            activity.type === type &&
            Date.now() - new Date(activity.timestamp).getTime() < 300000 // 5分钟内
        );
        
        // 如果5分钟内同类型可疑活动超过5次，自动阻止
        if (recentActivities.length >= 5) {
            this.blockIP(identifier, 3600000); // 阻止1小时
        }
    }

    /**
     * 获取安全统计信息
     * @returns {Object} 安全统计
     */
    getSecurityStats() {
        const now = Date.now();
        const last24h = now - 24 * 60 * 60 * 1000;
        
        const recentActivities = this.suspiciousActivities.filter(activity =>
            new Date(activity.timestamp).getTime() > last24h
        );
        
        const activityTypes = {};
        recentActivities.forEach(activity => {
            activityTypes[activity.type] = (activityTypes[activity.type] || 0) + 1;
        });
        
        return {
            blockedIPs: this.blockedIPs.size,
            rateLimitEntries: this.rateLimits.size,
            suspiciousActivities: {
                total: this.suspiciousActivities.length,
                last24h: recentActivities.length,
                types: activityTypes
            },
            uptime: process.uptime()
        };
    }

    /**
     * 清理过期的安全数据
     */
    cleanup() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        
        // 清理过期的速率限制记录
        for (const [identifier, requests] of this.rateLimits) {
            const validRequests = requests.filter(timestamp => timestamp > oneHourAgo);
            if (validRequests.length === 0) {
                this.rateLimits.delete(identifier);
            } else {
                this.rateLimits.set(identifier, validRequests);
            }
        }
        
        // 清理旧的可疑活动记录（保留最近7天）
        const sevenDaysAgo = now - 7 * 24 * 60 * 60 * 1000;
        this.suspiciousActivities = this.suspiciousActivities.filter(activity =>
            new Date(activity.timestamp).getTime() > sevenDaysAgo
        );
    }

    /**
     * 验证请求来源
     * @param {Object} req - 请求对象
     * @returns {Object} 验证结果
     */
    validateRequestOrigin(req) {
        const ip = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent') || '';
        const referer = req.get('Referer') || '';
        
        // 检查IP是否被阻止
        if (this.isIPBlocked(ip)) {
            return {
                valid: false,
                reason: 'IP_BLOCKED',
                action: 'DENY'
            };
        }
        
        // 检查速率限制
        const rateLimit = this.checkRateLimit(ip);
        if (!rateLimit.allowed) {
            return {
                valid: false,
                reason: 'RATE_LIMIT_EXCEEDED',
                action: 'THROTTLE',
                retryAfter: rateLimit.retryAfter
            };
        }
        
        // 检查可疑的User-Agent
        const suspiciousPatterns = [
            /bot/i,
            /crawler/i,
            /spider/i,
            /scraper/i
        ];
        
        if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
            this.logSuspiciousActivity(ip, 'SUSPICIOUS_USER_AGENT', { userAgent });
        }
        
        return {
            valid: true,
            ip,
            userAgent,
            referer,
            remaining: rateLimit.remaining
        };
    }
}

module.exports = SecurityManager;
