/**
 * 通知模块集成测试
 * 验证整个模块的功能完整性
 */
"use strict";

const { runAllTests } = require('./tests');

/**
 * 模拟数据库和通知管理器
 */
function createMockDependencies() {
    // 模拟数据库
    const mockDb = {
        setting: {
            data: new Map(),
            async get(key) {
                return this.data.get(key);
            },
            async set(key, value) {
                this.data.set(key, value);
            }
        },
        emailConfig: {
            config: {},
            async get() {
                return this.config;
            },
            async set(config) {
                this.config = config;
            }
        },
        notificationTasks: {
            tasks: [],
            async listAll() {
                return [...this.tasks];
            },
            async create(data) {
                const task = { id: `task_${Date.now()}`, ...data };
                this.tasks.push(task);
                return task.id;
            },
            async get(id) {
                return this.tasks.find(t => t.id === id);
            },
            async update(id, data) {
                const index = this.tasks.findIndex(t => t.id === id);
                if (index >= 0) {
                    this.tasks[index] = { ...this.tasks[index], ...data };
                }
            },
            async delete(id) {
                const index = this.tasks.findIndex(t => t.id === id);
                if (index >= 0) {
                    this.tasks.splice(index, 1);
                }
            }
        },
        getServers: async () => [
            { sid: 'server1', name: '测试服务器1' },
            { sid: 'server2', name: '测试服务器2' }
        ]
    };

    // 模拟通知管理器
    const mockNotificationManager = {
        async sendNotification(title, message, chatIds, options) {
            console.log(`[模拟通知] ${title}: ${message}`);
            return { success: true, message: '测试通知发送成功' };
        },
        emailProvider: {
            async send(emailData) {
                console.log(`[模拟邮件] 发送到: ${emailData.to?.join(', ')}`);
                return { ok: true, message: '测试邮件发送成功' };
            }
        },
        async reinitializeBot(config) {
            console.log('[模拟] 重新初始化 Bot');
        },
        async reinitializeEmailProvider(config) {
            console.log('[模拟] 重新初始化邮件提供者');
        }
    };

    return { mockDb, mockNotificationManager };
}

/**
 * 测试模块初始化
 */
async function testModuleInitialization() {
    console.log('🔧 测试模块初始化...');
    
    try {
        const { createServices } = require('./services');
        const { createControllers } = require('./controllers');
        const { mockDb, mockNotificationManager } = createMockDependencies();

        // 测试服务创建
        const services = createServices({
            db: mockDb,
            notificationManager: mockNotificationManager
        });

        console.log('✅ 服务层创建成功');
        console.log(`   - 配置服务: ${!!services.configService}`);
        console.log(`   - Telegram服务: ${!!services.telegramService}`);
        console.log(`   - 邮件服务: ${!!services.emailService}`);
        console.log(`   - 任务服务: ${!!services.tasksService}`);

        // 测试控制器创建
        const controllers = createControllers(services);

        console.log('✅ 控制器层创建成功');
        console.log(`   - 页面控制器: ${!!controllers.pageController}`);
        console.log(`   - Telegram API控制器: ${!!controllers.telegramApiController}`);
        console.log(`   - 邮件API控制器: ${!!controllers.emailApiController}`);
        console.log(`   - 任务API控制器: ${!!controllers.tasksApiController}`);

        return { services, controllers };

    } catch (error) {
        console.error('❌ 模块初始化失败:', error.message);
        throw error;
    }
}

/**
 * 测试基本功能
 */
async function testBasicFunctionality() {
    console.log('\n🧪 测试基本功能...');
    
    try {
        const { services } = await testModuleInitialization();

        // 测试配置管理
        console.log('📋 测试配置管理...');
        const allConfigs = await services.configService.getAllConfigs();
        console.log('✅ 配置获取成功');

        // 测试Telegram服务
        console.log('📱 测试Telegram服务...');
        const telegramConfig = await services.telegramService.getConfig();
        console.log('✅ Telegram配置获取成功');

        // 测试邮件服务
        console.log('📧 测试邮件服务...');
        const emailConfig = await services.emailService.getConfig();
        console.log('✅ 邮件配置获取成功');

        // 测试任务服务
        console.log('📝 测试任务服务...');
        const tasks = await services.tasksService.listAll();
        console.log('✅ 任务列表获取成功');

        // 测试任务创建
        const taskId = await services.tasksService.create({
            sid: 'server1',
            period: 'monthly',
            direction: 'both',
            enabled: true,
            type: 'traffic'
        });
        console.log(`✅ 任务创建成功: ${taskId}`);

        return true;

    } catch (error) {
        console.error('❌ 基本功能测试失败:', error.message);
        throw error;
    }
}

/**
 * 测试性能优化功能
 */
async function testPerformanceOptimization() {
    console.log('\n⚡ 测试性能优化功能...');
    
    try {
        const { createOptimizationManager } = require('./performance');
        
        const optimizationManager = createOptimizationManager({
            cache: { defaultTTL: 60000, maxSize: 100 },
            performance: { sampleInterval: 1000, maxSamples: 60 },
            security: {}
        });

        await optimizationManager.initialize();
        console.log('✅ 优化管理器初始化成功');

        // 测试缓存功能
        const cacheKey = 'test-key';
        const cacheValue = { data: 'test-data', timestamp: Date.now() };
        
        optimizationManager.cache.set(cacheKey, cacheValue);
        const retrievedValue = optimizationManager.cache.get(cacheKey);
        
        if (JSON.stringify(retrievedValue) === JSON.stringify(cacheValue)) {
            console.log('✅ 缓存功能正常');
        } else {
            throw new Error('缓存功能异常');
        }

        // 测试性能监控
        const perfSummary = optimizationManager.performance.getPerformanceSummary();
        console.log('✅ 性能监控正常');

        // 测试安全功能
        const securityStats = optimizationManager.security.getSecurityStats();
        console.log('✅ 安全功能正常');

        await optimizationManager.shutdown();
        console.log('✅ 优化管理器关闭成功');

        return true;

    } catch (error) {
        console.error('❌ 性能优化测试失败:', error.message);
        throw error;
    }
}

/**
 * 测试路由系统
 */
async function testRouteSystem() {
    console.log('\n🛣️  测试路由系统...');
    
    try {
        const { createPageRoutes, createApiRoutes } = require('./routes');
        const { services, controllers } = await testModuleInitialization();

        // 测试页面路由创建
        const pageRoutes = createPageRoutes(controllers);
        console.log('✅ 页面路由创建成功');

        // 测试API路由创建
        const apiRoutes = createApiRoutes(controllers);
        console.log('✅ API路由创建成功');

        return true;

    } catch (error) {
        console.error('❌ 路由系统测试失败:', error.message);
        throw error;
    }
}

/**
 * 运行完整的集成测试
 */
async function runIntegrationTest() {
    console.log('🚀 开始通知模块集成测试...\n');
    
    const startTime = Date.now();
    const results = {
        passed: 0,
        failed: 0,
        tests: []
    };

    const tests = [
        { name: '模块初始化', fn: testModuleInitialization },
        { name: '基本功能', fn: testBasicFunctionality },
        { name: '性能优化', fn: testPerformanceOptimization },
        { name: '路由系统', fn: testRouteSystem }
    ];

    for (const test of tests) {
        try {
            console.log(`\n📋 运行测试: ${test.name}`);
            await test.fn();
            results.passed++;
            results.tests.push({ name: test.name, status: 'passed' });
            console.log(`✅ ${test.name} 测试通过`);
        } catch (error) {
            results.failed++;
            results.tests.push({ 
                name: test.name, 
                status: 'failed', 
                error: error.message 
            });
            console.error(`❌ ${test.name} 测试失败: ${error.message}`);
        }
    }

    // 运行详细测试套件
    console.log('\n🧪 运行详细测试套件...');
    try {
        const testResults = await runAllTests();
        if (testResults.totalFailed === 0) {
            console.log('✅ 详细测试套件全部通过');
            results.passed++;
            results.tests.push({ name: '详细测试套件', status: 'passed' });
        } else {
            console.log(`⚠️  详细测试套件有 ${testResults.totalFailed} 个失败`);
            results.failed++;
            results.tests.push({ 
                name: '详细测试套件', 
                status: 'failed', 
                error: `${testResults.totalFailed} 个测试失败`
            });
        }
    } catch (error) {
        console.error('❌ 详细测试套件运行失败:', error.message);
        results.failed++;
        results.tests.push({ 
            name: '详细测试套件', 
            status: 'failed', 
            error: error.message 
        });
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    // 打印最终结果
    console.log('\n' + '='.repeat(60));
    console.log('🎯 通知模块集成测试完成');
    console.log('='.repeat(60));
    console.log(`⏱️  总耗时: ${duration}ms`);
    console.log(`✅ 通过: ${results.passed}`);
    console.log(`❌ 失败: ${results.failed}`);
    console.log(`📊 成功率: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(2)}%`);

    if (results.failed > 0) {
        console.log('\n❌ 失败的测试:');
        results.tests.filter(t => t.status === 'failed').forEach(test => {
            console.log(`  - ${test.name}: ${test.error}`);
        });
    }

    console.log('\n' + '='.repeat(60));
    
    if (results.failed === 0) {
        console.log('🎉 所有测试通过！通知模块功能正常，可以投入使用。');
    } else {
        console.log('⚠️  存在测试失败，请检查上述错误信息。');
    }
    
    console.log('='.repeat(60));

    return results;
}

// 如果直接运行此文件，执行集成测试
if (require.main === module) {
    runIntegrationTest().then(results => {
        process.exit(results.failed > 0 ? 1 : 0);
    }).catch(error => {
        console.error('集成测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    runIntegrationTest,
    testModuleInitialization,
    testBasicFunctionality,
    testPerformanceOptimization,
    testRouteSystem
};
