{% extends "layout.html" %}
{% block content %}
  <h2 style="font-size:16px; margin:0 0 10px 0;">{{ title }}</h2>

  {% if data.serverName %}
    <div class="kv"><b>服务器：</b>{{ data.serverName }}</div>
  {% endif %}

  {% if data.ipAddress %}
    <div class="kv"><b>IP地址：</b>{{ data.ipAddress }}</div>
  {% endif %}

  {% if data.system %}
    <div class="kv"><b>系统：</b>{{ data.system }}</div>
  {% endif %}

  {% if data.threshold %}
    <div class="kv"><b>阈值：</b>{{ data.threshold }}{% if data.ratio %}（当前 {{ data.ratio }}）{% endif %}</div>
  {% endif %}

  {% if data.used and data.limit %}
    <div class="kv"><b>用量：</b>{{ data.used }} / {{ data.limit }}</div>
  {% endif %}

  {% if data.periodLabel or data.directionLabel %}
    <div class="kv"><b>周期：</b>{{ data.periodLabel or '月度' }} / {{ data.directionLabel or '双向' }}</div>
  {% endif %}

  <div class="kv"><b>时间：</b>{{ data.time or '' }}</div>

  {% if content and content != title %}
    <div style="margin-top:12px; padding:8px; background-color:#f5f5f5; border-radius:4px;">
      <pre style="margin:0; font-family:inherit; white-space:pre-wrap;">{{ content }}</pre>
    </div>
  {% endif %}

  {% if data.link %}
    <p style="margin-top:16px;"><a class="btn" href="{{ data.link }}" target="_blank" rel="noopener">查看详情</a></p>
  {% endif %}
{% endblock %}

