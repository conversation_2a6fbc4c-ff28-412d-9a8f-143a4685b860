/**
 * 错误处理中间件
 * 标准化错误响应格式
 */
"use strict";

/**
 * API错误处理中间件
 * @param {Error} error - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function handleApiError(error, req, res, next) {
    // 记录错误日志
    console.error('[通知系统] API错误:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        body: req.body,
        timestamp: new Date().toISOString()
    });

    // 如果响应已经发送，则交给默认错误处理器
    if (res.headersSent) {
        return next(error);
    }

    // 根据错误类型返回不同的响应
    let statusCode = 500;
    let message = '服务器内部错误';
    let details = null;

    if (error.name === 'ValidationError') {
        statusCode = 400;
        message = error.message || '输入验证失败';
        details = error.details;
    } else if (error.name === 'AuthenticationError') {
        statusCode = 401;
        message = '认证失败';
    } else if (error.name === 'AuthorizationError') {
        statusCode = 403;
        message = '权限不足';
    } else if (error.name === 'NotFoundError') {
        statusCode = 404;
        message = '资源未找到';
    } else if (error.name === 'ConflictError') {
        statusCode = 409;
        message = error.message || '资源冲突';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        statusCode = 503;
        message = '外部服务不可用';
    }

    // 构建错误响应
    const errorResponse = {
        code: 0,
        msg: message
    };

    // 在开发环境下添加详细错误信息
    if (process.env.NODE_ENV === 'development' && details) {
        errorResponse.details = details;
    }

    res.status(statusCode).json(errorResponse);
}

/**
 * 页面错误处理中间件
 * @param {Error} error - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function handlePageError(error, req, res, next) {
    // 记录错误日志
    console.error('[通知系统] 页面错误:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        timestamp: new Date().toISOString()
    });

    // 如果响应已经发送，则交给默认错误处理器
    if (res.headersSent) {
        return next(error);
    }

    // 根据错误类型返回不同的页面
    let statusCode = 500;
    let errorMessage = '服务器内部错误';

    if (error.name === 'NotFoundError') {
        statusCode = 404;
        errorMessage = '页面未找到';
    } else if (error.name === 'AuthenticationError') {
        // 重定向到登录页面
        return res.redirect('/login');
    } else if (error.name === 'AuthorizationError') {
        statusCode = 403;
        errorMessage = '权限不足';
    }

    // 渲染错误页面或返回简单错误信息
    res.status(statusCode);
    
    // 尝试渲染错误页面，如果失败则返回简单文本
    try {
        res.render('error', { 
            error: { 
                status: statusCode, 
                message: errorMessage 
            } 
        });
    } catch (renderError) {
        console.error('[通知系统] 渲染错误页面失败:', renderError);
        res.send(`错误 ${statusCode}: ${errorMessage}`);
    }
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理器，自动捕获Promise rejection
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function asyncErrorHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * 创建自定义错误类
 */
class ValidationError extends Error {
    constructor(message, details = null) {
        super(message);
        this.name = 'ValidationError';
        this.details = details;
    }
}

class AuthenticationError extends Error {
    constructor(message = '认证失败') {
        super(message);
        this.name = 'AuthenticationError';
    }
}

class AuthorizationError extends Error {
    constructor(message = '权限不足') {
        super(message);
        this.name = 'AuthorizationError';
    }
}

class NotFoundError extends Error {
    constructor(message = '资源未找到') {
        super(message);
        this.name = 'NotFoundError';
    }
}

class ConflictError extends Error {
    constructor(message = '资源冲突') {
        super(message);
        this.name = 'ConflictError';
    }
}

/**
 * 404处理中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function handle404(req, res, next) {
    const error = new NotFoundError(`路径 ${req.originalUrl} 未找到`);
    next(error);
}

/**
 * 日志记录辅助函数
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} meta - 元数据
 */
function logError(level, message, meta = {}) {
    const logEntry = {
        level,
        message,
        timestamp: new Date().toISOString(),
        ...meta
    };
    
    console.error(`[通知系统] ${level.toUpperCase()}:`, logEntry);
}

module.exports = {
    handleApiError,
    handlePageError,
    asyncErrorHandler,
    handle404,
    logError,
    // 自定义错误类
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError
};
