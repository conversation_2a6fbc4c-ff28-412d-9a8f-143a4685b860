/**
 * 认证中间件
 * 确保管理员权限验证
 */
"use strict";

/**
 * 要求管理员权限的中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function requireAdmin(req, res, next) {
    if (!req.admin) {
        // 对于页面请求，重定向到登录页
        if (req.accepts('html') && !req.accepts('json')) {
            return res.redirect('/login');
        }
        // 对于API请求，返回JSON错误
        return res.status(401).json({ 
            code: 0, 
            msg: '需要管理员权限' 
        });
    }
    next();
}

/**
 * 可选的管理员权限中间件
 * 不强制要求管理员权限，但会设置req.isAdmin标志
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function optionalAdmin(req, res, next) {
    req.isAdmin = !!req.admin;
    next();
}

/**
 * 检查管理员权限的辅助函数
 * @param {Object} req - Express请求对象
 * @returns {boolean} 是否具有管理员权限
 */
function hasAdminPermission(req) {
    return !!req.admin;
}

module.exports = {
    requireAdmin,
    optionalAdmin,
    hasAdminPermission
};
