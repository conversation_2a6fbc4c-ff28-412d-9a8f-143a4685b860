/**
 * 验证中间件
 * 提供统一的输入验证机制
 */
"use strict";

/**
 * 创建验证中间件
 * @param {Function} validator - 验证器函数，接收req.body并返回验证结果
 * @returns {Function} Express中间件函数
 */
function validateRequest(validator) {
    return (req, res, next) => {
        try {
            const result = validator(req.body);
            
            if (!result.isValid) {
                return res.status(400).json({ 
                    code: 0, 
                    msg: '输入验证失败', 
                    errors: result.errors || []
                });
            }
            
            // 将验证后的数据附加到请求对象
            if (result.data) {
                req.validatedData = result.data;
            }
            
            next();
        } catch (error) {
            console.error('[验证中间件] 验证过程发生错误:', error);
            return res.status(500).json({ 
                code: 0, 
                msg: '验证过程发生错误' 
            });
        }
    };
}

/**
 * 创建查询参数验证中间件
 * @param {Function} validator - 验证器函数，接收req.query并返回验证结果
 * @returns {Function} Express中间件函数
 */
function validateQuery(validator) {
    return (req, res, next) => {
        try {
            const result = validator(req.query);
            
            if (!result.isValid) {
                return res.status(400).json({ 
                    code: 0, 
                    msg: '查询参数验证失败', 
                    errors: result.errors || []
                });
            }
            
            // 将验证后的查询参数附加到请求对象
            if (result.data) {
                req.validatedQuery = result.data;
            }
            
            next();
        } catch (error) {
            console.error('[验证中间件] 查询参数验证过程发生错误:', error);
            return res.status(500).json({ 
                code: 0, 
                msg: '查询参数验证过程发生错误' 
            });
        }
    };
}

/**
 * 创建路径参数验证中间件
 * @param {Function} validator - 验证器函数，接收req.params并返回验证结果
 * @returns {Function} Express中间件函数
 */
function validateParams(validator) {
    return (req, res, next) => {
        try {
            const result = validator(req.params);
            
            if (!result.isValid) {
                return res.status(400).json({ 
                    code: 0, 
                    msg: '路径参数验证失败', 
                    errors: result.errors || []
                });
            }
            
            // 将验证后的路径参数附加到请求对象
            if (result.data) {
                req.validatedParams = result.data;
            }
            
            next();
        } catch (error) {
            console.error('[验证中间件] 路径参数验证过程发生错误:', error);
            return res.status(500).json({ 
                code: 0, 
                msg: '路径参数验证过程发生错误' 
            });
        }
    };
}

/**
 * 通用验证结果格式化函数
 * @param {boolean} isValid - 是否验证通过
 * @param {Array} errors - 错误信息数组
 * @param {Object} data - 验证后的数据
 * @returns {Object} 标准化的验证结果
 */
function createValidationResult(isValid, errors = [], data = null) {
    return {
        isValid,
        errors: Array.isArray(errors) ? errors : [errors],
        data
    };
}

/**
 * 基础字段验证函数
 */
const validators = {
    /**
     * 验证必填字段
     * @param {*} value - 要验证的值
     * @param {string} fieldName - 字段名称
     * @returns {string|null} 错误信息或null
     */
    required(value, fieldName) {
        if (value === undefined || value === null || value === '') {
            return `${fieldName}是必填字段`;
        }
        return null;
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {string|null} 错误信息或null
     */
    email(email) {
        if (!email) return null;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return '邮箱格式不正确';
        }
        return null;
    },

    /**
     * 验证数字范围
     * @param {number} value - 要验证的数字
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {string} fieldName - 字段名称
     * @returns {string|null} 错误信息或null
     */
    numberRange(value, min, max, fieldName) {
        if (typeof value !== 'number' || isNaN(value)) {
            return `${fieldName}必须是有效数字`;
        }
        if (value < min || value > max) {
            return `${fieldName}必须在${min}到${max}之间`;
        }
        return null;
    },

    /**
     * 验证字符串长度
     * @param {string} value - 要验证的字符串
     * @param {number} minLength - 最小长度
     * @param {number} maxLength - 最大长度
     * @param {string} fieldName - 字段名称
     * @returns {string|null} 错误信息或null
     */
    stringLength(value, minLength, maxLength, fieldName) {
        if (typeof value !== 'string') {
            return `${fieldName}必须是字符串`;
        }
        if (value.length < minLength || value.length > maxLength) {
            return `${fieldName}长度必须在${minLength}到${maxLength}之间`;
        }
        return null;
    }
};

module.exports = {
    validateRequest,
    validateQuery,
    validateParams,
    createValidationResult,
    validators
};
