/**
 * 中间件模块入口文件
 * 统一导出所有中间件组件
 */
"use strict";

const authMiddleware = require('./auth-middleware');
const validationMiddleware = require('./validation-middleware');
const errorMiddleware = require('./error-middleware');

module.exports = {
    // 认证中间件
    auth: authMiddleware,
    
    // 验证中间件
    validation: validationMiddleware,
    
    // 错误处理中间件
    error: errorMiddleware,
    
    // 便捷访问常用中间件
    requireAdmin: authMiddleware.requireAdmin,
    optionalAdmin: authMiddleware.optionalAdmin,
    validateRequest: validationMiddleware.validateRequest,
    validateQuery: validationMiddleware.validateQuery,
    validateParams: validationMiddleware.validateParams,
    handleApiError: errorMiddleware.handleApiError,
    handlePageError: errorMiddleware.handlePageError,
    asyncErrorHandler: errorMiddleware.asyncErrorHandler,
    handle404: errorMiddleware.handle404,
    
    // 错误类
    ValidationError: errorMiddleware.ValidationError,
    AuthenticationError: errorMiddleware.AuthenticationError,
    AuthorizationError: errorMiddleware.AuthorizationError,
    NotFoundError: errorMiddleware.NotFoundError,
    ConflictError: errorMiddleware.ConflictError
};
