/**
 * 全局配置中心 - 统一管理所有系统配置
 * 配置优先级: 环境变量 > 配置文件 > 数据库设置 > 默认值
 * 
 * 设计原则:
 * 1. 分层配置 - 支持多种配置来源
 * 2. 类型安全 - 自动类型转换和验证  
 * 3. 动态更新 - 支持运行时配置变更
 * 4. 向后兼容 - 不影响现有代码
 * 5. 扩展性 - 便于添加新配置项
 */

const path = require('path');

// 配置定义 - 使用函数形式支持动态解析
const CONFIG_SCHEMA = {
    // 系统核心配置
    system: {
        debug: {
            sources: ['env:DEBUG', 'env:NODE_ENV', 'db:debug'],
            default: false,
            type: 'boolean',
            description: '调试模式开关'
        },
        logLevel: {
            sources: ['env:LOG_LEVEL'],
            default: 'WARN',
            type: 'string',
            enum: ['DEBUG', 'INFO', 'WARN', 'ERROR'],
            description: '全局日志级别'
        },
        environment: {
            sources: ['env:NODE_ENV'],
            default: 'production',
            type: 'string',
            enum: ['development', 'staging', 'production'],
            description: '运行环境'
        }
    },

    // 数据库配置
    database: {
        type: {
            sources: ['env:DB_TYPE'],
            default: 'postgresql',
            type: 'string',
            enum: ['sqlite', 'postgresql'],
            description: '数据库类型'
        },
        url: {
            sources: ['env:DATABASE_URL'],
            default: '',
            type: 'string',
            description: '数据库连接URL'
        },
        path: {
            sources: ['env:DB_PATH'],
            default: path.join(__dirname, '../data/db.db'),
            type: 'string',
            description: 'SQLite数据库文件路径'
        },
        connectionPool: {
            sources: ['env:DB_MAX_CONNECTIONS', 'db:db_max_connections'],
            default: 100,
            type: 'integer',
            min: 10,
            max: 500,
            description: '数据库连接池大小'
        }
    },

    // WebSocket配置
    websocket: {
        maxConnections: {
            sources: ['env:MAX_WS_CONNECTIONS', 'db:max_ws_connections'],
            default: 100,
            type: 'integer',
            min: 10,
            max: 1000,
            description: 'WebSocket最大连接数'
        },
        updateInterval: {
            sources: ['env:WS_UPDATE_INTERVAL', 'db:websocket_interval'],
            default: 5000,
            type: 'integer',
            min: 1000,
            max: 30000,
            description: 'WebSocket更新间隔(毫秒)'
        },
        maxConnectionsPerIp: {
            sources: ['env:MAX_CONN_PER_IP', 'db:max_connections_per_ip'],
            default: 30,
            type: 'integer',
            min: 5,
            max: 100,
            description: '每个IP最大连接数'
        },
        serverCacheDuration: {
            sources: ['env:SERVER_CACHE_DURATION', 'db:server_cache_duration'],
            default: 300000,
            type: 'integer',
            min: 60000,
            max: 1800000,
            description: '服务器缓存持续时间(毫秒) - 5分钟默认'
        },
        requestLimitWindow: {
            sources: ['env:REQUEST_LIMIT_WINDOW', 'db:request_limit_window'],
            default: 1000,
            type: 'integer',
            min: 100,
            max: 10000,
            description: '请求限制窗口时间(毫秒)'
        },
        activeModeTimeout: {
            sources: ['env:ACTIVE_MODE_TIMEOUT', 'db:active_mode_timeout'],
            default: 300000,
            type: 'integer',
            min: 60000,
            max: 3600000,
            description: '活跃模式超时时间(毫秒) - 5分钟默认'
        }
    },

    // 性能监控配置
    monitoring: {
        pollingInterval: {
            sources: ['env:POLLING_INTERVAL', 'db:polling_interval'],
            default: 1500, // 恢复1.5秒轮询，保证实时性
            type: 'integer',
            min: 1000,
            max: 60000,
            description: '监控轮询间隔(毫秒) - 保证实时监控需求'
        },
        archiveInterval: {
            calculated: true,
            formula: (config) => 15000, // 固定15秒，优化数据库写入性能
            description: '归档处理间隔(毫秒) = 固定15秒，优化数据库写入频率'
        },
        enablePerformanceLog: {
            sources: ['env:ENABLE_PERF_LOG', 'db:enable_performance_log'],
            default: false,
            type: 'boolean',
            description: '性能监控日志开关'
        }
    },

    // 批处理配置（已简化，仅保留批量插入）
    batch: {
        // 以下配置项已废弃，不再使用分批处理
        // size: { ... },
        // delay: { ... },
        // safetyRatio: { ... },
        
        // 批量插入现在默认启用，不再需要配置
        bulkInsertEnabled: {
            sources: ['env:BULK_INSERT_ENABLED', 'db:archive_bulk_insert'],
            default: 'true',  // 改为默认启用
            type: 'boolean',
            description: '批量插入模式开关（已弃用，始终启用）'
        }
    },

    // 日志配置
    logging: {
        level: {
            sources: ['env:LOG_LEVEL'],
            default: 'WARN',
            type: 'string',
            enum: ['DEBUG', 'INFO', 'WARN', 'ERROR'],
            description: '日志输出级别'
        },
        console: {
            sources: ['env:LOG_CONSOLE', 'db:log_console_output'],
            default: true,
            type: 'boolean',
            description: '控制台日志输出开关'
        },
        file: {
            sources: ['env:LOG_FILE', 'db:log_file_path'],
            default: './logs/dstatus.log',
            type: 'string',
            description: '日志文件路径'
        },
        aggregation: {
            sources: ['env:LOG_AGGREGATION', 'db:log_aggregation_enabled'],
            default: true,
            type: 'boolean',
            description: '日志聚合功能开关'
        },
        maxFileSize: {
            sources: ['env:LOG_MAX_SIZE', 'db:log_max_file_size'],
            default: '10M',
            type: 'string',
            description: '日志文件最大大小'
        },
        maxFiles: {
            sources: ['env:LOG_MAX_FILES', 'db:log_max_files'],
            default: 5,
            type: 'integer',
            min: 1,
            max: 50,
            description: '日志文件最大保留数量'
        }
    },

    // 网络配置
    network: {
        timeout: {
            sources: ['env:NETWORK_TIMEOUT', 'db:network_timeout'],
            default: 15000,
            type: 'integer',
            min: 5000,
            max: 60000,
            description: '网络请求超时时间(毫秒)'
        },
        retries: {
            sources: ['env:NETWORK_RETRIES', 'db:network_retries'],
            default: 3,
            type: 'integer',
            min: 0,
            max: 10,
            description: '网络请求重试次数'
        }
    },

    // 缓存配置
    cache: {
        trafficCacheTtl: {
            sources: ['env:TRAFFIC_CACHE_TTL', 'db:traffic_cache_ttl'],
            default: 60000,
            type: 'integer',
            min: 10000,
            max: 300000,
            description: '流量数据缓存TTL(毫秒)'
        },
        statsCacheTtl: {
            sources: ['env:STATS_CACHE_TTL', 'db:stats_cache_ttl'],
            default: 30000,
            type: 'integer',
            min: 5000,
            max: 120000,
            description: '状态数据缓存TTL(毫秒)'
        },
        dataRetentionTtl: {
            sources: ['env:DATA_RETENTION_TTL', 'db:data_retention_ttl'],
            default: 3600000,
            type: 'integer',
            min: 300000,
            max: 86400000,
            description: '数据保留缓存TTL(毫秒) - 1小时默认'
        },
        queryMonitoringInterval: {
            sources: ['env:QUERY_MONITORING_INTERVAL', 'db:query_monitoring_interval'],
            default: 60000,
            type: 'integer',
            min: 10000,
            max: 300000,
            description: '查询监控间隔(毫秒)'
        }
    },

    // 认证配置
    auth: {
        loginPath: {
            sources: ['db:auth_login_path'],
            default: '/admin-login',
            type: 'string',
            description: '管理员登录入口路径',
            validation: (value) => /^\/[a-zA-Z0-9\-_]{4,49}$/.test(value)
        },
        allowLoginPathRandomize: {
            sources: ['db:auth_allow_login_path_randomize'],
            default: true,
            type: 'boolean',
            description: '是否允许随机化登录入口'
        },
        notifyOnLoginPathChange: {
            sources: ['db:auth_notify_on_login_path_change'],
            default: true,
            type: 'boolean',
            description: '登录入口变更时是否发送通知'
        },
        githubTrust2fa: {
            sources: ['db:auth_github_trust_2fa'],
            default: true,
            type: 'boolean',
            description: 'GitHub登录是否视为满足2FA'
        },
        githubAllowedAccounts: {
            sources: ['db:auth_github_allowed_accounts'],
            default: [],
            type: 'array',
            description: 'GitHub账号白名单（必填，降低误授权风险）'
        },
        githubAllowedOrganizations: {
            sources: ['db:auth_github_allowed_organizations'],
            default: [],
            type: 'array',
            description: 'GitHub组织白名单'
        },
        reminderDaily: {
            sources: ['db:auth_reminder_daily'],
            default: true,
            type: 'boolean',
            description: '是否开启每日首次登录2FA提醒'
        },
        'github.clientId': {
            sources: ['env:GITHUB_CLIENT_ID', 'db:auth_github_client_id'],
            default: null,
            type: 'string',
            description: 'GitHub OAuth应用客户端ID',
            sensitive: false
        },
        'github.clientSecret': {
            sources: ['env:GITHUB_CLIENT_SECRET', 'db:auth_github_client_secret'],
            default: null,
            type: 'string',
            description: 'GitHub OAuth应用客户端密钥',
            sensitive: true
        }
    },

    // 安全配置
    security: {
        maxFailedAttempts: {
            sources: ['env:MAX_FAILED_ATTEMPTS', 'db:max_failed_attempts'],
            default: 5,
            type: 'integer',
            min: 3,
            max: 20,
            description: '最大失败尝试次数'
        },
        cooldownPeriod: {
            sources: ['env:COOLDOWN_PERIOD', 'db:cooldown_period'],
            default: 300000,
            type: 'integer',
            min: 60000,
            max: 3600000,
            description: '冷却期时间(毫秒)'
        },
        failThreshold: {
            sources: ['env:FAIL_THRESHOLD', 'db:fail_threshold'],
            default: 3,
            type: 'integer',
            min: 1,
            max: 10,
            description: '失败阈值 - 触发冷却的连续失败次数'
        },
        initialCoolingTime: {
            sources: ['env:INITIAL_COOLING_TIME', 'db:initial_cooling_time'],
            default: 5,
            type: 'integer',
            min: 1,
            max: 60,
            description: '初始冷却时间(分钟)'
        },
        maxCoolingTime: {
            sources: ['env:MAX_COOLING_TIME', 'db:max_cooling_time'],
            default: 60,
            type: 'integer',
            min: 5,
            max: 1440,
            description: '最大冷却时间(分钟)'
        },
        coolingFactor: {
            sources: ['env:COOLING_FACTOR', 'db:cooling_factor'],
            default: 2,
            type: 'integer',
            min: 1,
            max: 10,
            description: '冷却时间增长因子'
        },
        jwtSecret: {
            sources: ['env:JWT_SECRET', 'db:security_jwt_secret'],
            default: null,
            type: 'string',
            description: 'JWT签名密钥（自动生成）',
            sensitive: true
        },
        jwtAccessTokenExpiry: {
            sources: ['db:security_jwt_access_token_expiry'],
            default: '15m',
            type: 'string',
            description: 'Access Token过期时间'
        },
        jwtRefreshTokenExpiry: {
            sources: ['db:security_jwt_refresh_token_expiry'],
            default: '7d',
            type: 'string',
            description: 'Refresh Token过期时间'
        },
        totpEncryptionKey: {
            sources: ['db:security_totp_encryption_key'],
            default: null,
            type: 'string',
            description: 'TOTP密钥加密密钥（自动生成）',
            sensitive: true
        },
        loginRateLimitMaxAttempts: {
            sources: ['db:security_login_rate_limit_max_attempts'],
            default: 5,
            type: 'integer',
            min: 3,
            max: 20,
            description: '最大登录尝试次数'
        },
        loginRateLimitWindowMinutes: {
            sources: ['db:security_login_rate_limit_window_minutes'],
            default: 15,
            type: 'integer',
            min: 5,
            max: 60,
            description: '限速时间窗口（分钟）'
        },
        loginRateLimitLockoutMinutes: {
            sources: ['db:security_login_rate_limit_lockout_minutes'],
            default: 30,
            type: 'integer',
            min: 10,
            max: 120,
            description: '锁定时长（分钟）'
        },
        loginSendPathMaxAttempts: {
            sources: ['db:security_login_send_path_max_attempts'],
            default: 3,
            type: 'integer',
            min: 1,
            max: 10,
            description: '发送登录入口最大次数'
        },
        loginSendPathWindowMinutes: {
            sources: ['db:security_login_send_path_window_minutes'],
            default: 60,
            type: 'integer',
            min: 30,
            max: 180,
            description: '发送入口限速窗口（分钟）'
        }
    },

    // 外部服务配置
    services: {
        licenseServerUrl: {
            sources: ['env:LICENSE_SERVER_URL'],
            default: 'https://dstatus_api.vps.mom',
            type: 'string',
            description: '许可证服务器URL'
        },
        userFrontendUrl: {
            sources: ['env:USER_FRONTEND_URL'],
            default: 'https://client.vps.mom/',
            type: 'string',
            description: '用户前端URL'
        },
        staticPath: {
            sources: ['env:STATIC_PATH'],
            default: path.join(__dirname, '../static'),
            type: 'string',
            description: '静态文件路径'
        },
        viewsPath: {
            sources: ['env:VIEWS_PATH'],
            default: path.join(__dirname, '../views'),
            type: 'string',
            description: '视图模板路径'
        },
        tokensPath: {
            sources: ['env:TOKENS_PATH'],
            default: path.join(__dirname, '../data'),
            type: 'string',
            description: '令牌存储路径'
        }
    },

    // 服务器配置
    server: {
        port: {
            sources: ['env:PORT'],
            default: 3001,
            type: 'integer',
            min: 1000,
            max: 65535,
            description: '服务器端口'
        },
        host: {
            sources: ['env:HOST'],
            default: '0.0.0.0',
            type: 'string',
            description: '服务器监听地址'
        }
    },

    // 调度任务配置
    scheduler: {
        dataCollectionCron: {
            sources: ['env:DATA_COLLECTION_CRON', 'db:data_collection_cron'],
            default: '{second:0}',
            type: 'string',
            description: '数据收集定时任务表达式 - 每分钟'
        },
        hourlyAggregationCron: {
            sources: ['env:HOURLY_AGGREGATION_CRON', 'db:hourly_aggregation_cron'],
            default: '{minute:0,second:1}',
            type: 'string',
            description: '小时聚合定时任务表达式'
        },
        dailyTrafficShiftCron: {
            sources: ['env:DAILY_TRAFFIC_SHIFT_CRON', 'db:daily_traffic_shift_cron'],
            default: '{hour:4,minute:0,second:2}',
            type: 'string',
            description: '每日流量切换定时任务表达式 - 凌晨4点'
        },
        monthlyTrafficShiftCron: {
            sources: ['env:MONTHLY_TRAFFIC_SHIFT_CRON', 'db:monthly_traffic_shift_cron'],
            default: '{date:1,hour:4,minute:0,second:3}',
            type: 'string',
            description: '月度流量切换定时任务表达式 - 每月1日凌晨4点'
        },
        trafficStatsUpdateCron: {
            sources: ['env:TRAFFIC_STATS_UPDATE_CRON', 'db:traffic_stats_update_cron'],
            default: '0 * * * *',
            type: 'string',
            description: '流量统计更新定时任务表达式 - 每小时'
        },
        activeNodeCheckCron: {
            sources: ['env:ACTIVE_NODE_CHECK_CRON', 'db:active_node_check_cron'],
            default: '* * * * *',
            type: 'string',
            description: '活跃节点检查定时任务表达式 - 每分钟'
        },
        ipLocationRetryCron: {
            sources: ['env:IP_LOCATION_RETRY_CRON', 'db:ip_location_retry_cron'],
            default: '*/30 * * * *',
            type: 'string',
            description: 'IP地址位置重试定时任务表达式 - 每30分钟'
        }
    },

    // UI配置
    ui: {
        loadingAnimationDelay: {
            sources: ['env:LOADING_ANIMATION_DELAY', 'db:loading_animation_delay'],
            default: 10,
            type: 'integer',
            min: 0,
            max: 1000,
            description: '加载动画延迟(毫秒)'
        },
        loadingFadeoutDuration: {
            sources: ['env:LOADING_FADEOUT_DURATION', 'db:loading_fadeout_duration'],
            default: 300,
            type: 'integer',
            min: 100,
            max: 2000,
            description: '加载消失动画持续时间(毫秒)'
        },
        noticeAutoCloseDuration: {
            sources: ['env:NOTICE_AUTO_CLOSE_DURATION', 'db:notice_auto_close_duration'],
            default: 3000,
            type: 'integer',
            min: 1000,
            max: 10000,
            description: '通知自动关闭时间(毫秒)'
        }
    },

    // 通知配置
    notifications: {
        loginEventsSuccess: {
            sources: ['db:notifications_login_events_success'],
            default: false,
            type: 'boolean',
            description: '登录成功通知（采样）'
        },
        loginEventsFailure: {
            sources: ['db:notifications_login_events_failure'],
            default: true,
            type: 'boolean',
            description: '登录失败通知（去抖）'
        },
        loginEventsLockout: {
            sources: ['db:notifications_login_events_lockout'],
            default: true,
            type: 'boolean',
            description: '账户锁定通知（即时）'
        },
        loginEventsPathChange: {
            sources: ['db:notifications_login_events_path_change'],
            default: true,
            type: 'boolean',
            description: '登录入口变更通知（即时）'
        },
        loginEventsTwoFactorChange: {
            sources: ['db:notifications_login_events_two_factor_change'],
            default: true,
            type: 'boolean',
            description: '2FA状态变更通知'
        }
    },

    // 数据保留配置
    retention: {
        archiveHours: {
            sources: ['env:ARCHIVE_HOURS', 'db:archive_hours'],
            default: 3,
            type: 'integer',
            min: 1,
            max: 24,
            description: '实时数据保留小时数'
        },
        minuteDays: {
            sources: ['env:MINUTE_DAYS', 'db:minute_days'],
            default: 14,
            type: 'integer',
            min: 1,
            max: 90,
            description: '分钟级数据保留天数'
        },
        hourDays: {
            sources: ['env:HOUR_DAYS', 'db:hour_days'],
            default: 90,
            type: 'integer',
            min: 7,
            max: 365,
            description: '小时级数据保留天数'
        }
    }
};

// 配置元数据
const CONFIG_META = {
    version: '1.0.0',
    lastUpdated: new Date().toISOString(),
    schemaVersion: '1.0',
    description: 'DStatus 全局配置管理系统',
    
    // 配置分组信息
    groups: {
        system: { name: '系统配置', priority: 1 },
        database: { name: '数据库配置', priority: 2 },
        websocket: { name: 'WebSocket配置', priority: 3 },
        monitoring: { name: '监控配置', priority: 4 },
        batch: { name: '批处理配置', priority: 5 },
        logging: { name: '日志配置', priority: 6 },
        network: { name: '网络配置', priority: 7 },
        cache: { name: '缓存配置', priority: 8 },
        auth: { name: '认证配置', priority: 9 },
        security: { name: '安全配置', priority: 10 },
        notifications: { name: '通知配置', priority: 11 },
        services: { name: '外部服务配置', priority: 12 },
        server: { name: '服务器配置', priority: 13 },
        scheduler: { name: '调度任务配置', priority: 14 },
        ui: { name: 'UI界面配置', priority: 15 },
        retention: { name: '数据保留配置', priority: 16 }
    }
};

module.exports = {
    CONFIG_SCHEMA,
    CONFIG_META
};