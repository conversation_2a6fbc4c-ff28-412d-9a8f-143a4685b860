/**
 * 性能优化与数据保留设置 JavaScript
 */

class PerformanceRetentionManager {
    constructor() {
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.bindDetailedConfigEvents();
        this.loadCurrentSettings();
    }

    bindEvents() {
        // 性能模式按钮事件
        document.querySelectorAll('.perf-mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (this.isLoading) return;
                const mode = e.target.dataset.mode;
                this.setPerformanceMode(mode, e.target);
            });
        });

        // 数据保留按钮事件
        document.querySelectorAll('.retention-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (this.isLoading) return;
                const preset = e.target.dataset.preset;
                this.setDataRetention(preset, e.target);
            });
        });
    }

    bindDetailedConfigEvents() {
        // 监听详细配置输入框的变化
        const pollingInput = document.querySelector('[key="polling_interval_seconds"]');
        const websocketInput = document.querySelector('[key="websocket_interval_seconds"]');
        
        if (pollingInput) {
            pollingInput.addEventListener('change', () => {
                this.syncFromDetailedConfig();
            });
        }
        
        if (websocketInput) {
            websocketInput.addEventListener('change', () => {
                this.syncFromDetailedConfig();
            });
        }
    }

    syncFromDetailedConfig() {
        // 从详细配置同步到紧凑组件
        const pollingInput = document.querySelector('[key="polling_interval_seconds"]');
        const websocketInput = document.querySelector('[key="websocket_interval_seconds"]');
        
        if (pollingInput && websocketInput) {
            const pollingMs = Math.round(parseFloat(pollingInput.value) * 1000);
            const websocketMs = Math.round(parseFloat(websocketInput.value) * 1000);
            
            // 判断当前配置对应的模式
            const mode = this.detectModeFromValues(pollingMs, websocketMs);
            this.updatePerformanceModeButtons(mode);
        }
    }

    detectModeFromValues(polling, websocket) {
        // 允许误差范围（±500ms）
        const tolerance = 500;
        
        // 节能模式：10s/12s
        if (Math.abs(polling - 10000) <= tolerance && Math.abs(websocket - 12000) <= tolerance) {
            return 'eco';
        }
        
        // 平衡模式：3s/4s
        if (Math.abs(polling - 3000) <= tolerance && Math.abs(websocket - 4000) <= tolerance) {
            return 'balanced';
        }
        
        // 高性能模式：1.5s/2s
        if (Math.abs(polling - 1500) <= tolerance && Math.abs(websocket - 2000) <= tolerance) {
            return 'performance';
        }
        
        // 自定义配置
        return 'custom';
    }

    syncToDetailedConfig(mode, config) {
        // 从紧凑组件同步到详细配置
        const pollingInput = document.querySelector('[key="polling_interval_seconds"]');
        const websocketInput = document.querySelector('[key="websocket_interval_seconds"]');
        const pollingHidden = document.getElementById('polling_interval_hidden');
        const websocketHidden = document.getElementById('websocket_interval_hidden');
        
        if (pollingInput && config) {
            pollingInput.value = config.polling_interval / 1000;
            if (pollingHidden) {
                pollingHidden.value = config.polling_interval;
            }
        }
        
        if (websocketInput && config) {
            websocketInput.value = config.websocket_interval / 1000;
            if (websocketHidden) {
                websocketHidden.value = config.websocket_interval;
            }
        }
    }

    async loadCurrentSettings() {
        try {
            // 加载数据保留设置
            const retentionResponse = await fetch('/api/settings/data-retention');
            if (retentionResponse.ok) {
                const retentionData = await retentionResponse.json();
                if (retentionData.status === 1) {
                    this.updateRetentionButtons(retentionData.data.current_preset || 'standard');
                }
            }

            // 加载性能模式设置
            try {
                const perfResponse = await fetch('/api/settings/performance-mode');
                if (perfResponse.ok) {
                    const perfData = await perfResponse.json();
                    if (perfData.status === 1) {
                        this.updatePerformanceModeButtons(perfData.data.mode || 'balanced');
                    }
                } else {
                    // 如果API不存在，使用默认值
                    this.updatePerformanceModeButtons('balanced');
                }
            } catch (perfError) {
                console.log('性能模式API暂不可用，使用默认值');
                this.updatePerformanceModeButtons('balanced');
            }
            
        } catch (error) {
            console.error('加载当前设置失败:', error);
        }
    }

    async setPerformanceMode(mode, button) {
        this.isLoading = true;
        
        try {
            // 更新UI
            this.updatePerformanceModeButtons(mode);
            
            // 调用性能模式API
            const response = await fetch('/api/settings/performance-mode', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ mode })
            });
            
            const data = await response.json();
            if (data.status === 1) {
                this.showToast(`性能模式已切换到${this.getModeText(mode)}`, 'success');
                
                // 同步到详细配置
                if (data.data && data.data.config) {
                    this.syncToDetailedConfig(mode, data.data.config);
                }
            } else {
                throw new Error(data.data || '设置失败');
            }
            
        } catch (error) {
            console.error('设置性能模式失败:', error);
            this.showToast('设置失败，请重试', 'error');
            // 如果失败，恢复之前的UI状态
            this.loadCurrentSettings();
        } finally {
            this.isLoading = false;
        }
    }

    async setDataRetention(preset, button) {
        this.isLoading = true;
        
        try {
            // 更新UI
            this.updateRetentionButtons(preset);
            
            // 调用现有的数据保留API
            const response = await fetch('/api/settings/data-retention', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ preset })
            });
            
            const data = await response.json();
            if (data.status === 1) {
                this.showToast(`数据保留已切换到${this.getPresetText(preset)}模式`, 'success');
            } else {
                throw new Error(data.data || '设置失败');
            }
            
        } catch (error) {
            console.error('设置数据保留失败:', error);
            this.showToast('设置失败，请重试', 'error');
        } finally {
            this.isLoading = false;
        }
    }

    updatePerformanceModeButtons(activeMode) {
        document.querySelectorAll('.perf-mode-btn').forEach(btn => {
            const isActive = btn.dataset.mode === activeMode;
            btn.classList.toggle('bg-white', isActive);
            btn.classList.toggle('dark:bg-slate-600', isActive);
            btn.classList.toggle('text-slate-800', isActive);
            btn.classList.toggle('dark:text-white', isActive);
            btn.classList.toggle('shadow-sm', isActive);
            btn.classList.toggle('text-slate-600', !isActive);
            btn.classList.toggle('dark:text-slate-400', !isActive);
        });
        
        // 如果是自定义模式，取消所有按钮的激活状态
        if (activeMode === 'custom') {
            document.querySelectorAll('.perf-mode-btn').forEach(btn => {
                btn.classList.remove('bg-white', 'dark:bg-slate-600', 'text-slate-800', 'dark:text-white', 'shadow-sm');
                btn.classList.add('text-slate-600', 'dark:text-slate-400');
                // 添加自定义模式的视觉提示
                btn.classList.add('opacity-60');
            });
        } else {
            // 移除自定义模式的视觉提示
            document.querySelectorAll('.perf-mode-btn').forEach(btn => {
                btn.classList.remove('opacity-60');
            });
        }
    }

    updateRetentionButtons(activePreset) {
        document.querySelectorAll('.retention-btn').forEach(btn => {
            const isActive = btn.dataset.preset === activePreset;
            btn.classList.toggle('bg-white', isActive);
            btn.classList.toggle('dark:bg-slate-600', isActive);
            btn.classList.toggle('text-slate-800', isActive);
            btn.classList.toggle('dark:text-white', isActive);
            btn.classList.toggle('shadow-sm', isActive);
            btn.classList.toggle('text-slate-600', !isActive);
            btn.classList.toggle('dark:text-slate-400', !isActive);
        });
    }

    getModeText(mode) {
        const texts = {
            eco: '节能',
            balanced: '平衡',
            performance: '高性能'
        };
        return texts[mode] || '未知';
    }

    getPresetText(preset) {
        const texts = {
            minimal: '精简',
            standard: '标准',
            extended: '扩展'
        };
        return texts[preset] || '未知';
    }

    showToast(message, type = 'info') {
        // 使用现有的通知系统，如果存在
        if (window.showToast) {
            window.showToast(message, type);
        } else if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            // 简单的控制台输出作为后备
            console.log(`[${type}] ${message}`);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.perf-mode-btn') || document.querySelector('.retention-btn')) {
        new PerformanceRetentionManager();
    }
});