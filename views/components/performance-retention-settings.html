<!-- 性能优化与历史数据保留设置组件 -->
<div class="bg-white/70 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/60 dark:border-slate-700/60 rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300">
    <!-- 标题 -->
    <div class="flex items-center gap-2 mb-4">
        <div class="w-8 h-8 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
        </div>
        <h3 class="text-sm font-medium text-slate-800 dark:text-slate-200">性能与数据管理</h3>
    </div>

    <!-- 响应式网格布局：移动端垂直，PC端水平 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 性能模式设置 -->
        <div class="space-y-2">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <span class="text-xs font-medium text-slate-600 dark:text-slate-400">性能模式</span>
                    <div class="text-xs text-slate-500 dark:text-slate-500">调整资源使用</div>
                </div>
            </div>
            <div class="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-0.5 w-full">
                <button type="button" class="perf-mode-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200" data-mode="eco">节能</button>
                <button type="button" class="perf-mode-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200 bg-white dark:bg-slate-600 text-slate-800 dark:text-white shadow-sm" data-mode="balanced">平衡</button>
                <button type="button" class="perf-mode-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200" data-mode="performance">高性能</button>
            </div>
            <!-- 性能参数显示 -->
            <div class="parameter-display">
                <div class="text-xs text-slate-600 dark:text-slate-400">
                    <span class="param-label">轮询间隔:</span>
                    <span class="param-value text-blue-500 dark:text-blue-300 font-semibold" id="polling-interval">3秒</span>
                    <span class="param-separator opacity-50 mx-2">|</span>
                    <span class="param-label">WebSocket:</span>
                    <span class="param-value text-blue-500 dark:text-blue-300 font-semibold" id="websocket-interval">4秒</span>
                </div>
            </div>
        </div>

        <!-- 数据保留设置 -->
        <div class="space-y-2">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <span class="text-xs font-medium text-slate-600 dark:text-slate-400">数据保留</span>
                    <div class="text-xs text-slate-500 dark:text-slate-500">历史数据时长</div>
                </div>
            </div>
            <div class="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-0.5 w-full">
                <button type="button" class="retention-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200" data-preset="minimal">精简</button>
                <button type="button" class="retention-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200 bg-white dark:bg-slate-600 text-slate-800 dark:text-white shadow-sm" data-preset="standard">标准</button>
                <button type="button" class="retention-btn flex-1 px-2 py-1.5 text-xs font-medium rounded-md transition-all duration-200" data-preset="extended">扩展</button>
            </div>
            <!-- 数据保留参数显示 -->
            <div class="parameter-display">
                <div class="text-xs text-slate-600 dark:text-slate-400">
                    <span class="param-label">实时:</span>
                    <span class="param-value text-blue-500 dark:text-blue-300 font-semibold" id="archive-duration">3小时</span>
                    <span class="param-separator opacity-50 mx-1">|</span>
                    <span class="param-label">分钟级:</span>
                    <span class="param-value text-blue-500 dark:text-blue-300 font-semibold" id="minute-duration">14天</span>
                    <span class="param-separator opacity-50 mx-1">|</span>
                    <span class="param-label">小时级:</span>
                    <span class="param-value text-blue-500 dark:text-blue-300 font-semibold" id="hour-duration">90天</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用说明 -->
    <div class="usage-instructions mt-3 pt-3 border-t border-slate-200/60 dark:border-slate-700/60">
        <div class="text-xs text-slate-500 dark:text-slate-500 text-center">
            选择模式自动调整系统参数，重启后生效
        </div>
    </div>
</div>

<script>
(function() {
    'use strict';
    
    class PerformanceRetentionManager {
        constructor() {
            this.performanceModes = {};
            this.retentionPresets = {};
            this.currentPerformanceMode = 'balanced';
            this.currentRetentionMode = 'standard';
            this.perfAutoSaveManager = null;
            this.retentionAutoSaveManager = null;
            this.init();
        }
        
        async init() {
            await this.loadConfigurations();
            this.bindEvents();
            this.updateDisplays();
            this.setupAutoSave();
        }
        
        async loadConfigurations() {
            try {
                const [perfResponse, retentionResponse] = await Promise.all([
                    fetch('/api/settings/performance-mode'),
                    fetch('/api/settings/data-retention')
                ]);
                
                const perfData = await perfResponse.json();
                const retentionData = await retentionResponse.json();
                
                if (perfData.success && perfData.data) {
                    this.performanceModes = perfData.data.modes || {};
                    this.currentPerformanceMode = perfData.data.mode || 'balanced';
                }
                
                if (retentionData.success && retentionData.data) {
                    this.retentionPresets = retentionData.data.presets || {};
                    this.currentRetentionMode = retentionData.data.current_preset || 'standard';
                }
            } catch (error) {
                console.error('Failed to load configurations:', error);
                this.useDefaultValues();
            }
        }
        
        useDefaultValues() {
            this.performanceModes = {
                eco: { polling_interval: 10000, websocket_interval: 12000 },
                balanced: { polling_interval: 3000, websocket_interval: 4000 },
                performance: { polling_interval: 1500, websocket_interval: 2000 }
            };
            this.retentionPresets = {
                minimal: { archive_hours: 1, minute_days: 7, hour_days: 30 },
                standard: { archive_hours: 3, minute_days: 14, hour_days: 90 },
                extended: { archive_hours: 6, minute_days: 30, hour_days: 180 }
            };
        }
        
        bindEvents() {
            document.querySelectorAll('.perf-mode-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    this.handlePerformanceModeChange(e.target.dataset.mode);
                });
            });
            
            document.querySelectorAll('.retention-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    this.handleRetentionModeChange(e.target.dataset.preset);
                });
            });
        }
        
        async handlePerformanceModeChange(mode) {
            // 立即更新当前模式和UI
            this.currentPerformanceMode = mode;
            this.updateActiveButton('.perf-mode-btn', mode, 'mode');
            
            // 如果有自动保存管理器，触发变更事件让它处理保存
            if (this.perfAutoSaveManager) {
                this.perfAutoSaveManager.handleChange();
            } else {
                // 没有自动保存管理器时，直接调用API保存
                try {
                    const response = await fetch('/api/settings/performance-mode', {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ mode })
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                    
                        // 从API响应中更新配置缓存和显示
                        if (result.data && result.data.config) {
                            // 更新缓存中的配置
                            this.performanceModes[mode] = result.data.config;
                            
                            // 直接使用API返回的实际值更新显示
                            const pollingElement = document.getElementById('polling-interval');
                            const websocketElement = document.getElementById('websocket-interval');
                            
                            if (pollingElement) {
                                pollingElement.textContent = `${result.data.config.polling_interval / 1000}秒`;
                            }
                            if (websocketElement) {
                                websocketElement.textContent = `${result.data.config.websocket_interval / 1000}秒`;
                            }
                        } else {
                            // 如果响应中没有配置数据，使用缓存更新
                            this.updatePerformanceDisplay();
                        }
                        
                        if (window.showNotification) {
                            window.showNotification('性能模式已切换', 'success');
                        }
                    }
                } catch (error) {
                    console.error('Failed to update performance mode:', error);
                    if (window.showNotification) {
                        window.showNotification('切换性能模式失败', 'error');
                    }
                }
            }
        }
        
        async handleRetentionModeChange(preset) {
            // 立即更新当前模式和UI
            this.currentRetentionMode = preset;
            this.updateActiveButton('.retention-btn', preset, 'preset');
            
            // 如果有自动保存管理器，触发变更事件让它处理保存
            if (this.retentionAutoSaveManager) {
                this.retentionAutoSaveManager.handleChange();
            } else {
                // 没有自动保存管理器时，直接调用API保存
                try {
                    const response = await fetch('/api/settings/data-retention', {
                        method: 'PUT', 
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ preset })
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                    
                        // 从API响应中更新配置缓存和显示
                        if (result.data && result.data.config) {
                            // 更新缓存中的配置
                            this.retentionPresets[preset] = result.data.config;
                            
                            // 直接使用API返回的实际值更新显示
                            const archiveElement = document.getElementById('archive-duration');
                            const minuteElement = document.getElementById('minute-duration');
                            const hourElement = document.getElementById('hour-duration');
                            
                            if (archiveElement) {
                                archiveElement.textContent = `${result.data.config.archive_hours}小时`;
                            }
                            if (minuteElement) {
                                minuteElement.textContent = `${result.data.config.minute_days}天`;
                            }
                            if (hourElement) {
                                hourElement.textContent = `${result.data.config.hour_days}天`;
                            }
                        } else {
                            // 如果响应中没有配置数据，使用缓存更新
                            this.updateRetentionDisplay();
                        }
                        
                        if (window.showNotification) {
                            window.showNotification('数据保留设置已更新', 'success');
                        }
                    }
                } catch (error) {
                    console.error('Failed to update retention mode:', error);
                    if (window.showNotification) {
                        window.showNotification('更新数据保留设置失败', 'error');
                    }
                }
            }
        }
        
        updatePerformanceDisplay() {
            const config = this.performanceModes[this.currentPerformanceMode];
            if (config) {
                const pollingElement = document.getElementById('polling-interval');
                const websocketElement = document.getElementById('websocket-interval');
                
                if (pollingElement) {
                    pollingElement.textContent = `${config.polling_interval / 1000}秒`;
                }
                if (websocketElement) {
                    websocketElement.textContent = `${config.websocket_interval / 1000}秒`;
                }
            }
        }
        
        updateRetentionDisplay() {
            const config = this.retentionPresets[this.currentRetentionMode];
            if (config) {
                const archiveElement = document.getElementById('archive-duration');
                const minuteElement = document.getElementById('minute-duration');
                const hourElement = document.getElementById('hour-duration');
                
                if (archiveElement) {
                    archiveElement.textContent = `${config.archive_hours}小时`;
                }
                if (minuteElement) {
                    minuteElement.textContent = `${config.minute_days}天`;
                }
                if (hourElement) {
                    hourElement.textContent = `${config.hour_days}天`;
                }
            }
        }
        
        updateActiveButton(selector, activeValue, dataAttr) {
            document.querySelectorAll(selector).forEach(btn => {
                btn.classList.remove('bg-white', 'dark:bg-slate-600', 'text-slate-800', 'dark:text-white', 'shadow-sm');
                btn.classList.add('text-slate-600', 'dark:text-slate-400');
                
                if (btn.dataset[dataAttr] === activeValue) {
                    btn.classList.remove('text-slate-600', 'dark:text-slate-400');
                    btn.classList.add('bg-white', 'dark:bg-slate-600', 'text-slate-800', 'dark:text-white', 'shadow-sm');
                }
            });
        }
        
        updateDisplays() {
            this.updatePerformanceDisplay();
            this.updateRetentionDisplay();
            this.updateActiveButton('.perf-mode-btn', this.currentPerformanceMode, 'mode');
            this.updateActiveButton('.retention-btn', this.currentRetentionMode, 'preset');
        }
        
        setupAutoSave() {
            // 检查是否存在 AutoSaveManager 类
            if (typeof AutoSaveManager === 'undefined') {
                console.warn('[性能设置] AutoSaveManager 未加载，使用传统保存模式');
                return;
            }
            
            try {
                // 性能模式自动保存配置
                this.perfAutoSaveManager = new AutoSaveManager({
                    apiEndpoint: '/api/settings/performance-mode',
                    collectData: () => this.collectPerformanceData(),
                    validateData: (data) => this.validatePerformanceData(data),
                    onSuccess: (result) => this.onPerformanceSaveSuccess(result),
                    onError: (error) => this.onPerformanceSaveError(error),
                    debounceMs: 2500,
                    statusIndicator: '#perf-auto-save-status',
                    watchSelector: '.perf-mode-btn',
                    enabled: false // 禁用自动监听，我们手动触发
                });
                
                // 覆盖performSave方法以使用PUT方法
                this.perfAutoSaveManager.performSave = async function() {
                    if (!this.state.hasChanges || this.state.isSaving) return;
                    
                    this.state.isSaving = true;
                    this.updateStatus('saving', '保存中...');
                    
                    try {
                        const data = await this.collectData();
                        
                        if (this.options.validateData) {
                            const validation = await this.options.validateData(data);
                            if (!validation.valid) {
                                throw new Error(validation.message || '数据验证失败');
                            }
                        }
                        
                        if (this.isDuplicateSave(data)) {
                            this.updateStatus('success', '已保存');
                            return;
                        }
                        
                        const response = await this.apiRequest(this.options.apiEndpoint, {
                            method: 'PUT', // 使用PUT方法
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(data)
                        });
                        
                        if (!response.ok) {
                            throw new Error(`保存失败: ${response.status}`);
                        }
                        
                        const result = await response.json();
                        
                        if (!this.isSuccessResponse(result)) {
                            throw new Error(result.message || '保存失败');
                        }
                        
                        this.state.hasChanges = false;
                        this.state.lastSavedData = JSON.stringify(data);
                        this.updateStatus('success', '已保存');
                        
                        if (this.options.onSuccess) {
                            this.options.onSuccess(result);
                        }
                        
                        console.log('[AutoSave] 保存成功');
                        
                    } catch (error) {
                        console.error('[AutoSave] 保存失败:', error);
                        this.updateStatus('error', '保存失败');
                        
                        if (this.options.onError) {
                            this.options.onError(error);
                        }
                    } finally {
                        this.state.isSaving = false;
                    }
                }.bind(this.perfAutoSaveManager);
                
                // 启用自动保存
                this.perfAutoSaveManager.state.isEnabled = true;
                
                // 数据保留自动保存配置
                this.retentionAutoSaveManager = new AutoSaveManager({
                    apiEndpoint: '/api/settings/data-retention',
                    collectData: () => this.collectRetentionData(),
                    validateData: (data) => this.validateRetentionData(data),
                    onSuccess: (result) => this.onRetentionSaveSuccess(result),
                    onError: (error) => this.onRetentionSaveError(error),
                    debounceMs: 2500,
                    statusIndicator: '#retention-auto-save-status',
                    watchSelector: '.retention-btn',
                    enabled: false // 禁用自动监听，我们手动触发
                });
                
                // 覆盖performSave方法以使用PUT方法
                this.retentionAutoSaveManager.performSave = async function() {
                    if (!this.state.hasChanges || this.state.isSaving) return;
                    
                    this.state.isSaving = true;
                    this.updateStatus('saving', '保存中...');
                    
                    try {
                        const data = await this.collectData();
                        
                        if (this.options.validateData) {
                            const validation = await this.options.validateData(data);
                            if (!validation.valid) {
                                throw new Error(validation.message || '数据验证失败');
                            }
                        }
                        
                        if (this.isDuplicateSave(data)) {
                            this.updateStatus('success', '已保存');
                            return;
                        }
                        
                        const response = await this.apiRequest(this.options.apiEndpoint, {
                            method: 'PUT', // 使用PUT方法
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(data)
                        });
                        
                        if (!response.ok) {
                            throw new Error(`保存失败: ${response.status}`);
                        }
                        
                        const result = await response.json();
                        
                        if (!this.isSuccessResponse(result)) {
                            throw new Error(result.message || '保存失败');
                        }
                        
                        this.state.hasChanges = false;
                        this.state.lastSavedData = JSON.stringify(data);
                        this.updateStatus('success', '已保存');
                        
                        if (this.options.onSuccess) {
                            this.options.onSuccess(result);
                        }
                        
                        console.log('[AutoSave] 保存成功');
                        
                    } catch (error) {
                        console.error('[AutoSave] 保存失败:', error);
                        this.updateStatus('error', '保存失败');
                        
                        if (this.options.onError) {
                            this.options.onError(error);
                        }
                    } finally {
                        this.state.isSaving = false;
                    }
                }.bind(this.retentionAutoSaveManager);
                
                // 启用自动保存
                this.retentionAutoSaveManager.state.isEnabled = true;
                
                console.log('[性能设置] 自动保存已启用');
            } catch (error) {
                console.error('[性能设置] 自动保存初始化失败:', error);
            }
        }
        
        // 性能模式数据收集
        collectPerformanceData() {
            return {
                mode: this.currentPerformanceMode
            };
        }
        
        // 性能模式数据验证
        validatePerformanceData(data) {
            const validModes = ['eco', 'balanced', 'performance'];
            return {
                valid: validModes.includes(data.mode),
                message: validModes.includes(data.mode) ? '' : '无效的性能模式'
            };
        }
        
        // 性能模式保存成功回调
        onPerformanceSaveSuccess(result) {
            console.log('[性能设置] 性能模式自动保存成功:', result);
            // 如果返回了新配置，更新显示
            if (result.data && result.data.config) {
                this.performanceModes[this.currentPerformanceMode] = result.data.config;
                this.updatePerformanceDisplay();
            }
        }
        
        // 性能模式保存错误回调
        onPerformanceSaveError(error) {
            console.error('[性能设置] 性能模式自动保存失败:', error);
        }
        
        // 数据保留数据收集
        collectRetentionData() {
            return {
                preset: this.currentRetentionMode
            };
        }
        
        // 数据保留数据验证
        validateRetentionData(data) {
            const validPresets = ['minimal', 'standard', 'extended'];
            return {
                valid: validPresets.includes(data.preset),
                message: validPresets.includes(data.preset) ? '' : '无效的数据保留预设'
            };
        }
        
        // 数据保留保存成功回调
        onRetentionSaveSuccess(result) {
            console.log('[性能设置] 数据保留自动保存成功:', result);
            // 如果返回了新配置，更新显示
            if (result.data && result.data.config) {
                this.retentionPresets[this.currentRetentionMode] = result.data.config;
                this.updateRetentionDisplay();
            }
        }
        
        // 数据保留保存错误回调
        onRetentionSaveError(error) {
            console.error('[性能设置] 数据保留自动保存失败:', error);
        }
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new PerformanceRetentionManager();
        });
    } else {
        new PerformanceRetentionManager();
    }
})();
</script>