{% set title = "编辑服务器" %}
{%set admin = true%}
{% extends "../../base.html" %}

{% block content %}
<!-- 页面容器 - 修复布局结构 -->
<div class="flex flex-col md:flex-row gap-6" style="padding-top: calc(1.5rem + env(safe-area-inset-top));">
    <!-- 引入侧边栏 -->
    {% include "../sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局 -->
    <div class="flex-1 min-w-0 space-y-6">
        
        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card mb-6">
            <div class="flex items-center gap-3 p-4">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                    <i class="ti ti-edit text-base text-blue-600 dark:text-blue-400"></i>
                </div>
                <div>
                    <h1 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">{{server.name}}</h1>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">编辑服务器配置</p>
                </div>
            </div>
        </div>

        <!-- 隐藏字段：服务器ID和置顶状态 -->
        <input type="text" id='sid' value="{{server.sid}}" hidden>
        <input type="text" id='edit_top' value="{{server.top}}" hidden>

        <!-- 表单内容区域 -->

        <!-- 📋 基本信息卡片 -->
        <div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-info-circle text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">基本信息</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">编辑服务器的基本属性和监控设置</p>
                    </div>
                </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">服务器名称</label>
                        <input type="text"
                               id="edit_name"
                               value="{{server.name}}"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">节点权限</label>
                        <select id="edit_status"
                                    class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                            {% for sta in ['1', '2', '0'] %}
                            {% set names = {'1':'公开','2':'私有','0':'停用'} %}
                            <option value="{{sta}}" {% if sta==server.status %}selected{% endif %}>{{names[sta]}}</option>
                            {% endfor %}
                        </select>
                            <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
                                <span class="font-medium">公开</span>：服务器正常监控且对外展示<br>
                                <span class="font-medium">私有</span>：服务器正常监控但仅管理员可见<br>
                                <span class="font-medium">停用</span>：停止监控数据收集，不参与监控
                            </p>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">到期时间</label>
                        <div class="flex items-center gap-1">
                            <input type="date"
                                   id="edit_expire_time"
                                   value="{{server.expire_time|formatDate}}"
                                   onfocus="this.showPicker && this.showPicker()"
                                   onclick="this.showPicker && this.showPicker()"
                                   min="1970-01-01" max="2099-12-31"
                                   class="flex-[2] min-w-0 w-full px-3 py-2 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                            <div class="shrink-0 flex items-center gap-1 justify-end w-auto">
                                <button type="button" data-renew="1m" class="px-2 py-1.5 text-xs sm:text-xs font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/30 transition-all">+1月</button>
                                <button type="button" data-renew="3m" class="px-2 py-1.5 text-xs sm:text-xs font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/30 transition-all">+3月</button>
                                <button type="button" data-renew="1y" class="px-2 py-1.5 text-xs sm:text-xs font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/30 transition-all">+1年</button>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">留空表示永久有效</p>
                        <style>
                        /* 日期图标常显 + 亮暗色兼容 */
                        input[type="date"]::-webkit-calendar-picker-indicator { opacity: 1; cursor: pointer; }
                        .dark input[type="date"]::-webkit-calendar-picker-indicator { filter: invert(1) hue-rotate(180deg); }
                        </style>
                        <script>
                        (function(){
                            function setLoading(el, loading){
                                if(!el) return;
                                if(loading){
                                    el.dataset._origText = el.textContent;
                                    el.textContent = '处理中...';
                                    el.disabled = true;
                                    el.classList.add('opacity-60','cursor-not-allowed');
                                }else{
                                    if(el.dataset._origText){ el.textContent = el.dataset._origText; }
                                    el.disabled = false;
                                    el.classList.remove('opacity-60','cursor-not-allowed');
                                }
                            }
                            function showTip(msg, ok){
                                try{
                                    const el = document.createElement('div');
                                    el.textContent = msg;
                                    el.className = `fixed bottom-4 right-4 z-50 px-3 py-2 rounded-lg text-xs shadow-md ${ok? 'bg-green-600 text-white':'bg-red-600 text-white'}`;
                                    document.body.appendChild(el);
                                    setTimeout(()=>{ el.remove(); }, 2500);
                                }catch(_){ alert(msg); }
                            }
                            function renew(period, btn){
                                const sid = document.getElementById('sid')?.value;
                                if(!sid) return;
                                setLoading(btn, true);
                                fetch(`/api/admin/servers/${encodeURIComponent(sid)}/renew`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ period })
                                })
                                .then(r=>r.json())
                                .then(ret=>{
                                    if(ret && (ret.status === 1 || ret.code === 1) && ret.data){
                                        const d = document.getElementById('edit_expire_time');
                                        if (d && ret.data.expire_date) {
                                            d.value = ret.data.expire_date;
                                        }
                                        showTip('续期成功', true);
                                    } else {
                                        showTip((ret && (ret.msg || ret.message)) || '续期失败', false);
                                    }
                                })
                                .catch(e=>{
                                    showTip('续期失败: ' + e.message, false);
                                })
                                .finally(()=> setLoading(btn, false));
                            }
                            document.addEventListener('DOMContentLoaded', function(){
                                document.querySelectorAll('button[data-renew]')
                                    .forEach(btn => btn.addEventListener('click', function(){
                                        const p = this.getAttribute('data-renew');
                                        renew(p, this);
                                    }));
                                // 日期选择器由原生控制，点击/聚焦自动 showPicker（不再需要复杂事件）
                            });
                        })();
                        </script>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">服务器分组</label>
                        <select id="edit_group_id"
                                    class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                            {% for group in groups %}
                            <option value="{{group.id}}" {% if group.id == server.group_id %}selected{% endif %}>{{group.name}}</option>
                            {% endfor %}
                            {% if not groups or groups.length == 0 %}
                            <option value="default" {% if server.group_id == 'default' %}selected{% endif %}>默认分组</option>
                            {% endif %}
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🔐 SSH连接配置卡片 -->
        <div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-shield-check text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">SSH连接配置</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">编辑SSH连接参数和认证信息</p>
                    </div>
                </div>
                    
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">域名/IP</label>
                        <input type="text"
                               id="edit_ssh_host"
                               value="{{server.data.ssh.host}}"
                               placeholder="IPv4或IPv6地址，如: *********** 或 2001:db8::1"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                            <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">支持IPv4和IPv6地址，IPv6地址无需添加方括号</p>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">端口</label>
                        <input type="number"
                               id="edit_ssh_port"
                               value="{{server.data.ssh.port}}"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p id="edit_ssh_port_error" class="mt-1 text-xs text-red-600 dark:text-red-400 hidden"></p>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">用户名</label>
                        <input type="text"
                               id="edit_ssh_username"
                               value="{{server.data.ssh.username}}"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">密码 (可选)</label>
                        <div class="relative">
                            <input type="password"
                                   id="edit_ssh_password"
                                   value="{{server.data.ssh.password}}"
                                       class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                                <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                                    <i class="ti ti-eye text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"></i>
                                </button>
                        </div>
                    </div>

                        <!-- 私钥设置区域 - 跨列设计 -->
                        <div class="lg:col-span-3 md:col-span-2 col-span-1">
                            <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 p-4 rounded-xl border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm space-y-3">
                                <div class="flex items-center justify-between">
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 tracking-wide">私钥 (可选)</label>
                                    <button type="button" 
                                            onclick="togglePrivateKeyVisibility()"
                                            class="text-xs text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-colors">
                                        <i class="ti ti-eye-off text-sm align-middle" id="privateKeyToggleIcon"></i>
                                        <span id="privateKeyToggleText">显示私钥</span>
                                    </button>
                                </div>
                            <div class="relative">
                                <textarea id="edit_ssh_privateKey"
                                          rows="8"
                                          placeholder="-----BEGIN RSA PRIVATE KEY-----&#10;...&#10;-----END RSA PRIVATE KEY-----"
                                          class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm font-mono text-xs resize-vertical opacity-90">{{server.data.ssh.privateKey}}</textarea>
                            </div>

                                <!-- 私钥密码字段 -->
                            <div>
                                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">私钥密码 (如果有)</label>
                                <div class="relative">
                                    <input type="password"
                                           id="edit_ssh_passphrase"
                                           placeholder="如果私钥有密码保护，请在此输入"
                                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                                    <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer toggle-password">
                                        <i class="ti ti-eye text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-3">
                                    <p class="text-xs text-slate-500 dark:text-slate-400 mb-2">支持 RSA、ED25519 和 PKCS8 格式的私钥</p>
                                    <div class="flex items-center space-x-3">
                                    <input type="file"
                                           id="edit_ssh_privateKey_file"
                                           class="hidden"
                                           accept=".pem,.key,.ppk">
                                    <button type="button"
                                            onclick="document.getElementById('edit_ssh_pri_file').click()"
                                                class="inline-flex items-center gap-2 px-3 py-2 text-xs font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                                            <i class="ti ti-upload text-sm"></i>
                                            <span>上传私钥文件</span>
                                    </button>
                                    <button type="button"
                                            onclick="testSSHConnectionForEdit()"
                                                class="inline-flex items-center gap-2 px-3 py-2 text-xs font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-200 active:scale-95">
                                            <i class="ti ti-circle-check text-sm"></i>
                                            <span>测试连接</span>
                                    </button>
                                </div>
                                <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                    将使用已保存的密码进行连接测试（如密码显示为 ••••••••）
                                </p>
                                <!-- 连接结果 -->
                                <div id="ssh-test-result" class="mt-2 text-sm hidden"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ⚙️ 高级设置卡片 -->
        <div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-adjustments-horizontal text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">高级设置</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">配置流量监控、API通讯和其他高级选项</p>
                    </div>
                </div>

                <!-- 流量设置区域 -->
                <div class="space-y-4">
                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                        <i class="ti ti-chart-line text-sm text-orange-600 dark:text-orange-400"></i>
                        流量监控设置
                    </h4>
                    
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">每月流量限制 (GB)</label>
                        <input type="number"
                               id="edit_traffic_limit"
                               value="{{server.traffic_limit|bytesToGB}}"
                                   placeholder="0"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量重置日</label>
                        <input type="number"
                               id="edit_traffic_reset_day"
                               value="{{server.traffic_reset_day|default(1)}}"
                               min="1"
                               max="31"
                                   placeholder="1"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量预警阈值 (%)</label>
                        <input type="number"
                               id="edit_traffic_alert_percent"
                               value="{{server.traffic_alert_percent|default(80)}}"
                               min="0"
                               max="100"
                                   placeholder="80"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量统计方向</label>
                        <select id="edit_traffic_direction"
                                    class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 cursor-pointer backdrop-blur-sm shadow-sm">
                            <option value="both" {% if server.traffic_direction == 'both' or not server.traffic_direction %}selected{% endif %}>双向流量 (入站+出站)</option>
                            <option value="in" {% if server.traffic_direction == 'in' %}selected{% endif %}>仅入站流量</option>
                            <option value="out" {% if server.traffic_direction == 'out' %}selected{% endif %}>仅出站流量</option>
                            <option value="max" {% if server.traffic_direction == 'max' %}selected{% endif %}>单向最大 (取较大值)</option>
                        </select>
                            <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">设置月度流量的统计方式</p>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">流量校准日期</label>
                        <input type="date"
                               id="edit_traffic_calibration_date"
                               value="{{server.traffic_calibration_date|formatDate}}"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">校准时已用流量 (GB)</label>
                        <input type="number"
                               id="edit_traffic_calibration_value"
                               value="{{server.traffic_calibration_value|bytesToGB}}"
                               step="0.01"
                                   placeholder="0"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                </div>

                <!-- API设置区域 -->
                <div class="space-y-4 mt-6">
                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4 flex items-center gap-2">
                        <i class="ti ti-settings text-sm text-cyan-600 dark:text-cyan-400"></i>
                        API通讯设置
                    </h4>
                    
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">通讯密钥</label>
                        <input type="text"
                               id="edit_api_key"
                               value="{{server.data.api.key}}"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm font-mono text-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">API端口</label>
                        <input type="number"
                               id="edit_api_port"
                               value="{{server.data.api.port}}"
                               min="1"
                               max="65535"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p id="edit_api_port_error" class="mt-1 text-xs text-red-600 dark:text-red-400 hidden"></p>
                        <p class="mt-1 text-xs text-amber-600 dark:text-amber-400 flex items-start gap-1">
                            <i class="ti ti-info-circle text-xs mt-0.5"></i>
                            <span>探针端口，需开放防火墙</span>
                        </p>
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">网络设备</label>
                        <input type="text"
                               id="edit_device"
                               value="{{server.data.device}}"
                                   placeholder="例如: eth0"
                                   class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                    </div>
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">通讯模式</label>
                        <div class="flex items-center gap-3">
                            <div class="relative inline-block">
                                <input type="checkbox"
                                       id="edit_api_mode"
                                       class="sr-only peer"
                                       {% if server.data.api.mode %}checked{% endif %}>
                                <div onclick="toggleApiMode('edit')"
                                     class="w-11 h-6 bg-slate-300 dark:bg-slate-600 rounded-full cursor-pointer relative transition-colors duration-200">
                                    <div id="edit-api-mode-toggle"
                                         class="w-5 h-5 rounded-full bg-white absolute left-0.5 top-0.5 transition-transform duration-200 shadow-sm">
                                    </div>
                                </div>
                            </div>
                                <span id="edit-api-mode-text" class="text-sm text-slate-600 dark:text-slate-300">
                                {% if server.data.api.mode %}主动模式{% else %}被动模式{% endif %}
                            </span>
                        </div>
                            <p class="mt-1 text-xs text-slate-500 dark:text-slate-400">主动模式：客户端主动上报数据；被动模式：服务器主动获取数据</p>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div> <!-- 关闭高级设置 admin-card -->

        <!-- 📍 位置设置卡片 -->
        <div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-map-pin text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">位置设置</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">配置服务器的地理位置信息</p>
                    </div>
                </div>
                    
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">手动设置国家</label>
                        <div class="flex items-center gap-3">
                            <div class="relative inline-block">
                                <input type="checkbox"
                                       id="edit_location_manual"
                                       class="sr-only peer"
                                       {% if server.data.location.manual %}checked{% endif %}>
                                <div onclick="toggleLocationManual()"
                                     class="w-11 h-6 bg-slate-300 dark:bg-slate-600 rounded-full cursor-pointer relative transition-colors duration-200">
                                    <div id="location-manual-toggle"
                                         class="w-5 h-5 rounded-full bg-white absolute left-0.5 top-0.5 transition-transform duration-200 shadow-sm">
                                    </div>
                                </div>
                            </div>
                                <span class="text-sm text-slate-600 dark:text-slate-300">
                                    启用手动设置
                                    {% if server.data.location.code %}
                                        <span class="text-xs text-slate-500 dark:text-slate-400"
                                              data-current-location="true"
                                              data-location-code="{{ server.data.location.code }}"
                                              data-location-name="{{ server.data.location.name_zh|default(server.data.location.code) }}">
                                            (当前: {% if server.data.location.name_zh %}{{ server.data.location.name_zh }}{% else %}{{ server.data.location.code }}{% endif %})
                                        </span>
                                    {% endif %}
                                </span>
                        </div>
                    </div>

                    <!-- 手动获取位置信息按钮 -->
                    <div>
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">获取位置信息</label>
                        <div id="fetch-location-container" class="{% if server.data.location.manual %}hidden{% endif %}">
                            <button type="button"
                                  id="fetch-location-button"
                                  onclick="fetchLocationInfo()"
                                      class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-200 active:scale-95">
                                    <i class="ti ti-map-pin text-sm"></i>
                                <span>手动获取位置</span>
                            </button>
                                <div id="location-status" class="mt-2 text-sm text-slate-500 dark:text-slate-400 hidden"></div>
                        </div>
                    </div>

                    <div id="country-select-container" class="{% if not server.data.location.manual %}hidden{% endif %}">
                            <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 tracking-wide">国家/地区</label>

                        <!-- 隐藏的选择器，用于存储选中的国家代码 -->
                        <input type="hidden" id="edit_location_country_code" value="{% if server.data.location.code %}{{ server.data.location.code }}{% else %}OT{% endif %}">

                        <!-- 搜索输入框 -->
                        <div class="relative mb-2">
                            <input type="text"
                                   id="country-search"
                                   placeholder="输入国家代码或名称搜索..."
                                       class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm pr-12">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <i class="ti ti-search text-slate-500 dark:text-slate-400"></i>
                            </div>
                        </div>

                        <!-- 当前选中的国家显示 -->
                            <div id="selected-country" class="flex items-center gap-2 p-3 bg-white/80 dark:bg-slate-800/60 rounded-xl border border-slate-200/60 dark:border-slate-700/40 mb-2">
                                <div id="selected-country-flag" class="w-8 h-6 flex-shrink-0 overflow-hidden rounded-sm border border-slate-200 dark:border-slate-700"></div>
                                <div id="selected-country-name" class="text-slate-800 dark:text-slate-200 flex-grow"></div>
                                <div id="selected-country-code" class="text-slate-500 dark:text-slate-400 text-sm"></div>
                        </div>

                        <!-- 国家列表容器 -->
                            <div id="country-list-container" class="max-h-60 overflow-y-auto bg-white/80 dark:bg-slate-800/60 border border-slate-200/60 dark:border-slate-700/40 rounded-xl hidden">
                            <div id="country-list" class="grid grid-cols-2 gap-1 p-2">
                                <!-- 国家列表将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <!-- 特殊选项 -->
                        <div class="flex flex-wrap gap-2 mt-2">
                                <div class="country-option cursor-pointer flex items-center gap-2 p-2 bg-white/80 dark:bg-slate-800/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/80 border border-slate-200/60 dark:border-slate-700/40 transition-colors" data-code="LO">
                                    <i class="ti ti-home text-slate-600 dark:text-slate-300"></i>
                                    <span class="text-slate-600 dark:text-slate-300">本地网络</span>
                                    <span class="text-slate-500 dark:text-slate-400 text-sm">LO</span>
                            </div>
                                <div class="country-option cursor-pointer flex items-center gap-2 p-2 bg-white/80 dark:bg-slate-800/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/80 border border-slate-200/60 dark:border-slate-700/40 transition-colors" data-code="OT">
                                    <i class="ti ti-world text-slate-600 dark:text-slate-300"></i>
                                    <span class="text-slate-600 dark:text-slate-300">其他地区</span>
                                    <span class="text-slate-500 dark:text-slate-400 text-sm">OT</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 🏷️ 标签管理卡片（已移除：仅在卡片页面管理标签） -->
        <!-- 已移除：统一在卡片页面管理标签 -->
        <!--div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-tags text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">自定义标签</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">管理服务器的自定义标签</p>
                    </div>
                </div>-->
                    
                    <!-- 标签列表容器 
                    <div id="tags-container" class="flex flex-wrap gap-2 cursor-pointer mb-3" title="点击管理标签">
                    </div>
                    
                    <button type="button" 
                            id="add-tag-btn" 
                            onclick="showAddTagModal()"
                            class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-pink-500/50 transition-all duration-200 active:scale-95">
                        <i class="ti ti-plus text-sm"></i>
                        <span>添加标签</span>
                    </button>
                    
                    <div class="mt-4">
                        <div class="flex items-center justify-between mb-2">
                            <p class="text-xs text-slate-500 dark:text-slate-400">常用标签（Top N）</p>
                            <span id="popular-inline-hint" class="text-[11px] text-slate-400"></span>
                        </div>
                        <div id="popular-tags-inline" class="flex flex-wrap gap-2"></div>
                    </div>
            </div>
        </div -->

        <!-- 🔧 探针管理卡片 -->
        <div class="admin-card">
            <div class="p-6">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl shadow-lg flex items-center justify-center">
                        <i class="ti ti-settings text-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">探针管理</h3>
                        <p class="text-sm text-slate-600 dark:text-slate-400">管理服务器监控探针的安装和配置</p>
                    </div>
                </div>
                    
                <div class="flex items-center gap-3">
                    <button onclick="init()"
                                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 transition-all duration-200 active:scale-95">
                            <i class="ti ti-download text-sm"></i>
                        <span>安装/更新探针</span>
                    </button>
                        <button onclick="uninstall()"
                                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600/80 hover:text-red-600 dark:hover:text-red-400 focus:outline-none focus:ring-2 focus:ring-red-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                            <i class="ti ti-trash text-sm"></i>
                            <span>卸载探针</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="admin-card" id="action-bar">
            <div class="p-6">
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                    <a href="/admin/servers"
                       class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95">
                        <i class="ti ti-arrow-left text-sm"></i>
                        <span>返回列表</span>
                    </a>
                    
                    <div class="flex items-center gap-3">
                        <button type="button"
                                onclick="testSSHConnectionForEdit()"
                                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-white/80 dark:bg-slate-700/60 text-slate-700 dark:text-slate-300 border border-slate-300/60 dark:border-slate-600/60 rounded-xl hover:bg-slate-50 dark:hover:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-200 backdrop-blur-sm active:scale-95"
                                title="使用已保存的密码进行连接测试">
                            <i class="ti ti-circle-check text-sm"></i>
                            <span>测试连接</span>
                        </button>

                        <button onclick="del()"
                                class="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500/50 transition-all duration-200 active:scale-95">
                            <i class="ti ti-trash text-sm"></i>
                            <span>删除服务器</span>
                        </button>
                        
                        <button onclick="edit()"
                                class="inline-flex items-center gap-2 px-6 py-2 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                            <i class="ti ti-device-floppy text-sm"></i>
                            <span>保存修改</span>
                        </button>
                </div>
                </div>
            </div>
        </div>

        <!-- 添加标签模态框 -->
<div id="add-tag-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200">添加标签</h3>
            <button type="button" onclick="closeAddTagModal()" class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-200">
                <i class="ti ti-x"></i>
            </button>
        </div>
        
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">标签名称</label>
                <input type="text" 
                       id="tag-name-input" 
                       placeholder="输入标签名称"
                       maxlength="20"
                       class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-pink-500/50">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">标签颜色</label>
                <div class="flex items-center gap-2">
                    <input type="color" 
                           id="tag-color-input" 
                           value="#6b7280"
                           class="w-12 h-8 border border-slate-300 dark:border-slate-600 rounded cursor-pointer">
                    <span id="tag-color-display" class="text-sm text-slate-600 dark:text-slate-400">#6b7280</span>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">标签图标</label>
                <div class="grid grid-cols-6 gap-2" id="icon-selector">
                    <!-- 图标选择器将通过JavaScript填充 -->
                </div>
                <input type="hidden" id="selected-icon" value="tag">
            </div>
            
            <div class="flex justify-end gap-3 mt-6">
                <button type="button" 
                        onclick="closeAddTagModal()"
                        class="px-4 py-2 text-sm font-medium text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-700 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors">
                    取消
                </button>
                <button type="button" 
                        onclick="addTag()"
                        class="px-4 py-2 text-sm font-medium text-white bg-pink-500 hover:bg-pink-600 rounded-lg transition-colors">
                    添加标签
                </button>
            </div>
        </div>
    </div> <!-- 关闭模态框内容容器 -->
</div> <!-- 关闭模态框背景 -->

<!-- 包含共享的安装进度模态框 -->
{% include "./partials/install-progress-modal.html" %}

    </div> <!-- 关闭主内容区域 flex-1 min-w-0 -->
</div> <!-- 关闭最外层 flex 容器 -->

{% endblock %}

{%block js%}
<!-- 引入核心工具函数 -->
<script src="/js/core.js"></script>

<!-- 引入服务器编辑相关的JavaScript模块 -->
<script src="/js/admin/servers/traffic-format.js"></script>
<script src="/js/admin/servers/form-utils.js"></script>
<script src="/js/admin/servers/location-common.js"></script>
<script src="/js/admin/servers/server-operations.js"></script>
<script src="/js/admin/servers/ssh-test-common.js"></script>
<!-- 统一标签UI组件 -->
<script src="/js/card/tag-ui.js"></script>
<script src="/js/card/quick-tag-editor.js"></script>
<script src="/js/admin/servers/edit.js"></script>
<script src="/js/admin/servers/probe-manager.js"></script>
<script src="/js/admin/servers/file-upload.js"></script>
<script src="/js/admin/servers/api-mode-toggle.js"></script>
<script src="/js/admin/servers/floating-action-bar.js"></script>
<script src="/js/admin/servers/password-toggle.js"></script>
<script src="/js/admin/servers/keyboard-shortcuts.js"></script>
<script src="/js/admin/servers/install-progress.js"></script>


<!-- 服务器数据传递给JavaScript - 移到脚本加载前 -->
<script type="application/json" id="server-data">
{{server|tojson}}
</script>
<script>
// 安全解析服务器数据 - 立即执行
let server;
try {
    const jsonText = document.getElementById('server-data')?.textContent || '{}';
    server = JSON.parse(jsonText);
    console.log('服务器数据解析成功:', server);
    console.log('标签数据:', server.data?.tags);
} catch (error) {
    console.error('服务器数据解析失败:', error);
    // 回退为空对象，避免阻塞其他逻辑
    server = {};
}

// 统一标签管理初始化：载入 Chip 并绑定弹窗打开
document.addEventListener('DOMContentLoaded', function(){});
</script>

<!-- SSH 私钥格式化工具 -->
<script src="/js/admin/servers/ssh-key-formatter.js"></script>

<!-- 初始化私钥格式化 -->
<script>
// 私钥显示/隐藏切换
function togglePrivateKeyVisibility() {
    const textarea = document.getElementById('edit_ssh_privateKey');
    const icon = document.getElementById('privateKeyToggleIcon');
    const text = document.getElementById('privateKeyToggleText');
    
    if (textarea.style.filter === 'blur(5px)') {
        // 显示私钥
        textarea.style.filter = 'none';
        icon.className = 'ti ti-eye-off text-sm align-middle';
        text.textContent = '隐藏私钥';
    } else {
        // 隐藏私钥
        textarea.style.filter = 'blur(5px)';
        icon.className = 'ti ti-eye text-sm align-middle';
        text.textContent = '显示私钥';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 绑定私钥输入框
    const privateKeyTextarea = document.getElementById('edit_ssh_privateKey');
    if (privateKeyTextarea) {
        SSHKeyFormatter.bindToTextarea(privateKeyTextarea);
        // 默认隐藏私钥内容
        privateKeyTextarea.style.filter = 'blur(5px)';
    }
    
    // 绑定端口验证（使用form-utils.js中的validatePortInput函数）
    validatePortInput('edit_ssh_port', 'edit_ssh_port_error');
    validatePortInput('edit_api_port', 'edit_api_port_error');
});
</script>
{% endblock %}
