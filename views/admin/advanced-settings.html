{% set title = "高级设置" %}
{%set admin = true%}
{% extends "../base.html" %}

{%block head%}
<!-- PWA支持 -->
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

<!-- 主题样式由 theme-manager.js 动态管理 -->

<!-- 页面特定样式 -->
<style>
    /* 确保SVG图标不受强调色影响 */
    .tabler-icon {
        color: inherit !important;
        width: 1em;
        height: 1em;
        stroke: currentColor;
        stroke-width: 2;
        stroke-linecap: round;
        stroke-linejoin: round;
        fill: none;
    }
    
    /* 确保SVG图标在不同状态下正确显示 */
    .tabler-icon:hover {
        color: inherit !important;
    }
    
    /* 旋转动画支持 */
    .tabler-icon.animate-spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>

<!-- 背景加载脚本 -->
<script>
// 初始化背景设置
document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;

    // 从localStorage获取背景设置
    try {
        // 尝试从localStorage中获取本地保存的背景设置
        const personalizationSettings = localStorage.getItem('personalization_settings');
        if (personalizationSettings) {
            const settings = JSON.parse(personalizationSettings);
            const background = settings.background;

            // 如果有背景图片
            if (background && background.type === 'image' && background.url) {
                body.classList.add('has-background-image');
                const darkenAmount = background.darken_amount || 60;
                body.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, ${darkenAmount / 100}), rgba(0, 0, 0, ${darkenAmount / 100})), url("${background.url}")`;
            }
            // 如果是服务器保存的数据
            else {
                // 尝试从服务器设置中获取
                fetch('/api/personalization-settings')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 1 && data.settings && data.settings.background) {
                            const background = data.settings.background;
                            if (background.type === 'image' && background.url) {
                                body.classList.add('has-background-image');
                                const darkenAmount = background.darken_amount || 60;
                                body.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, ${darkenAmount / 100}), rgba(0, 0, 0, ${darkenAmount / 100})), url("${background.url}")`;
                            }
                        }
                    })
                    .catch(error => console.error('获取设置失败:', error));
            }
        }
    } catch (error) {
        console.error('加载背景设置错误:', error);
    }
});
</script>
{%endblock%}

{%block content%}
<!-- 页面容器 - 简化布局，去掉复杂容器样式 -->
<div class="flex flex-col md:flex-row gap-6 justify-center" style="padding-top: calc(1.5rem + env(safe-area-inset-top));">
    <!-- 引入侧边栏 -->
    {% include "admin/sidebar.html" %}

    <!-- 主内容区域 - 独立卡片布局 -->
    <div class="flex-1 max-w-auto space-y-6">

        <!-- 页面标题卡片 - 独立设计 -->
        <div class="admin-card">
            <div class="flex items-center gap-3 p-4">
                <div class="w-9 h-9 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/30 rounded-lg shadow-sm flex-shrink-0 border border-purple-200/50 dark:border-purple-700/30 flex items-center justify-center">
                    <svg class="tabler-icon text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24">
                        <path d="M14 6a2 2 0 1 0 -4 0 2 2 0 0 0 4 0z"/>
                        <path d="M4 6h8m4 0h4"/>
                        <path d="M16 18a2 2 0 1 0 4 0 2 2 0 0 0 -4 0z"/>
                        <path d="M4 18h12m4 0h0"/>
                        <path d="M6 12a2 2 0 1 0 4 0 2 2 0 0 0 -4 0z"/>
                        <path d="M4 12h2m4 0h10"/>
                    </svg>
                </div>
                <div>
                    <h1 class="text-lg font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">高级设置</h1>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">Advanced Settings</p>
                </div>
            </div>
        </div>

        <!-- 性能优化与数据保留设置卡片 -->
        {% include "components/performance-retention-settings.html" %}
        
        <!-- 详细性能配置卡片（已整合到上方组件，注释保留备用） -->
        <!-- <div class="admin-card">
            <!-- 卡片头部 - iPhone风格 -->
            <div class="flex items-center gap-3 p-4 pb-3 border-b border-slate-200/60 dark:border-slate-700/40">
                <div class="w-9 h-9 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/40 dark:to-amber-800/30 rounded-lg shadow-sm flex-shrink-0 border border-amber-200/50 dark:border-amber-700/30 flex items-center justify-center">
                    <svg class="tabler-icon text-amber-600 dark:text-amber-400" fill="none" viewBox="0 0 24 24">
                        <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/>
                        <path d="M12 12l3.5 -3.5"/>
                        <path d="M12 7v5"/>
                        <path d="M9 12h6"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">详细性能配置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">高级用户自定义轮询间隔和更新频率</p>
                </div>
            </div>

            <!-- 卡片内容 - iPhone风格表单 -->
            <div class="p-4 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">监控轮询间隔（秒）</label>
                        <input type="number"
                               value="{{(setting.polling_interval | default(3000)) / 1000}}"
                               key="polling_interval_seconds"
                               min="1.5"
                               max="60"
                               step="0.5"
                               onchange="updatePollingMillis(this)"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">控制服务器状态检查频率（1.5-60秒）</p>
                        <input type="hidden" key="polling_interval" id="polling_interval_hidden" value="{{setting.polling_interval | default(3000)}}">
                    </div>
                    <div class="space-y-3">
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 tracking-wide">WebSocket更新间隔（秒）</label>
                        <input type="number"
                               value="{{(setting.websocket_interval | default(4000)) / 1000}}"
                               key="websocket_interval_seconds"
                               min="1.5"
                               max="60"
                               step="0.5"
                               onchange="updateWebSocketMillis(this)"
                               class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/60 transition-all duration-200 hover:border-slate-300/80 dark:hover:border-slate-600/60 backdrop-blur-sm shadow-sm">
                        <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">控制实时数据推送频率（建议比轮询间隔略大）</p>
                        <input type="hidden" key="websocket_interval" id="websocket_interval_hidden" value="{{setting.websocket_interval | default(4000)}}">
                    </div>
                </div>

                <!-- 重要信息说明区域 -->
                <div class="bg-gradient-to-r from-slate-50/80 to-white/60 dark:from-slate-800/40 dark:to-slate-800/20 rounded-xl p-4 border border-slate-200/60 dark:border-slate-700/40 backdrop-blur-sm">
                    <!-- 性能影响说明 -->
                    <div class="bg-amber-50/60 dark:bg-amber-900/20 rounded-lg p-3 border border-amber-200/40 dark:border-amber-800/30 mb-3">
                        <h6 class="text-xs font-semibold text-amber-700 dark:text-amber-300 mb-2 flex items-center gap-1">
                            <svg class="tabler-icon text-amber-600" fill="none" viewBox="0 0 24 24" style="width: 0.75rem; height: 0.75rem;">
                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                                <path d="M12 9h.01"/>
                                <path d="M11 12h1v4h1"/>
                            </svg>
                            性能影响说明
                        </h6>
                        <div class="text-xs text-amber-600 dark:text-amber-400 space-y-1">
                            <p>• 轮询间隔越短，服务器负载越高，但数据更新越及时</p>
                            <p>• WebSocket间隔控制浏览器接收更新的频率</p>
                            <p>• 建议WebSocket间隔略大于轮询间隔以避免数据积压</p>
                            <p>• 节点数量越多，建议使用更长的间隔以降低负载</p>
                        </div>
                    </div>

                    <!-- 推荐配置指南 -->
                    <div class="bg-blue-50/60 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200/40 dark:border-blue-800/30">
                        <h6 class="text-xs font-semibold text-blue-700 dark:text-blue-300 mb-2 flex items-center gap-1">
                            <svg class="tabler-icon text-blue-600" fill="none" viewBox="0 0 24 24" style="width: 0.75rem; height: 0.75rem;">
                                <path d="M7 13l3 3l7 -7"/>
                            </svg>
                            推荐配置指南
                        </h6>
                        <div class="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                            <p>• <50个节点：性能模式（1.5秒），确保快速响应</p>
                            <p>• 50-100个节点：平衡模式（3秒），兼顾性能</p>
                            <p>• 100-200个节点：节能模式（10秒），降低负载</p>
                            <p>• >200个节点：自定义更长间隔（15-30秒）</p>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->


        <!-- PostgreSQL数据库配置卡片 -->
        <div class="admin-card">
            <!-- 卡片头部 -->
            <div class="flex items-center gap-3 p-4 pb-3 border-b border-slate-200/60 dark:border-slate-700/40">
                <div class="w-9 h-9 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/30 rounded-lg shadow-sm flex-shrink-0 border border-blue-200/50 dark:border-blue-700/30 flex items-center justify-center">
                    <svg class="tabler-icon text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24">
                        <ellipse cx="12" cy="6" rx="8" ry="3"></ellipse>
                        <path d="M4 6v6a8 3 0 0 0 16 0v-6"></path>
                        <path d="M4 12v6a8 3 0 0 0 16 0v-6"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">PostgreSQL 数据库配置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">启用PostgreSQL读取优化，提升大数据量查询性能</p>
                </div>
            </div>

            <!-- 卡片内容 -->
            <div class="p-4 space-y-4">
                <!-- PostgreSQL开关 -->
                <div class="flex items-center justify-between p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border border-slate-200/40 dark:border-slate-600/30">
                    <div class="flex items-center gap-3">
                        <div class="w-9 h-9 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                            <svg class="tabler-icon text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" style="width: 1.25rem; height: 1.25rem;">
                                <path d="M2 12a5 5 0 0 0 5 5h10a5 5 0 0 0 0 -10h-10a5 5 0 0 0 -5 5z"/>
                                <circle cx="17" cy="12" r="3"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-800 dark:text-slate-200">启用PostgreSQL</h3>
                            <p class="text-xs text-slate-500 dark:text-slate-400">使用PostgreSQL作为读取数据库，提升查询性能</p>
                        </div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="postgres-enabled" class="sr-only peer" {% if setting.postgresql_config and setting.postgresql_config.enabled %}checked{% endif %}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                </div>

                <!-- PostgreSQL配置表单 -->
                <div id="postgres-settings" class="space-y-4 {% if not (setting.postgresql_config and setting.postgresql_config.enabled) %}hidden{% endif %}">
                    
                    <!-- 连接字符串快速填入 -->
                    <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/30">
                        <div class="flex items-start gap-3">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                                    快速配置（可选）
                                    <span class="text-xs font-normal text-blue-600 dark:text-blue-300 ml-2">
                                        从连接字符串自动填入表单
                                    </span>
                                </label>
                                <input type="text" id="connection-string-input" 
                                       placeholder="postgresql://username:password@host:port/database" 
                                       class="w-full px-3 py-2 bg-white/80 dark:bg-slate-800/60 border border-blue-300/50 dark:border-blue-600/50 rounded-lg text-sm font-mono placeholder-blue-400 dark:placeholder-blue-500">
                            </div>
                            <button type="button" onclick="parseAndFillConnectionString()" 
                                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors duration-200">
                                解析填入
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300">主机地址</label>
                            <div class="relative">
                                <input type="text" id="postgres-host" 
                                       data-real-value="{{setting.postgresql_config.host if setting.postgresql_config else ''}}"
                                       value="{{setting.postgresql_config.host if setting.postgresql_config else ''}}" 
                                       placeholder="localhost" 
                                       class="w-full px-4 py-3 pr-12 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                                <button type="button" onclick="toggleHostVisibility()" 
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300 focus:outline-none transition-colors duration-200"
                                        title="显示/隐藏完整地址">
                                    <svg id="host-toggle-icon" class="tabler-icon" fill="none" viewBox="0 0 24 24">
                                        <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/>
                                        <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/>
                                        <path d="M3 3l18 18"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300">端口</label>
                            <input type="number" id="postgres-port" value="{{setting.postgresql_config.port if setting.postgresql_config else '5432'}}" placeholder="5432" class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                        </div>
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300">数据库名</label>
                            <input type="text" id="postgres-database" value="{{setting.postgresql_config.database if setting.postgresql_config else ''}}" placeholder="dstatus" class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                        </div>
                        <div class="space-y-2">
                            <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300">用户名</label>
                            <input type="text" id="postgres-username" value="{{setting.postgresql_config.username if setting.postgresql_config else ''}}" placeholder="dstatus" class="w-full px-4 py-3 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                        </div>
                        <div class="space-y-2 md:col-span-2">
                            <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300">密码</label>
                            <div class="relative">
                                <input type="password" id="postgres-password" value="{{setting.postgresql_config.password if setting.postgresql_config else ''}}" placeholder="输入密码" class="w-full px-4 py-3 pr-12 bg-white/80 dark:bg-slate-800/60 border-2 border-slate-200/80 dark:border-slate-700/60 rounded-xl text-slate-800 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/60 transition-all duration-200">
                                <button type="button" onclick="togglePasswordVisibility()" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300 focus:outline-none transition-colors duration-200">
                                    <svg id="password-toggle-icon" class="tabler-icon" fill="none" viewBox="0 0 24 24">
                                        <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/>
                                        <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/>
                                        <path d="M3 3l18 18"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- PostgreSQL操作按钮组（移到配置表单外部，确保保存按钮始终可见） -->
                <div id="postgres-buttons" class="flex gap-3 justify-end mt-4">
                    <button id="test-connection-btn" onclick="testPostgresConnection()" 
                            class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium bg-green-500 hover:bg-green-600 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-200 {% if not (setting.postgresql_config and setting.postgresql_config.enabled) %}hidden{% endif %}">
                        <svg class="tabler-icon" fill="none" viewBox="0 0 24 24">
                            <path d="M12 18h.01"/>
                            <path d="M9.172 15.172a4 4 0 0 1 5.656 0"/>
                            <path d="M6.343 12.343a8 8 0 0 1 11.314 0"/>
                            <path d="M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0"/>
                        </svg>
                        <span>测试连接</span>
                    </button>
                    <button onclick="savePostgresConfig()" 
                            class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium bg-blue-500 hover:bg-blue-600 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200">
                        <svg class="tabler-icon" fill="none" viewBox="0 0 24 24">
                            <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                            <circle cx="12" cy="14" r="2"/>
                            <polyline points="14,4 14,8 8,8 8,4"/>
                        </svg>
                        <span>保存配置</span>
                    </button>
                    <button id="migration-btn" onclick="startDataMigration()" 
                            class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium bg-purple-500 hover:bg-purple-600 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-200 {% if not (setting.postgresql_config and setting.postgresql_config.enabled) %}hidden{% endif %}">
                        <svg class="tabler-icon" fill="none" viewBox="0 0 24 24">
                            <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                            <polyline points="9 15 12 12 15 15"/>
                        </svg>
                        <span>开始迁移</span>
                    </button>
                </div>

                <!-- 当前状态显示 -->
                <div id="postgres-status" class="bg-slate-50/60 dark:bg-slate-800/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-700/40">
                    <h6 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center gap-2">
                        <svg class="tabler-icon text-slate-500" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                            <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                            <path d="M12 9h.01"/>
                            <path d="M11 12h1v4h1"/>
                        </svg>
                        当前状态
                    </h6>
                    <div id="postgres-status-content" class="text-sm text-slate-600 dark:text-slate-400">
                        加载中...
                    </div>
                </div>

                <!-- 迁移进度显示 -->
                <div id="migration-progress" class="hidden bg-blue-50/60 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200/60 dark:border-blue-700/40">
                    <h6 class="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center gap-2">
                        <svg id="migration-icon" class="tabler-icon text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                            <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                            <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                        </svg>
                        数据迁移进度
                    </h6>
                    
                    <div class="space-y-3">
                        <!-- 进度条 -->
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-blue-700 dark:text-blue-300">迁移进度</span>
                            <span id="migration-percentage" class="font-medium text-blue-600 dark:text-blue-400">0%</span>
                        </div>
                        
                        <div class="w-full bg-blue-200/60 dark:bg-blue-800/40 rounded-full h-2">
                            <div id="migration-bar" class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300 ease-out" style="width: 0%"></div>
                        </div>
                        
                        <!-- 状态信息 -->
                        <div class="flex items-start gap-2">
                            <svg class="tabler-icon text-blue-500 mt-0.5" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                                <path d="M12 9h.01"/>
                                <path d="M11 12h1v4h1"/>
                            </svg>
                            <div class="flex-1">
                                <div id="migration-status" class="text-sm text-blue-700 dark:text-blue-300">准备迁移...</div>
                                <div id="migration-details" class="text-xs text-blue-600 dark:text-blue-400 mt-1 opacity-75"></div>
                            </div>
                        </div>
                        
                        <!-- 迁移完成状态 -->
                        <div id="migration-complete" class="hidden items-center gap-2 p-2 bg-green-100/60 dark:bg-green-900/20 rounded border border-green-200/40 dark:border-green-700/40">
                            <svg class="tabler-icon text-green-600" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                                <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/>
                                <path d="M9 12l2 2l4 -4"/>
                            </svg>
                            <span class="text-sm font-medium text-green-700 dark:text-green-300">数据迁移完成！PostgreSQL已生效</span>
                        </div>
                        
                        <!-- 迁移失败状态 -->
                        <div id="migration-error" class="hidden items-start gap-2 p-2 bg-red-100/60 dark:bg-red-900/20 rounded border border-red-200/40 dark:border-red-700/40">
                            <svg class="tabler-icon text-red-600 mt-0.5" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                                <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/>
                                <path d="M12 9v4"/>
                                <path d="M12 16h.01"/>
                            </svg>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-red-700 dark:text-red-300">迁移失败</div>
                                <div id="migration-error-message" class="text-xs text-red-600 dark:text-red-400 mt-1"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 重要提示 -->
                <div class="bg-amber-50/60 dark:bg-amber-900/20 rounded-lg p-3 border border-amber-200/40 dark:border-amber-800/30">
                    <h6 class="text-xs font-semibold text-amber-700 dark:text-amber-300 mb-2 flex items-center gap-1">
                        <svg class="tabler-icon text-amber-600" fill="none" viewBox="0 0 24 24" style="width: 0.75rem; height: 0.75rem;">
                            <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"/>
                            <path d="M12 9h.01"/>
                            <path d="M11 12h1v4h1"/>
                        </svg>
                        重要说明
                    </h6>
                    <div class="text-xs text-amber-600 dark:text-amber-400 space-y-1">
                        <p>• PostgreSQL用于读取优化，所有写入仍使用SQLite</p>
                        <p>• 配置保存后需要重启应用才能生效</p>
                        <p>• PostgreSQL连接失败时会自动降级到SQLite</p>
                        <p>• 配置信息存储在SQLite中，重启安全</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据保留设置卡片（已整合到性能与数据管理组件，注释保留备用） -->
        <!-- <div class="admin-card">
            <!-- 卡片头部 -->
            <div class="flex items-center gap-3 p-4 pb-3 border-b border-slate-200/60 dark:border-slate-700/40">
                <div class="w-9 h-9 bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900/40 dark:to-emerald-800/30 rounded-lg shadow-sm flex-shrink-0 border border-emerald-200/50 dark:border-emerald-700/30 flex items-center justify-center">
                    <svg class="tabler-icon text-emerald-600 dark:text-emerald-400" fill="none" viewBox="0 0 24 24">
                        <path d="M3 12a9 9 0 1 0 9 -9a9.75 9.75 0 0 0 -6.74 2.74l-3.26 -3.26"/>
                        <path d="M3 3v6h6"/>
                        <path d="M12 7v5l4 2"/>
                    </svg>
                </div>
                <div>
                    <h2 class="text-base font-medium text-slate-800 dark:text-slate-200 tracking-wide leading-tight">历史数据保留设置</h2>
                    <p class="text-xs text-slate-500 dark:text-slate-400 leading-relaxed mt-0.5">选择合适的数据保留方案</p>
                </div>
            </div>

            <!-- 卡片内容 -->
            <div class="p-4 space-y-4">
                <!-- 数据保留方案选择 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- 精简模式 -->
                    <div id="retention-preset-minimal" class="retention-preset-card cursor-pointer" onclick="selectRetentionPreset('minimal')">
                        <div class="p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-slate-200/40 dark:border-slate-600/30 hover:border-green-300 dark:hover:border-green-600 transition-all duration-200">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-9 h-9 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="tabler-icon text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" style="width: 1.25rem; height: 1.25rem;">
                                        <path d="M12 10a6 6 0 0 0 -6 -6h-3v2a6 6 0 0 0 6 6h3"/>
                                        <path d="M12 14a6 6 0 0 1 6 -6h3v1a6 6 0 0 1 -6 6h-3"/>
                                        <path d="M12 11l0 .01"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-slate-800 dark:text-slate-200">精简模式</h3>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">适合存储空间有限的环境</p>
                                </div>
                            </div>
                            <div class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                                <div class="flex justify-between">
                                    <span>实时数据:</span>
                                    <span class="font-medium">1小时</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>分钟级数据:</span>
                                    <span class="font-medium">7天</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>小时级数据:</span>
                                    <span class="font-medium">30天</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 标准模式 -->
                    <div id="retention-preset-standard" class="retention-preset-card cursor-pointer" onclick="selectRetentionPreset('standard')">
                        <div class="p-4 rounded-lg border-2 border-blue-300 dark:border-blue-600 bg-blue-50/30 dark:bg-blue-900/20 transition-all duration-200">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-9 h-9 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="tabler-icon text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" style="width: 1.25rem; height: 1.25rem;">
                                        <path d="M7 13l3 3l7 -7"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                                        标准模式
                                        <span class="text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full">推荐</span>
                                    </h3>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">平衡性能与存储的最佳选择</p>
                                </div>
                            </div>
                            <div class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                                <div class="flex justify-between">
                                    <span>实时数据:</span>
                                    <span class="font-medium">3小时</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>分钟级数据:</span>
                                    <span class="font-medium">14天</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>小时级数据:</span>
                                    <span class="font-medium">90天</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 扩展模式 -->
                    <div id="retention-preset-extended" class="retention-preset-card cursor-pointer" onclick="selectRetentionPreset('extended')">
                        <div class="p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-slate-200/40 dark:border-slate-600/30 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-9 h-9 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                    <svg class="tabler-icon text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" style="width: 1.25rem; height: 1.25rem;">
                                        <polyline points="3,17 9,11 13,15 21,7"/>
                                        <polyline points="14,7 21,7 21,14"/>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-slate-800 dark:text-slate-200">扩展模式</h3>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">用于长期趋势分析和历史对比</p>
                                </div>
                            </div>
                            <div class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                                <div class="flex justify-between">
                                    <span>实时数据:</span>
                                    <span class="font-medium">6小时</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>分钟级数据:</span>
                                    <span class="font-medium">30天</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>小时级数据:</span>
                                    <span class="font-medium">180天</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 当前配置显示 -->
                <div id="current-retention-config" class="bg-slate-50/60 dark:bg-slate-800/40 rounded-lg p-4 border border-slate-200/60 dark:border-slate-700/40">
                    <h6 class="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center gap-2">
                        <svg class="tabler-icon text-slate-500" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                            <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        当前配置
                    </h6>
                    <div id="current-config-display" class="text-sm text-slate-600 dark:text-slate-400">
                        加载中...
                    </div>
                </div>



        <!-- 保存按钮 -->
        <div class="mt-6 flex justify-end">
            <button id="saveSettingsButton" onclick="saveAdvancedSettings()"
                    class="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 transition-all duration-200 transform hover:scale-105 active:scale-95">
                <svg class="tabler-icon" fill="none" viewBox="0 0 24 24">
                    <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/>
                    <circle cx="12" cy="14" r="2"/>
                    <polyline points="14,4 14,8 8,8 8,4"/>
                </svg>
                <span>保存设置</span>
            </button>
        </div>
    </div>
</div>
{%endblock%}

{%block js%}
<!-- 基础函数和工具 -->
<script src="/js/admin-buttons.js"></script>

<!-- 高级设置管理脚本 -->
<script>
// 轮询间隔配置函数
function updatePollingMillis(input) {
    const seconds = parseFloat(input.value) || 3;
    const millis = Math.round(seconds * 1000);
    document.getElementById('polling_interval_hidden').value = millis;
}

function updateWebSocketMillis(input) {
    const seconds = parseFloat(input.value) || 4;
    const millis = Math.round(seconds * 1000);
    document.getElementById('websocket_interval_hidden').value = millis;
}


// 数据保留设置相关函数
// 数据保留设置管理
let currentRetentionPreset = 'standard'; // 默认为标准模式

// 选择数据保留方案
async function selectRetentionPreset(presetName) {
    try {
        console.log('[数据保留] 选择预设方案:', presetName);
        
        // 发送更新请求
        const response = await fetch('/api/settings/data-retention', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ preset: presetName })
        });
        
        const result = await response.json();
        console.log('[数据保留] 服务器响应:', result);
        
        if (result.status === 1) {
            currentRetentionPreset = presetName;
            updateRetentionUI(presetName, result.data.config);
            
            // 获取节点数量并计算存储空间
            const nodeCount = await getNodeCount();
            const singleNodeStorage = {
                minimal: { total_mb: 0.9 },
                standard: { total_mb: 1.9 },
                extended: { total_mb: 4.1 }
            };
            
            const actualStorage = {
                total_mb: Math.round(singleNodeStorage[presetName].total_mb * nodeCount),
                details: `${nodeCount}个节点`
            };
            
            updateCurrentConfigDisplay(presetName, result.data.config, actualStorage, nodeCount);
            showToast(`已切换到${getPresetDisplayName(presetName)}`, 'success');

            console.log('[数据保留] 配置更新成功');
        } else {
            throw new Error(result.data.message || result.message || '保存失败');
        }
    } catch (error) {
        console.error('[数据保留] 保存失败:', error);
        showToast('保存失败: ' + error.message, 'error');
    }
}

// 更新UI显示
function updateRetentionUI(selectedPreset, config) {
    // 重置所有卡片样式
    document.querySelectorAll('.retention-preset-card').forEach(card => {
        const cardDiv = card.querySelector('div');
        cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-slate-200/40 dark:border-slate-600/30 hover:border-green-300 dark:hover:border-green-600 transition-all duration-200';
        if (card.id.includes('minimal')) {
            cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-slate-200/40 dark:border-slate-600/30 hover:border-green-300 dark:hover:border-green-600 transition-all duration-200';
        } else if (card.id.includes('extended')) {
            cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-slate-200/40 dark:border-slate-600/30 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200';
        }
    });
    
    // 高亮选中的方案
    const selectedCard = document.getElementById(`retention-preset-${selectedPreset}`);
    if (selectedCard) {
        const cardDiv = selectedCard.querySelector('div');
        if (selectedPreset === 'minimal') {
            cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-green-300 dark:border-green-600 bg-green-50/30 dark:bg-green-900/20 transition-all duration-200';
        } else if (selectedPreset === 'standard') {
            cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-blue-300 dark:border-blue-600 bg-blue-50/30 dark:bg-blue-900/20 transition-all duration-200';
        } else if (selectedPreset === 'extended') {
            cardDiv.className = 'p-4 bg-white/60 dark:bg-slate-700/30 rounded-lg border-2 border-purple-300 dark:border-purple-600 bg-purple-50/30 dark:bg-purple-900/20 transition-all duration-200';
        }
    }
    
    // 更新当前配置显示
    updateCurrentConfigDisplay(selectedPreset, config);
}

// 更新当前配置显示
function updateCurrentConfigDisplay(preset, config, actualStorage, nodeCount) {
    const displayDiv = document.getElementById('current-config-display');
    if (displayDiv && config) {
        let storageDisplay = '';
        if (actualStorage && nodeCount > 0) {
            storageDisplay = `
                <div class="text-center">
                    <div class="text-xs text-slate-500 dark:text-slate-400">实际存储空间</div>
                    <div class="font-semibold text-slate-700 dark:text-slate-300">${actualStorage.total_mb}MB</div>
                    <div class="text-xs text-slate-400 dark:text-slate-500">${nodeCount}个节点</div>
                </div>
            `;
        }
        
        displayDiv.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-${actualStorage ? '5' : '4'} gap-4">
                <div class="text-center">
                    <div class="text-xs text-slate-500 dark:text-slate-400">当前方案</div>
                    <div class="font-semibold text-slate-700 dark:text-slate-300">${getPresetDisplayName(preset)}</div>
                </div>
                <div class="text-center">
                    <div class="text-xs text-slate-500 dark:text-slate-400">实时数据</div>
                    <div class="font-semibold text-slate-700 dark:text-slate-300">${config.archive_hours}小时</div>
                </div>
                <div class="text-center">
                    <div class="text-xs text-slate-500 dark:text-slate-400">分钟级数据</div>
                    <div class="font-semibold text-slate-700 dark:text-slate-300">${config.minute_days}天</div>
                </div>
                <div class="text-center">
                    <div class="text-xs text-slate-500 dark:text-slate-400">小时级数据</div>
                    <div class="font-semibold text-slate-700 dark:text-slate-300">${config.hour_days}天</div>
                </div>
                ${storageDisplay}
            </div>
        `;
    }
}

// 获取预设方案显示名称
function getPresetDisplayName(preset) {
    const names = {
        minimal: '精简模式',
        standard: '标准模式',
        extended: '扩展模式'
    };
    return names[preset] || preset;
}

// 获取服务器节点数量
async function getNodeCount() {
    try {
        const response = await fetch('/api/servers');
        const result = await response.json();
        
        if (result.success && result.data) {
            // 计算活跃的服务器数量（未禁用的）
            return result.data.filter(server => !server.disabled).length;
        }
        return 0;
    } catch (error) {
        console.error('[数据保留] 获取节点数量失败:', error);
        return 0;
    }
}

// 加载当前数据保留配置
async function loadRetentionConfig() {
    try {
        // 并行获取配置和节点数量
        const [configResponse, nodeCount] = await Promise.all([
            fetch('/api/settings/data-retention'),
            getNodeCount()
        ]);
        
        const result = await configResponse.json();
        
        if (result.code === 1 || result.status === 1) {
            const currentPreset = result.data?.current_preset || 'standard';
            const config = result.data?.config || { archive_hours: 3, minute_days: 14, hour_days: 90 };
            
            currentRetentionPreset = currentPreset;
            updateRetentionUI(currentPreset, config);
            
            // 使用从/api/servers获取的节点数量计算存储空间
            const singleNodeStorage = {
                minimal: { total_mb: 0.9 },
                standard: { total_mb: 1.9 },
                extended: { total_mb: 4.1 }
            };
            
            const actualStorage = {
                total_mb: Math.round(singleNodeStorage[currentPreset].total_mb * nodeCount),
                details: `${nodeCount}个节点`
            };
            
            updateCurrentConfigDisplay(currentPreset, config, actualStorage, nodeCount);
            updateStorageDisplayInCards(singleNodeStorage, nodeCount);
            
            console.log('[数据保留] 配置加载成功，节点数:', nodeCount);
        } else {
            throw new Error('获取配置失败');
        }
    } catch (error) {
        console.error('[数据保留] 加载配置失败:', error);
        // 使用默认配置
        updateCurrentConfigDisplay('standard', { archive_hours: 3, minute_days: 14, hour_days: 90 });
    }
}


// 用于生成嵌套对象的辅助函数
function gen(x, keys, val) {
    if (keys.length == 0) return;
    var key = keys[0];
    keys.shift();
    if (keys.length == 0) x[key] = val;
    else {
        if (!x[key]) x[key] = {};
        gen(x[key], keys, val);
    }
}

async function saveAdvancedSettings() {
    // 显示加载状态
    const saveButton = document.getElementById('saveSettingsButton');
    const originalContent = saveButton.innerHTML;
    saveButton.disabled = true;
    saveButton.innerHTML = '<svg class="tabler-icon animate-spin" fill="none" viewBox="0 0 24 24"><path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/><path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/></svg><span>保存中...</span>';

    try {
        var setting = {};
        
        // 收集所有带有key属性的输入
        for (var x of document.querySelectorAll("[key]")) {
            var val = x.value;
            if (x.type == "number") val = Number(x.value);
            if (x.type == "checkbox") val = x.checked;
            if (x.getAttribute("isarray")) val = val.split(",");
            gen(setting, x.getAttribute("key").split('.'), val);
        }
        
        console.log('保存高级设置:', setting);
        
        // 发送到高级设置更新端点
        const res = await fetch('/admin/advanced-settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
        }).then(r => r.json());

        if (res.status === 1) {
            showToast('高级设置保存成功', 'success');

            // 如果服务器返回需要重启的提示
            if (res.data && res.data.needRestart) {
                setTimeout(() => {
                    showToast('设置已生效，建议重启服务以获得最佳性能', 'info');
                }, 1000);
            }
        } else {
            showToast(res.data || '保存失败', 'error');
        }
    } catch (error) {
        console.error('保存设置失败:', error);
        showToast('保存设置失败', 'error');
    } finally {
        // 恢复按钮状态
        saveButton.disabled = false;
        saveButton.innerHTML = originalContent;
    }
}

// 快捷键支持 (Ctrl/Cmd + S)
document.addEventListener("keydown", (e) => {
    if ((window.navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey) && e.keyCode == 83) {
        e.preventDefault();
        saveAdvancedSettings();
    }
}, false);

// 页面加载时验证输入值
document.addEventListener('DOMContentLoaded', function() {
    // 验证轮询间隔输入
    const pollingInput = document.querySelector('[key="polling_interval_seconds"]');
    const websocketInput = document.querySelector('[key="websocket_interval_seconds"]');
    
    [pollingInput, websocketInput].forEach(input => {
        if (input) {
            input.addEventListener('input', function() {
                const value = parseFloat(this.value);
                if (value < 1.5) {
                    this.value = 1.5;
                    showToast('最小间隔为1.5秒', 'warning');
                } else if (value > 60) {
                    this.value = 60;
                    showToast('最大间隔为60秒', 'warning');
                }
            });
        }
    });
    
    // 初始化数据保留设置显示
    loadRetentionConfig();
    
    // 初始化PostgreSQL状态显示
    loadPostgresStatus();
    
    // 初始化主机地址隐私保护
    initializeHostPrivacy();
});

// PostgreSQL配置相关函数
// 切换PostgreSQL开关
document.getElementById('postgres-enabled').addEventListener('change', function() {
    const settingsDiv = document.getElementById('postgres-settings');
    const testBtn = document.getElementById('test-connection-btn');
    const migrationBtn = document.getElementById('migration-btn');
    
    if (this.checked) {
        settingsDiv.classList.remove('hidden');
        testBtn?.classList.remove('hidden');
        migrationBtn?.classList.remove('hidden');
    } else {
        settingsDiv.classList.add('hidden');
        testBtn?.classList.add('hidden');
        migrationBtn?.classList.add('hidden');
    }
    // 注意：保存按钮始终保持可见，不在此处控制
});

// 解析连接字符串并填入表单
function parseAndFillConnectionString() {
    const connectionString = document.getElementById('connection-string-input').value.trim();
    
    if (!connectionString) {
        showToast('请输入连接字符串', 'warning');
        return;
    }
    
    try {
        // 基本格式检查
        if (!connectionString.startsWith('postgresql://')) {
            throw new Error('连接字符串必须以 postgresql:// 开头');
        }
        
        const url = new URL(connectionString);
        
        if (!url.hostname) {
            throw new Error('缺少主机名');
        }
        
        // 填入表单字段
        const hostInput = document.getElementById('postgres-host');
        const portInput = document.getElementById('postgres-port');
        const databaseInput = document.getElementById('postgres-database');
        const usernameInput = document.getElementById('postgres-username');
        const passwordInput = document.getElementById('postgres-password');
        
        if (hostInput) {
            hostInput.value = url.hostname;
            hostInput.setAttribute('data-real-value', url.hostname);
        }
        if (portInput) portInput.value = url.port || 5432;
        if (databaseInput) databaseInput.value = url.pathname.slice(1) || 'postgres';
        if (usernameInput) usernameInput.value = url.username || 'postgres';
        if (passwordInput && url.password) passwordInput.value = url.password;
        
        // 清空输入框
        document.getElementById('connection-string-input').value = '';
        
        showToast('连接字符串解析成功，表单已自动填入', 'success');
        
    } catch (error) {
        showToast(`解析连接字符串失败: ${error.message}`, 'error');
    }
}

// 测试PostgreSQL连接
async function testPostgresConnection() {
    const hostInput = document.getElementById('postgres-host');
    const host = hostInput.getAttribute('data-real-value') || hostInput.value;
    const port = document.getElementById('postgres-port').value;
    const database = document.getElementById('postgres-database').value;
    const username = document.getElementById('postgres-username').value;
    const password = document.getElementById('postgres-password').value;
    
    if (!host || !port || !database || !username || !password) {
        showToast('请填写完整的连接信息', 'warning');
        return;
    }
    
    try {
        
        showToast('正在测试连接...', 'info');
        
        const response = await fetch('/admin/postgresql-config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                enabled: true,
                host: host,
                port: parseInt(port),
                database: database,
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        if (result.status === 1) {
            showToast('连接测试成功！', 'success');
        } else {
            showToast('连接测试失败: ' + (result.data?.message || result.data), 'error');
        }
    } catch (error) {
        showToast('连接测试失败: ' + error.message, 'error');
    }
}

// 保存PostgreSQL配置
async function savePostgresConfig() {
    const enabled = document.getElementById('postgres-enabled').checked;
    const hostInput = document.getElementById('postgres-host');
    const host = hostInput.getAttribute('data-real-value') || hostInput.value;
    const port = document.getElementById('postgres-port').value;
    const database = document.getElementById('postgres-database').value;
    const username = document.getElementById('postgres-username').value;
    const password = document.getElementById('postgres-password').value;
    
    try {
        const response = await fetch('/admin/postgresql-config', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                enabled: enabled,
                host: host,
                port: parseInt(port),
                database: database,
                username: username,
                password: password
            })
        });
        
        const result = await response.json();
        if (result.status === 1) {
            showToast('配置保存成功，重启后生效', 'success');
            loadPostgresStatus(); // 重新加载状态
        } else {
            showToast('保存失败: ' + result.data, 'error');
        }
    } catch (error) {
        showToast('保存失败: ' + error.message, 'error');
    }
}

// 加载PostgreSQL状态
async function loadPostgresStatus() {
    try {
        const response = await fetch('/admin/postgresql-status');
        const result = await response.json();
        
        const statusContent = document.getElementById('postgres-status-content');
        if (result.status === 1) {
            const { config, status } = result.data;
            
            let statusHtml = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h6 class="text-xs font-semibold text-slate-500 dark:text-slate-400 mb-2">配置信息</h6>
                        <div class="space-y-1">
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-500">状态:</span>
                                <span class="text-xs font-medium ${config.enabled ? 'text-green-600' : 'text-gray-500'}">
                                    ${config.enabled ? '已启用' : '已禁用'}
                                </span>
                            </div>
            `;
            
            if (config.enabled && config.host) {
                const maskedHost = maskIP(config.host);
                statusHtml += `
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-slate-500">主机:</span>
                                <div class="flex items-center gap-1">
                                    <span class="text-xs font-medium text-slate-700 dark:text-slate-300 postgres-host-display" 
                                          data-full="${config.host}:${config.port}" 
                                          data-masked="${maskedHost}:${config.port}"
                                          data-visible="false">
                                        ${maskedHost}:${config.port}
                                    </span>
                                    <button type="button" onclick="togglePostgresHost(this)" 
                                            class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                                            title="显示/隐藏完整地址">
                                        <svg class="tabler-icon" fill="none" viewBox="0 0 24 24" style="width: 0.875rem; height: 0.875rem;">
                                            <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/>
                                            <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/>
                                            <path d="M3 3l18 18"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-500">数据库:</span>
                                <span class="text-xs font-medium text-slate-700 dark:text-slate-300">${config.database}</span>
                            </div>
                `;
            }
            
            statusHtml += `
                        </div>
                    </div>
                    <div>
                        <h6 class="text-xs font-semibold text-slate-500 dark:text-slate-400 mb-2">连接状态</h6>
                        <div class="space-y-1">
            `;
            
            if (status && status.type === 'dual') {
                statusHtml += `
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-500">SQLite:</span>
                                <span class="text-xs font-medium ${status.sqlite.connected ? 'text-green-600' : 'text-red-600'}">
                                    ${status.sqlite.connected ? '已连接' : '未连接'}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-500">PostgreSQL:</span>
                                <span class="text-xs font-medium ${status.postgresql.connected ? 'text-green-600' : 'text-red-600'}">
                                    ${status.postgresql.connected ? '已连接' : '未连接'}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-500">错误计数:</span>
                                <span class="text-xs font-medium text-slate-700 dark:text-slate-300">${status.postgresql.errorCount || 0}</span>
                            </div>
                `;
            } else {
                statusHtml += `
                            <div class="text-xs text-slate-500">使用SQLite模式</div>
                `;
            }
            
            statusHtml += `
                        </div>
                    </div>
                </div>
            `;
            
            statusContent.innerHTML = statusHtml;
        } else {
            statusContent.innerHTML = '<div class="text-xs text-red-500">获取状态失败</div>';
        }
    } catch (error) {
        document.getElementById('postgres-status-content').innerHTML = '<div class="text-xs text-red-500">获取状态失败</div>';
    }
}

// 密码显示/隐藏切换功能
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('postgres-password');
    const toggleIcon = document.getElementById('password-toggle-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.innerHTML = '<path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>';
    } else {
        passwordInput.type = 'password';
        toggleIcon.innerHTML = '<path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/><path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/><path d="M3 3l18 18"/>';
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 使用notice函数如果存在
    if (window.notice && typeof window.notice === 'function') {
        window.notice(message, type);
    } else {
        // 备选方案：控制台输出和弹窗
        console.log(`[${type}] ${message}`);
        alert(message);
    }
}

// 迁移进度管理
const MigrationProgressManager = {
    progressElement: null,
    
    init() {
        this.progressElement = document.getElementById('migration-progress');
        // 不自动启动轮询，由迁移操作触发
    },
    
    setupMigrationPolling() {
        // 轮询进度日志文件方案：定期检查迁移进度
        this.pollInterval = setInterval(async () => {
            try {
                const response = await fetch('/admin/migration-progress');
                const data = await response.json();
                
                console.log('[迁移轮询] API响应:', data);
                
                if (data.status === 1) {
                    const progressData = data.data;
                    const progress = progressData.progress || 0;
                    const message = progressData.message || '';
                    const details = progressData.details || '';
                    
                    console.log(`[迁移进度] ${progress}% - ${message}${details ? ' (' + details + ')' : ''}`);
                    
                    // 更新进度条
                    MigrationProgressManager.handleProgressUpdate({
                        progress: progress,
                        message: message,
                        details: details
                    });
                    
                    // 检查是否完成（只有progress>=100或明确的完成消息才停止）
                    if (progress >= 100 || message.includes('数据迁移完成！')) {
                        MigrationProgressManager.handleMigrationCompleted({ 
                            message: message || '数据迁移完成！PostgreSQL已生效' 
                        });
                        MigrationProgressManager.stopPolling();
                    } else if (message.includes('失败')) {
                        MigrationProgressManager.handleMigrationFailed({ 
                            error: message || '迁移失败' 
                        });
                        MigrationProgressManager.stopPolling();
                    }
                } else {
                    console.error('[迁移进度] API调用失败:', data.data);
                }
            } catch (error) {
                console.error('[迁移] 进度检查失败:', error);
            }
        }, 2000); // 每2秒检查一次
    },
    
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    },
    
    showProgress() {
        if (this.progressElement) {
            this.progressElement.classList.remove('hidden');
            this.resetProgress();
        } else {
            this.progressElement = document.getElementById('migration-progress');
            if (this.progressElement) {
                this.progressElement.classList.remove('hidden');
                this.resetProgress();
            }
        }
    },
    
    hideProgress() {
        if (this.progressElement) {
            this.progressElement.classList.add('hidden');
        }
    },
    
    resetProgress() {
        document.getElementById('migration-percentage').textContent = '0%';
        document.getElementById('migration-bar').style.width = '0%';
        document.getElementById('migration-status').textContent = '准备迁移...';
        document.getElementById('migration-details').textContent = '';
        // 隐藏状态块，并移除 flex，避免与 hidden 冲突
        const completeEl = document.getElementById('migration-complete');
        const errorEl = document.getElementById('migration-error');
        completeEl.classList.add('hidden');
        errorEl.classList.add('hidden');
        completeEl.classList.remove('flex');
        errorEl.classList.remove('flex');
        
        const icon = document.getElementById('migration-icon');
        icon.innerHTML = '<path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/><path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>';
        icon.classList.add('animate-spin');
    },
    
    handleStatusUpdate(data) {
        console.log('[迁移进度] 状态更新:', data);
        
        if (data.status === 'preparing' || data.status === 'running') {
            this.showProgress();
            document.getElementById('migration-status').textContent = data.message || '正在迁移...';
        }
    },
    
    handleProgressUpdate(data) {
        console.log('[迁移进度] 进度更新:', data);
        
        if (data.progress !== undefined) {
            const percentage = Math.min(100, Math.max(0, data.progress));
            document.getElementById('migration-percentage').textContent = `${percentage}%`;
            document.getElementById('migration-bar').style.width = `${percentage}%`;
        }
        
        if (data.message) {
            document.getElementById('migration-status').textContent = data.message;
        }
        
        if (data.details) {
            document.getElementById('migration-details').textContent = data.details;
        }
    },
    
    handleMigrationCompleted(data) {
        console.log('[迁移进度] 迁移完成:', data);
        
        // 停止旋转图标
        const icon = document.getElementById('migration-icon');
        icon.classList.remove('animate-spin');
        icon.innerHTML = '<path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path d="M9 12l2 2l4 -4"/>'; icon.classList.remove('animate-spin');
        
        // 设置100%完成
        document.getElementById('migration-percentage').textContent = '100%';
        document.getElementById('migration-bar').style.width = '100%';
        
        // 显示完成状态（移除 hidden 并添加 flex 布局）
        const completeEl2 = document.getElementById('migration-complete');
        completeEl2.classList.remove('hidden');
        completeEl2.classList.add('flex');
        
        // 显示成功消息
        showToast(data.message || '数据迁移完成！PostgreSQL已生效', 'success');
        
        // 刷新PostgreSQL状态
        setTimeout(() => {
            loadPostgresStatus();
        }, 2000);
        
        // 5秒后隐藏进度条
        setTimeout(() => {
            this.hideProgress();
        }, 5000);
    },
    
    handleMigrationFailed(data) {
        console.log('[迁移进度] 迁移失败:', data);
        
        // 停止旋转图标
        const icon = document.getElementById('migration-icon');
        icon.classList.remove('animate-spin');
        icon.innerHTML = '<path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path d="M12 9v4"/><path d="M12 16h.01"/>'; icon.classList.remove('animate-spin');
        
        // 显示错误状态（移除 hidden 并添加 flex 布局）
        const errorEl2 = document.getElementById('migration-error');
        errorEl2.classList.remove('hidden');
        errorEl2.classList.add('flex');
        document.getElementById('migration-error-message').textContent = data.error || '未知错误';
        
        // 显示错误消息
        showToast(data.message || '数据迁移失败', 'error');
        
        // 10秒后隐藏进度条
        setTimeout(() => {
            this.hideProgress();
        }, 10000);
    }
};

// 在页面加载完成后初始化迁移进度管理器
document.addEventListener('DOMContentLoaded', () => {
    MigrationProgressManager.init();
});

// 修改savePostgresConfig函数，添加迁移响应处理
const originalSavePostgresConfig = window.savePostgresConfig;
window.savePostgresConfig = async function() {
    try {
        const hostInput = document.getElementById('postgres-host');
        const host = hostInput.getAttribute('data-real-value') || hostInput.value;
        
        const response = await fetch('/admin/postgresql-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                enabled: document.getElementById('postgres-enabled').checked,
                host: host,
                port: document.getElementById('postgres-port').value,
                database: document.getElementById('postgres-database').value,
                username: document.getElementById('postgres-username').value,
                password: document.getElementById('postgres-password').value,
                // 不再自动触发迁移
            })
        });
        
        const result = await response.json();
        
        if (result.status === 1) {
            showToast(result.data?.message || '配置已保存', 'success');
        } else {
            showToast(result.data?.message || result.data || '保存失败', 'error');
        }
        
        // 刷新状态
        loadPostgresStatus();
        
    } catch (error) {
        console.error('保存PostgreSQL配置失败:', error);
        showToast('保存失败: ' + error.message, 'error');
    }
};

// 手动开始数据迁移
async function startDataMigration() {
    try {
        // 确认对话框
        const confirmed = confirm('确定要开始数据迁移吗？这将把SQLite数据迁移到PostgreSQL，过程可能需要几分钟。');
        if (!confirmed) {
            return;
        }
        
        showToast('正在启动数据迁移...', 'info');
        
        const response = await fetch('/admin/migrate-to-postgresql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.status === 1) {
            // 显示迁移进度条并启动轮询
            MigrationProgressManager.showProgress();
            MigrationProgressManager.setupMigrationPolling();
            showToast(result.data || '数据迁移已启动，请查看进度条...', 'success');
        } else {
            showToast(result.data || '启动迁移失败', 'error');
        }
        
    } catch (error) {
        console.error('启动数据迁移失败:', error);
        showToast('启动迁移失败: ' + error.message, 'error');
    }
}

// IP地址隐藏辅助函数
function maskIP(ip) {
    if (!ip) return '';
    const parts = ip.split('.');
    if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.*.*`;
    }
    // 对于域名或IPv6，只显示前几个字符
    return ip.substring(0, 5) + '****';
}

// 初始化主机地址隐私保护
function initializeHostPrivacy() {
    const hostInput = document.getElementById('postgres-host');
    if (hostInput && hostInput.value) {
        const realValue = hostInput.value;
        const maskedValue = maskIP(realValue);
        
        // 保存真实值到data属性
        hostInput.setAttribute('data-real-value', realValue);
        // 显示部分隐藏的值
        hostInput.value = maskedValue;
        // 标记当前是隐藏状态
        hostInput.setAttribute('data-visible', 'false');
        
        // 获得焦点时显示真实值
        hostInput.addEventListener('focus', function() {
            if (this.getAttribute('data-visible') === 'false') {
                this.value = this.getAttribute('data-real-value');
            }
        });
        
        // 失去焦点时更新真实值但不自动隐藏（让用户控制）
        hostInput.addEventListener('blur', function() {
            // 更新真实值
            this.setAttribute('data-real-value', this.value);
        });
        
        // 输入时更新真实值
        hostInput.addEventListener('input', function() {
            this.setAttribute('data-real-value', this.value);
            this.setAttribute('data-visible', 'true');
            // 更新图标状态
            const icon = document.getElementById('host-toggle-icon');
            if (icon) {
                icon.innerHTML = '<path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>';
            }
        });
    }
}

// 切换主机输入框显示/隐藏
function toggleHostVisibility() {
    const hostInput = document.getElementById('postgres-host');
    const icon = document.getElementById('host-toggle-icon');
    const isVisible = hostInput.getAttribute('data-visible') === 'true';
    
    if (isVisible) {
        // 隐藏真实值，显示部分隐藏的值
        const realValue = hostInput.getAttribute('data-real-value') || hostInput.value;
        hostInput.setAttribute('data-real-value', realValue);
        hostInput.value = maskIP(realValue);
        hostInput.setAttribute('data-visible', 'false');
        icon.innerHTML = '<path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/><path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/><path d="M3 3l18 18"/>';
    } else {
        // 显示真实值
        hostInput.value = hostInput.getAttribute('data-real-value');
        hostInput.setAttribute('data-visible', 'true');
        icon.innerHTML = '<path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>';
    }
}

// 切换PostgreSQL主机地址显示/隐藏（状态卡片中的）
function togglePostgresHost(button) {
    const displayElement = button.parentElement.querySelector('.postgres-host-display');
    const isVisible = displayElement.getAttribute('data-visible') === 'true';
    const icon = button.querySelector('i');
    
    if (isVisible) {
        // 隐藏完整地址，显示部分地址
        displayElement.textContent = displayElement.getAttribute('data-masked');
        displayElement.setAttribute('data-visible', 'false');
        icon.innerHTML = '<path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"/><path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"/><path d="M3 3l18 18"/>';
        button.title = '显示完整地址';
    } else {
        // 显示完整地址
        displayElement.textContent = displayElement.getAttribute('data-full');
        displayElement.setAttribute('data-visible', 'true');
        icon.innerHTML = '<path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>';
        button.title = '隐藏部分地址';
    }
}
</script>

<!-- 性能优化与数据保留设置脚本 -->
<script src="/js/performance-retention.js"></script>
{%endblock%}
