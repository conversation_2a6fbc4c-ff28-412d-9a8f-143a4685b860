# Design Document

## Overview

This design document outlines the optimization of the admin notification settings page by implementing a modular, maintainable architecture. The current system has a monolithic structure with a single large route file (300+ lines), a massive HTML template (1248 lines), and mixed concerns throughout. The optimization will create a clean separation of concerns while preserving all existing functionality and UI appearance.

## Architecture

### Current Architecture Issues
- Single monolithic route file handling all notification types
- Large HTML template with mixed JavaScript and HTML
- Business logic embedded in route handlers
- No clear separation between API and page rendering
- Inconsistent error handling and validation
- Mixed concerns in the NotificationManager class

### Proposed Architecture

```
modules/notification/
├── controllers/
│   ├── page-controller.js          # Page rendering logic
│   ├── telegram-api-controller.js  # Telegram API endpoints
│   ├── email-api-controller.js     # Email API endpoints
│   └── tasks-api-controller.js     # Custom tasks API endpoints
├── services/
│   ├── telegram-service.js         # Telegram business logic
│   ├── email-service.js            # Email business logic
│   ├── tasks-service.js            # Custom tasks business logic
│   └── config-service.js           # Configuration management
├── validators/
│   ├── telegram-validator.js       # Telegram input validation
│   ├── email-validator.js          # Email input validation
│   └── tasks-validator.js          # Tasks input validation
├── middleware/
│   ├── auth-middleware.js          # Admin authentication
│   ├── validation-middleware.js    # Request validation
│   └── error-middleware.js         # Error handling
├── routes/
│   ├── index.js                    # Main router setup
│   ├── page-routes.js              # Page rendering routes
│   └── api-routes.js               # API endpoint routes
└── templates/
    ├── components/
    │   ├── telegram-section.html   # Telegram configuration UI
    │   ├── email-section.html      # Email configuration UI
    │   └── tasks-section.html      # Custom tasks UI
    └── scripts/
        ├── telegram-client.js      # Telegram frontend logic
        ├── email-client.js         # Email frontend logic
        └── tasks-client.js         # Tasks frontend logic
```

## Components and Interfaces

### 1. Controllers Layer

#### PageController
```javascript
class PageController {
    constructor(configService) {
        this.configService = configService;
    }
    
    async renderNotificationPage(req, res) {
        // Aggregate all configuration data
        // Render main template with components
    }
}
```

#### TelegramApiController
```javascript
class TelegramApiController {
    constructor(telegramService, validator) {
        this.telegramService = telegramService;
        this.validator = validator;
    }
    
    async getConfig(req, res) { /* ... */ }
    async saveConfig(req, res) { /* ... */ }
    async testNotification(req, res) { /* ... */ }
}
```

#### EmailApiController
```javascript
class EmailApiController {
    constructor(emailService, validator) {
        this.emailService = emailService;
        this.validator = validator;
    }
    
    async getConfig(req, res) { /* ... */ }
    async saveConfig(req, res) { /* ... */ }
    async testEmail(req, res) { /* ... */ }
    async getLogs(req, res) { /* ... */ }
    async cleanupLogs(req, res) { /* ... */ }
}
```

#### TasksApiController
```javascript
class TasksApiController {
    constructor(tasksService, validator) {
        this.tasksService = tasksService;
        this.validator = validator;
    }
    
    async listTasks(req, res) { /* ... */ }
    async createTask(req, res) { /* ... */ }
    async deleteTask(req, res) { /* ... */ }
    async toggleTask(req, res) { /* ... */ }
}
```

### 2. Services Layer

#### TelegramService
```javascript
class TelegramService {
    constructor(db, notificationManager) {
        this.db = db;
        this.notificationManager = notificationManager;
    }
    
    async getConfig() { /* ... */ }
    async saveConfig(config) { /* ... */ }
    async testNotification(chatIds) { /* ... */ }
    async reinitializeBot(config) { /* ... */ }
}
```

#### EmailService
```javascript
class EmailService {
    constructor(db, notificationManager) {
        this.db = db;
        this.notificationManager = notificationManager;
    }
    
    async getConfig() { /* ... */ }
    async saveConfig(config) { /* ... */ }
    async testEmail() { /* ... */ }
    async getLogs(page, pageSize) { /* ... */ }
    async cleanupLogs(days) { /* ... */ }
}
```

#### TasksService
```javascript
class TasksService {
    constructor(db) {
        this.db = db;
    }
    
    async listAll() { /* ... */ }
    async create(taskData) { /* ... */ }
    async update(id, data) { /* ... */ }
    async delete(id) { /* ... */ }
    async validateServer(sid) { /* ... */ }
}
```

#### ConfigService
```javascript
class ConfigService {
    constructor(telegramService, emailService, tasksService, serversDb) {
        this.telegramService = telegramService;
        this.emailService = emailService;
        this.tasksService = tasksService;
        this.serversDb = serversDb;
    }
    
    async getAllConfigs() {
        // Aggregate all configuration data for page rendering
    }
}
```

### 3. Validators Layer

#### TelegramValidator
```javascript
class TelegramValidator {
    static validateConfig(config) {
        // Validate telegram configuration
        // Return { isValid: boolean, errors: string[] }
    }
    
    static validateChatIds(chatIds) {
        // Validate chat IDs format
    }
}
```

#### EmailValidator
```javascript
class EmailValidator {
    static validateConfig(config) {
        // Validate email configuration
        // Check required fields, email formats, etc.
    }
    
    static validateEmailList(emails) {
        // Validate email address lists
    }
}
```

#### TasksValidator
```javascript
class TasksValidator {
    static validateTask(taskData) {
        // Validate task creation/update data
        // Check thresholds, periods, directions, etc.
    }
    
    static normalizeThresholds(thresholds) {
        // Normalize and validate threshold arrays
    }
}
```

### 4. Middleware Layer

#### AuthMiddleware
```javascript
function requireAdmin(req, res, next) {
    if (!req.admin) {
        return res.status(401).json({ code: 0, msg: '需要管理员权限' });
    }
    next();
}
```

#### ValidationMiddleware
```javascript
function validateRequest(validator) {
    return (req, res, next) => {
        const result = validator(req.body);
        if (!result.isValid) {
            return res.status(400).json({ 
                code: 0, 
                msg: '输入验证失败', 
                errors: result.errors 
            });
        }
        next();
    };
}
```

#### ErrorMiddleware
```javascript
function handleApiError(error, req, res, next) {
    console.error('[通知系统] API错误:', error);
    
    if (error.name === 'ValidationError') {
        return res.status(400).json({ code: 0, msg: error.message });
    }
    
    res.status(500).json({ code: 0, msg: '服务器内部错误' });
}
```

## Data Models

### Configuration Models

#### TelegramConfig
```javascript
{
    enabled: boolean,
    token: string,
    chatIds: string[],
    webhook: boolean,
    webhookPort: number,
    baseApiUrl: string,
    notificationTypes: {
        serverOnline: boolean,
        serverOffline: boolean,
        trafficLimit: boolean,
        testNotification: boolean,
        statusSummary: boolean,
        newServerDiscovered: boolean,
        serverApproved: boolean
    },
    offlineNotificationDelay: number,
    trafficThresholds: number[]
}
```

#### EmailConfig
```javascript
{
    enabled: boolean,
    host: string,
    port: number,
    secure: boolean,
    auth_user: string,
    auth_pass: string,
    from_address: string,
    from_name: string,
    to_addresses: string[],
    cc_addresses: string[],
    bcc_addresses: string[],
    notification_types: {
        serverOnline: boolean,
        serverOffline: boolean,
        trafficLimit: boolean,
        testNotification: boolean,
        statusSummary: boolean
    }
}
```

#### NotificationTask
```javascript
{
    id: number,
    sid: string,
    period: 'daily' | 'weekly' | 'monthly',
    traffic_limit: number | null,
    thresholds: number[],
    direction: 'both' | 'in' | 'out' | 'max',
    reset_day: number | null,
    enabled: boolean,
    created_at: string,
    updated_at: string
}
```

## Error Handling

### Standardized Error Response Format
```javascript
{
    code: 0 | 1,  // 0 = error, 1 = success
    msg: string,
    data?: any,
    errors?: string[]  // For validation errors
}
```

### Error Categories
1. **Validation Errors**: Input validation failures
2. **Authentication Errors**: Admin permission required
3. **Business Logic Errors**: Configuration conflicts, invalid states
4. **System Errors**: Database failures, external service errors
5. **Network Errors**: Bot communication, email sending failures

### Error Logging Strategy
- Use structured logging with consistent format
- Include request context (user, endpoint, timestamp)
- Separate error levels: ERROR, WARN, INFO, DEBUG
- Implement error aggregation to prevent spam

## Testing Strategy

### Unit Tests
- **Services Layer**: Test business logic in isolation
- **Validators**: Test all validation rules and edge cases
- **Controllers**: Test request/response handling
- **Middleware**: Test authentication and validation logic

### Integration Tests
- **API Endpoints**: Test complete request/response cycles
- **Database Operations**: Test data persistence and retrieval
- **External Services**: Test Telegram bot and email sending (with mocks)

### Component Tests
- **Template Components**: Test individual UI components
- **Client-side Scripts**: Test JavaScript functionality
- **Form Validation**: Test frontend validation logic

### Test Structure
```
tests/
├── unit/
│   ├── services/
│   ├── validators/
│   ├── controllers/
│   └── middleware/
├── integration/
│   ├── api/
│   ├── database/
│   └── external/
└── components/
    ├── templates/
    └── scripts/
```

### Testing Tools
- **Mocha/Jest**: Test framework
- **Supertest**: HTTP endpoint testing
- **Sinon**: Mocking and stubbing
- **Chai**: Assertion library
- **Puppeteer**: UI component testing

## Security Considerations

### Input Validation
- Sanitize all user inputs before processing
- Use parameterized queries for database operations
- Validate email addresses and chat IDs format
- Implement rate limiting for API endpoints

### Authentication & Authorization
- Verify admin permissions for all endpoints
- Use secure session management
- Implement CSRF protection for state-changing operations

### Data Protection
- Encrypt sensitive configuration data (passwords, tokens)
- Implement secure password storage
- Use HTTPS for all communications
- Sanitize error messages to prevent information disclosure

### Configuration Security
- Validate configuration changes before applying
- Implement configuration backup and rollback
- Log all configuration changes with user attribution
- Prevent injection attacks through configuration fields

## Migration Strategy

### Phase 1: Infrastructure Setup
1. Create new directory structure
2. Implement base classes and interfaces
3. Set up testing framework
4. Create middleware components

### Phase 2: Services Migration
1. Extract business logic to service classes
2. Implement validators
3. Create unit tests for services
4. Migrate one service at a time (Telegram → Email → Tasks)

### Phase 3: Controllers Migration
1. Create controller classes
2. Migrate route handlers to controllers
3. Implement error handling
4. Add integration tests

### Phase 4: Template Optimization
1. Break down large template into components
2. Extract JavaScript to separate modules
3. Implement client-side validation
4. Add component tests

### Phase 5: Final Integration
1. Update main routes file to use new architecture
2. Run comprehensive testing
3. Performance optimization
4. Documentation updates

## Performance Considerations

### Caching Strategy
- Cache configuration data to reduce database queries
- Implement in-memory caching for frequently accessed data
- Use appropriate cache invalidation strategies

### Database Optimization
- Use connection pooling for database operations
- Implement query optimization
- Add appropriate database indexes
- Monitor query performance

### Frontend Optimization
- Minimize JavaScript bundle size
- Implement lazy loading for components
- Use efficient DOM manipulation
- Optimize API request patterns

### Monitoring
- Add performance metrics collection
- Implement health checks for all services
- Monitor error rates and response times
- Set up alerting for critical failures