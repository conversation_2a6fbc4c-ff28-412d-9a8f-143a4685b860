# 管理员安全增强功能设计文档

## 概述

本设计文档详细描述了管理员安全增强功能的技术架构和实现方案。该功能将完全替代现有的UUID token机制，引入JWT会话管理、强制2FA、自定义登录入口和完善的通知系统。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[客户端] --> B[登录入口路由]
    B --> C[认证中间件]
    C --> D[JWT管理器]
    C --> E[2FA验证器]
    D --> F[会话存储]
    E --> G[TOTP管理器]
    C --> H[审计日志]
    C --> I[通知系统]
    
    J[WebSocket] --> K[JWT验证中间件]
    K --> L[权限检查]
    
    M[配置管理] --> N[统一配置系统]
    N --> O[数据库]
```

### 核心组件

1. **JWT管理器** - 负责JWT令牌的签发、验证和刷新
2. **2FA验证器** - 处理TOTP验证和GitHub OAuth信任
3. **登录入口管理器** - 管理动态登录路径和随机化
4. **审计日志系统** - 记录所有安全相关事件
5. **通知管理器** - 复用现有NotificationManager发送安全通知
6. **配置管理器** - 通过统一配置系统管理安全策略

## 组件和接口

### 1. JWT管理器 (JWTManager)

```javascript
class JWTManager {
    constructor(secretKey, options = {}) {
        this.secretKey = secretKey;
        this.options = {
            expiresIn: options.expiresIn || '24h',
            issuer: options.issuer || 'dstatus',
            algorithm: 'HS256'
        };
    }

    // 签发JWT令牌
    sign(payload) {
        const jwtPayload = {
            ...payload,
            iat: Math.floor(Date.now() / 1000),
            iss: this.options.issuer
        };
        return jwt.sign(jwtPayload, this.secretKey, {
            expiresIn: this.options.expiresIn,
            algorithm: this.options.algorithm
        });
    }

    // 验证JWT令牌
    verify(token) {
        try {
            return {
                valid: true,
                payload: jwt.verify(token, this.secretKey, {
                    algorithms: [this.options.algorithm]
                })
            };
        } catch (error) {
            return {
                valid: false,
                error: error.name,
                message: error.message
            };
        }
    }

    // 从请求中提取JWT
    extractFromRequest(req) {
        // 仅从Authorization header获取
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return null;
    }

    // 签发Access Token和Refresh Token
    signTokenPair(payload) {
        const accessToken = this.sign({
            ...payload,
            type: 'access'
        }, '15m'); // 短期Access Token
        
        const refreshTokenId = crypto.randomUUID();
        const refreshToken = this.sign({
            ...payload,
            type: 'refresh',
            jti: refreshTokenId
        }, '7d'); // 长期Refresh Token
        
        return {
            accessToken,
            refreshToken,
            refreshTokenId
        };
    }

    // 刷新令牌（需要独立的Refresh Token）
    async refresh(refreshToken, refreshTokenStore) {
        const verification = this.verify(refreshToken);
        if (!verification.valid || verification.payload.type !== 'refresh') {
            throw new Error('Invalid refresh token');
        }
        
        const { payload } = verification;
        
        // 检查Refresh Token是否在有效存储中
        const isValidRefreshToken = await refreshTokenStore.isValid(payload.jti);
        if (!isValidRefreshToken) {
            throw new Error('Refresh token revoked');
        }
        
        // 撤销旧的Refresh Token
        await refreshTokenStore.revoke(payload.jti);
        
        // 签发新的Token对
        delete payload.iat;
        delete payload.exp;
        delete payload.jti;
        delete payload.type;
        
        return this.signTokenPair(payload);
    }
}
```

### 2. 2FA验证器 (TwoFactorAuth)

```javascript
class TwoFactorAuth {
    constructor(db, config) {
        this.db = db;
        this.config = config;
    }

    // 生成TOTP密钥
    async generateSecret(userId) {
        const secret = speakeasy.generateSecret({
            name: `DStatus (${userId})`,
            issuer: 'DStatus',
            length: 32
        });
        
        // 加密存储密钥
        const encryptedSecret = this.encryptSecret(secret.base32);
        await this.db.adminUsers.updateTOTPSecret(userId, encryptedSecret);
        
        return {
            secret: secret.base32,
            qrCode: secret.otpauth_url
        };
    }

    // 验证TOTP码
    async verifyTOTP(userId, token) {
        const user = await this.db.adminUsers.findById(userId);
        if (!user || !user.totp_secret) {
            return false;
        }
        
        const decryptedSecret = this.decryptSecret(user.totp_secret);
        return speakeasy.totp.verify({
            secret: decryptedSecret,
            encoding: 'base32',
            token: token,
            window: 2 // 允许前后2个时间窗口
        });
    }

    // 检查GitHub账号白名单
    async isGitHubAccountAllowed(githubUser) {
        const allowedAccounts = await this.config.get('auth.github.allowedAccounts');
        const allowedOrgs = await this.config.get('auth.github.allowedOrganizations');
        
        // 检查账号白名单
        if (allowedAccounts.length > 0 && !allowedAccounts.includes(githubUser.login)) {
            return false;
        }
        
        // 检查组织白名单
        if (allowedOrgs.length > 0) {
            // 需要调用GitHub API获取用户组织信息
            const userOrgs = await this.fetchGitHubUserOrganizations(githubUser.access_token);
            const hasAllowedOrg = userOrgs.some(org => allowedOrgs.includes(org.login));
            if (!hasAllowedOrg) {
                return false;
            }
        }
        
        return true;
    }

    // 生成备份码
    async generateBackupCodes(userId) {
        const codes = [];
        for (let i = 0; i < 10; i++) {
            codes.push(this.generateRandomCode(8));
        }
        
        const encryptedCodes = codes.map(code => this.encryptSecret(code));
        await this.db.adminUsers.updateBackupCodes(userId, JSON.stringify(encryptedCodes));
        
        return codes;
    }

    // 验证备份码
    async verifyBackupCode(userId, code) {
        const user = await this.db.adminUsers.findById(userId);
        if (!user || !user.backup_codes) {
            return false;
        }
        
        const encryptedCodes = JSON.parse(user.backup_codes);
        const codeIndex = encryptedCodes.findIndex(encryptedCode => {
            const decryptedCode = this.decryptSecret(encryptedCode);
            return decryptedCode === code;
        });
        
        if (codeIndex === -1) {
            return false;
        }
        
        // 移除已使用的备份码
        encryptedCodes.splice(codeIndex, 1);
        await this.db.adminUsers.updateBackupCodes(userId, JSON.stringify(encryptedCodes));
        
        return true;
    }

    // 加密密钥
    encryptSecret(secret) {
        const crypto = require('crypto');
        const algorithm = 'aes-256-gcm';
        const key = crypto.scryptSync(process.env.TOTP_ENCRYPTION_KEY || 'default-key', 'salt', 32);
        const iv = crypto.randomBytes(16);
        
        const cipher = crypto.createCipherGCM(algorithm, key, iv);
        cipher.setAAD(Buffer.from('totp-secret'));
        
        let encrypted = cipher.update(secret, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
    }

    // 解密密钥
    decryptSecret(encryptedSecret) {
        const crypto = require('crypto');
        const algorithm = 'aes-256-gcm';
        const key = crypto.scryptSync(process.env.TOTP_ENCRYPTION_KEY || 'default-key', 'salt', 32);
        
        const [ivHex, authTagHex, encrypted] = encryptedSecret.split(':');
        const iv = Buffer.from(ivHex, 'hex');
        const authTag = Buffer.from(authTagHex, 'hex');
        
        const decipher = crypto.createDecipherGCM(algorithm, key, iv);
        decipher.setAAD(Buffer.from('totp-secret'));
        decipher.setAuthTag(authTag);
        
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }

    // 生成随机码
    generateRandomCode(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
}
```

### 3. 登录入口管理器 (LoginPathManager)

```javascript
class LoginPathManager {
    constructor(config, notificationManager, rateLimiter) {
        this.config = config;
        this.notificationManager = notificationManager;
        this.rateLimiter = rateLimiter;
    }

    // 获取当前登录入口
    async getCurrentPath() {
        return await this.config.get('auth.loginPath') || '/admin-login';
    }

    // 生成随机登录入口
    generateRandomPath() {
        const crypto = require('crypto');
        const randomBytes = crypto.randomBytes(16);
        const randomString = randomBytes.toString('hex');
        return `/admin-${randomString.substring(0, 12)}`;
    }

    // 更新登录入口
    async updatePath(newPath, operator, reason = 'manual') {
        const oldPath = await this.getCurrentPath();
        
        // 验证新路径格式
        if (!this.isValidPath(newPath)) {
            throw new Error('Invalid login path format');
        }
        
        // 更新配置
        await this.config.set('auth.loginPath', newPath);
        
        // 记录审计日志
        await this.auditLogger.logConfigChange({
            type: 'login_path_change',
            operator: operator,
            oldValue: oldPath,
            newValue: newPath,
            reason: reason,
            timestamp: Date.now()
        });
        
        // 发送通知
        const notifyEnabled = await this.config.get('auth.notifyOnLoginPathChange');
        if (notifyEnabled) {
            await this.sendPathChangeNotification(oldPath, newPath, operator, reason);
        }
        
        return newPath;
    }

    // 随机化登录入口
    async randomizePath(operator) {
        const allowRandomize = await this.config.get('auth.allowLoginPathRandomize');
        if (!allowRandomize) {
            throw new Error('Login path randomization is disabled');
        }
        
        const newPath = this.generateRandomPath();
        return await this.updatePath(newPath, operator, 'randomize');
    }

    // 发送入口到通知渠道
    async sendPathToChannels(clientIP) {
        // 检查限速
        const rateLimitKey = `send_path_ip:${clientIP}`;
        const isAllowed = await this.rateLimiter.checkLimit(rateLimitKey, {
            maxAttempts: 3,
            windowMinutes: 60
        });
        
        if (!isAllowed) {
            throw new Error('Rate limit exceeded for sending login path');
        }
        
        const currentPath = await this.getCurrentPath();
        const siteUrl = await this.config.get('site.url') || 'https://your-domain.com';
        const fullUrl = `${siteUrl}${currentPath}`;
        
        const message = `管理员登录入口\n\n入口地址: ${fullUrl}\n\n请妥善保管此信息，不要在公开场所分享。`;
        
        // 通过NotificationManager获取通知设置
        const telegramSetting = await this.db.setting.get('telegram');
        const emailSetting = await this.db.setting.get('emailConfig');
        
        const results = [];
        
        // 发送到Telegram
        if (telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0) {
            try {
                const result = await this.notificationManager.sendNotification(
                    '登录入口信息',
                    message,
                    telegramSetting.chatIds,
                    { priority: 'high' }
                );
                results.push({ channel: 'telegram', success: result.success });
            } catch (error) {
                results.push({ channel: 'telegram', success: false, error: error.message });
            }
        }
        
        // 发送到邮件
        if (emailSetting?.enabled) {
            try {
                const result = await this.notificationManager.sendNotification(
                    '登录入口信息',
                    message,
                    [], // 邮件通过邮件配置发送
                    { priority: 'high', channel: 'email' }
                );
                results.push({ channel: 'email', success: result.success });
            } catch (error) {
                results.push({ channel: 'email', success: false, error: error.message });
            }
        }
        
        return results;
    }

    // 发送路径变更通知
    async sendPathChangeNotification(oldPath, newPath, operator, reason) {
        const message = `登录入口已更新\n\n旧入口: [已脱敏]\n新入口: [已脱敏]\n操作者: ${operator}\n变更原因: ${reason}\n时间: ${new Date().toLocaleString()}`;
        
        const telegramSetting = await this.db.setting.get('telegram');
        if (telegramSetting?.enabled && telegramSetting?.chatIds?.length > 0) {
            await this.notificationManager.sendNotification(
                '登录入口变更',
                message,
                telegramSetting.chatIds,
                { priority: 'high' }
            );
        }
    }

    // 验证路径格式
    isValidPath(path) {
        // 必须以/开头
        if (!path.startsWith('/')) {
            return false;
        }
        
        // 长度限制
        if (path.length < 5 || path.length > 50) {
            return false;
        }
        
        // 只允许字母、数字、连字符和下划线
        const validPattern = /^\/[a-zA-Z0-9\-_]+$/;
        return validPattern.test(path);
    }
}
```

### 4. 审计日志系统 (AuditLogger)

```javascript
class AuditLogger {
    constructor(db) {
        this.db = db;
    }

    // 记录登录事件
    async logLoginEvent(event) {
        // 记录到audit_logs表
    }

    // 记录2FA事件
    async log2FAEvent(event) {
        // 记录2FA相关操作
    }

    // 记录配置变更
    async logConfigChange(event) {
        // 记录配置修改
    }

    // 查询审计日志
    async queryLogs(filters) {
        // 支持分页和过滤
    }
}
```

## 数据模型

### 数据库表设计

#### 1. admin_users 表
```sql
-- SQLite版本
CREATE TABLE admin_users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    github_id TEXT,
    github_username TEXT,
    totp_secret TEXT,
    totp_enabled INTEGER DEFAULT 0, -- SQLite使用INTEGER存储布尔值
    backup_codes TEXT, -- JSON数组
    last_login_at INTEGER,
    last_2fa_reminder_date TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- PostgreSQL版本
CREATE TABLE admin_users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    github_id TEXT,
    github_username TEXT,
    totp_secret TEXT,
    totp_enabled BOOLEAN DEFAULT FALSE,
    backup_codes TEXT, -- JSON数组
    last_login_at BIGINT,
    last_2fa_reminder_date TEXT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);
```

#### 2. audit_logs 表
```sql
-- SQLite版本
CREATE TABLE audit_logs (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL, -- 'login_success', 'login_failure', 'totp_enabled', etc.
    user_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    details TEXT, -- JSON格式的详细信息
    timestamp INTEGER NOT NULL
);

-- 独立创建索引（SQLite）
CREATE INDEX idx_audit_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_user_id ON audit_logs(user_id);

-- PostgreSQL版本
CREATE TABLE audit_logs (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    user_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    details TEXT,
    timestamp BIGINT NOT NULL
);

CREATE INDEX idx_audit_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_user_id ON audit_logs(user_id);
```

#### 3. login_attempts 表
```sql
-- SQLite版本
CREATE TABLE login_attempts (
    id TEXT PRIMARY KEY,
    ip_address TEXT NOT NULL,
    username TEXT,
    attempt_type TEXT NOT NULL, -- 'password', 'totp', 'backup_code'
    success INTEGER NOT NULL, -- SQLite使用INTEGER存储布尔值
    timestamp INTEGER NOT NULL
);

CREATE INDEX idx_attempts_ip_timestamp ON login_attempts(ip_address, timestamp);
CREATE INDEX idx_attempts_username_timestamp ON login_attempts(username, timestamp);

-- PostgreSQL版本
CREATE TABLE login_attempts (
    id TEXT PRIMARY KEY,
    ip_address TEXT NOT NULL,
    username TEXT,
    attempt_type TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    timestamp BIGINT NOT NULL
);

CREATE INDEX idx_attempts_ip_timestamp ON login_attempts(ip_address, timestamp);
CREATE INDEX idx_attempts_username_timestamp ON login_attempts(username, timestamp);
```

#### 4. rate_limits 表
```sql
-- SQLite版本
CREATE TABLE rate_limits (
    id TEXT PRIMARY KEY,
    key_type TEXT NOT NULL, -- 'login_ip', 'send_path_ip', etc.
    key_value TEXT NOT NULL,
    attempts INTEGER DEFAULT 0,
    locked_until INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    UNIQUE(key_type, key_value)
);

-- PostgreSQL版本
CREATE TABLE rate_limits (
    id TEXT PRIMARY KEY,
    key_type TEXT NOT NULL,
    key_value TEXT NOT NULL,
    attempts INTEGER DEFAULT 0,
    locked_until BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    UNIQUE(key_type, key_value)
);
```

#### 5. refresh_tokens 表
```sql
-- SQLite版本
CREATE TABLE refresh_tokens (
    id TEXT PRIMARY KEY, -- jti from JWT
    user_id TEXT NOT NULL,
    token_hash TEXT NOT NULL, -- SHA256哈希存储
    expires_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    revoked INTEGER DEFAULT 0 -- SQLite使用INTEGER存储布尔值
);

CREATE INDEX idx_refresh_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_expires_at ON refresh_tokens(expires_at);

-- PostgreSQL版本
CREATE TABLE refresh_tokens (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    token_hash TEXT NOT NULL,
    expires_at BIGINT NOT NULL,
    created_at BIGINT NOT NULL,
    revoked BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_refresh_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_expires_at ON refresh_tokens(expires_at);
```

### 配置数据模型

将安全配置集成到现有的`config/global-config.js`中：

```javascript
// 在config/global-config.js的CONFIG_SCHEMA中添加
const CONFIG_SCHEMA = {
    // ... 现有配置 ...
    
    auth: {
        loginPath: {
            type: 'string',
            default: '/admin-login',
            description: '管理员登录入口路径',
            validation: (value) => /^\/[a-zA-Z0-9\-_]{4,49}$/.test(value)
        },
        allowLoginPathRandomize: {
            type: 'boolean',
            default: true,
            description: '是否允许随机化登录入口'
        },
        notifyOnLoginPathChange: {
            type: 'boolean',
            default: true,
            description: '登录入口变更时是否发送通知'
        },
        github: {
            trust2fa: {
                type: 'boolean',
                default: true,
                description: 'GitHub登录是否视为满足2FA'
            },
            allowedAccounts: {
                type: 'array',
                default: [],
                description: 'GitHub账号白名单（必填，降低误授权风险）',
                validation: (value) => Array.isArray(value) && value.length > 0
            },
            allowedOrganizations: {
                type: 'array',
                default: [],
                description: 'GitHub组织白名单'
            }
        },
        reminderDaily: {
            type: 'boolean',
            default: true,
            description: '是否开启每日首次登录2FA提醒'
        }
    },
    
    security: {
        jwt: {
            secret: {
                type: 'string',
                default: null,
                description: 'JWT签名密钥（自动生成）',
                sensitive: true
            },
            accessTokenExpiry: {
                type: 'string',
                default: '15m',
                description: 'Access Token过期时间'
            },
            refreshTokenExpiry: {
                type: 'string',
                default: '7d',
                description: 'Refresh Token过期时间'
            }
        },
        login: {
            rateLimit: {
                maxAttempts: {
                    type: 'number',
                    default: 5,
                    description: '最大登录尝试次数'
                },
                windowMinutes: {
                    type: 'number',
                    default: 15,
                    description: '限速时间窗口（分钟）'
                },
                lockoutMinutes: {
                    type: 'number',
                    default: 30,
                    description: '锁定时长（分钟）'
                }
            },
            sendPath: {
                maxAttempts: {
                    type: 'number',
                    default: 3,
                    description: '发送登录入口最大次数'
                },
                windowMinutes: {
                    type: 'number',
                    default: 60,
                    description: '发送入口限速窗口（分钟）'
                }
            }
        }
    },
    
    notifications: {
        loginEvents: {
            success: {
                type: 'boolean',
                default: false,
                description: '登录成功通知（采样）'
            },
            failure: {
                type: 'boolean',
                default: true,
                description: '登录失败通知（去抖）'
            },
            lockout: {
                type: 'boolean',
                default: true,
                description: '账户锁定通知（即时）'
            },
            pathChange: {
                type: 'boolean',
                default: true,
                description: '登录入口变更通知（即时）'
            },
            twoFactorChange: {
                type: 'boolean',
                default: true,
                description: '2FA状态变更通知'
            }
        }
    }
};
```

## 错误处理

### 错误类型定义

```javascript
class AuthError extends Error {
    constructor(message, code, statusCode = 401) {
        super(message);
        this.name = 'AuthError';
        this.code = code;
        this.statusCode = statusCode;
    }
}

const AUTH_ERROR_CODES = {
    INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
    TOTP_REQUIRED: 'TOTP_REQUIRED',
    INVALID_TOTP: 'INVALID_TOTP',
    ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
    RATE_LIMITED: 'RATE_LIMITED',
    JWT_EXPIRED: 'JWT_EXPIRED',
    JWT_INVALID: 'JWT_INVALID',
    GITHUB_NOT_ALLOWED: 'GITHUB_NOT_ALLOWED',
    PATH_SEND_RATE_LIMITED: 'PATH_SEND_RATE_LIMITED'
};
```

### 错误处理策略

1. **认证错误** - 返回统一的错误响应，避免信息泄露
2. **限速错误** - 返回剩余时间信息
3. **JWT错误** - 清除客户端令牌并重定向到登录页
4. **系统错误** - 记录详细日志并发送管理员通知

## 测试策略

### 单元测试

1. **JWT管理器测试**
   - 令牌签发和验证
   - 过期处理
   - 格式验证

2. **2FA验证器测试**
   - TOTP生成和验证
   - 备份码管理
   - GitHub白名单检查

3. **限速器测试**
   - 限速逻辑
   - 锁定和解锁
   - 清理过期记录

### 集成测试

1. **登录流程测试**
   - 完整的登录流程
   - 2FA验证流程
   - 错误处理流程

2. **WebSocket认证测试**
   - JWT验证
   - 权限检查
   - 连接管理

3. **通知系统测试**
   - 登录事件通知
   - 限频机制
   - 多渠道发送

### 安全测试

1. **暴力破解测试**
   - 登录限速
   - 账户锁定
   - IP封禁

2. **JWT安全测试**
   - 令牌伪造
   - 重放攻击
   - 过期处理

3. **2FA绕过测试**
   - TOTP验证
   - 备份码验证
   - GitHub信任机制

## 性能考虑

### 缓存策略

1. **JWT验证缓存** - 缓存已验证的JWT，减少重复验证开销
2. **配置缓存** - 缓存安全配置，避免频繁数据库查询
3. **限速状态缓存** - 使用内存缓存限速状态，提高响应速度

### 数据库优化

1. **索引优化** - 为audit_logs和login_attempts表创建合适的索引
2. **数据清理** - 定期清理过期的审计日志和限速记录
3. **连接池** - 复用现有的数据库连接池

### 内存管理

1. **限速状态** - 使用LRU缓存管理限速状态
2. **会话存储** - JWT无状态设计，减少服务器内存占用
3. **定期清理** - 清理过期的缓存数据

## 安全基线

### 生产环境要求

1. **HTTPS强制** - 所有认证相关请求必须使用HTTPS
2. **Cookie安全** - 设置HttpOnly、Secure、SameSite属性
3. **密钥管理** - JWT密钥使用环境变量或安全存储
4. **日志安全** - 避免在日志中记录敏感信息

### 安全配置

1. **JWT配置**
   - 使用强随机密钥
   - 设置合理的过期时间
   - 启用算法验证

2. **2FA配置**
   - TOTP时间窗口设置
   - 备份码强度要求
   - GitHub白名单验证

3. **限速配置**
   - 合理的限速阈值
   - 渐进式锁定策略
   - IP白名单支持

## 现有系统集成

### 替换现有认证机制

#### 1. 替换admin_tokens机制

**现有代码位置**: `dstatus.js:464-491`

```javascript
// 现有代码 (需要移除)
var admin_tokens=new Set();
svr.all('*',(req,res,nxt)=>{
    if(admin_tokens.has(req.cookies.token))req.admin=true;
    nxt();
});

// 新的JWT中间件 (替换)
const jwtAuth = require('./modules/auth/jwt-middleware');
svr.use(jwtAuth.extractJWT);
svr.all('*', jwtAuth.setAdminFlag);
```

#### 2. 替换登录路由

**现有代码位置**: `dstatus.js:536-564`

```javascript
// 现有登录路由 (需要替换)
svr.get('/login',(req,res)=>{...});
svr.post('/login', async (req,res)=>{...});
svr.get('/logout',(req,res)=>{...});

// 新的动态登录路由
const authRoutes = require('./modules/auth/routes');
svr.use('/', authRoutes);
```

#### 3. 替换WebSocket认证

**现有代码位置**: `dstatus.js:1104-1125, 1217-1239`

```javascript
// 现有WebSocket认证 (需要替换)
const isAdmin = admin_tokens.has(req.cookies.token);
ws.isAdmin = isAdmin;

// 新的JWT WebSocket认证
const jwtWsAuth = require('./modules/auth/websocket-auth');
const isAdmin = await jwtWsAuth.verifyWebSocketJWT(req);
ws.isAdmin = isAdmin;
```

### 集成通知系统

**现有通知管理器**: `modules/notification/index.js`

新的安全模块将复用现有的NotificationManager：

```javascript
// 在安全模块中使用现有通知系统
const notificationManager = svr.locals.notification;
await notificationManager.sendNotification(
    '登录失败',
    message,
    chatIds,
    { priority: 'high' }
);
```

### 集成统一配置系统

**现有配置系统**: `modules/config.js`

安全配置将通过现有的统一配置系统管理：

```javascript
// 使用现有配置系统
const config = require('./modules/config');
const loginPath = await config.get('auth.loginPath');
await config.set('auth.loginPath', newPath);
```

### 数据库集成

**现有数据库系统**: `database/index.js`

新的安全表将通过现有的迁移系统创建：

```javascript
// 在database/migrations.js中添加新的迁移
{
    version: 1001,
    name: 'Add admin security tables',
    up: async (client, isPostgres) => {
        // 创建admin_users, audit_logs等表
    }
}
```

## 部署和迁移

### 数据迁移步骤

1. **执行数据库迁移** - 通过现有迁移系统创建新表
2. **初始化管理员账户** - 从现有密码配置创建admin用户
3. **生成JWT密钥** - 自动生成或从环境变量读取
4. **配置安全策略** - 应用默认安全配置
5. **移除旧代码** - 清理admin_tokens相关代码

### 迁移脚本示例

```javascript
// scripts/migrate-to-jwt-auth.js
async function migrateToJWTAuth() {
    // 1. 创建数据库表
    await runMigrations();
    
    // 2. 创建默认管理员
    const currentPassword = await db.setting.get('password');
    await createDefaultAdmin(currentPassword);
    
    // 3. 生成JWT密钥
    const jwtSecret = generateJWTSecret();
    await config.set('security.jwt.secret', jwtSecret);
    
    // 4. 设置默认登录路径
    await config.set('auth.loginPath', '/admin-login');
    
    // 5. 清理旧tokens文件
    cleanupTokensFile();
    
    console.log('Migration to JWT authentication completed');
}
```

### 配置迁移

1. **JWT密钥生成** - 使用crypto.randomBytes生成256位密钥
2. **登录入口初始化** - 设置默认路径`/admin-login`
3. **通知配置继承** - 保持现有telegram和email配置
4. **安全策略应用** - 使用推荐的默认安全配置

### Guest用户保持不变

现有的guest用户访问逻辑完全保持不变：

```javascript
// 现有的数据过滤逻辑保持不变
const data = req.admin ? adminData : normalData;
const wsData = ws.isAdmin ? adminData : normalData;
```

所有现有的API和WebSocket接口保持相同的行为：
- `req.admin` 标志继续工作
- `ws.isAdmin` 标志继续工作  
- 数据过滤逻辑不变
- 响应格式不变

### 通知类型扩展

需要在NotificationManager中添加新的通知类型映射：

```javascript
// 在modules/notification/index.js中添加
const typeMap = {
    // ... 现有类型 ...
    '登录入口信息': 'loginPathInfo',
    '登录入口变更': 'loginPathChange',
    '登录成功': 'loginSuccess',
    '登录失败': 'loginFailure',
    '账户锁定': 'accountLocked',
    '2FA状态变更': 'twoFactorChange'
};
```

### 初始化仪表盘

部署后需要提供初始化检查界面：

1. **JWT密钥生成校验** - 验证密钥强度和有效性
2. **登录入口验证** - 检查路径格式和可访问性
3. **通知通道自检** - 测试TG和邮件发送功能
4. **GitHub白名单配置** - 确保白名单已正确配置
5. **2FA功能测试** - 验证TOTP生成和验证功能

### 限频窗口配置

明确各类操作的默认限频策略：

1. **登录成功通知** - 采样率10%，避免频繁通知
2. **登录失败通知** - 按(user,ip)去抖，5分钟内相同失败只通知一次
3. **账户锁定通知** - 即时发送，无去重
4. **登录入口变更** - 即时发送，无去重
5. **发送入口按钮** - 每IP每小时最多3次

---

## 附录：技术实现细节

### A. RefreshTokenStore 契约

```javascript
interface RefreshTokenStore {
    // 保存Refresh Token（存储SHA256哈希）
    async save(tokenId, userId, tokenHash, expiresAt);
    
    // 检查Token是否有效（未撤销且未过期）
    async isValid(tokenId);
    
    // 撤销Token
    async revoke(tokenId);
    
    // 撤销用户所有Token
    async revokeAllForUser(userId);
    
    // 清理过期Token
    async cleanupExpired();
}
```

**并发要求**: 使用数据库事务确保Token检查和撤销的原子性。

### B. JWT 认证约定

- **传输方式**: 仅支持 `Authorization: Bearer <token>`
- **算法**: 当前使用 HS256，预留 kid 字段支持密钥轮换
- **密钥策略**: 启动时自动生成256位密钥，存储在配置系统中
- **过期处理**: Access Token 15分钟，Refresh Token 7天

### C. WebSocket 握手认证

- **携带方式**: 通过 `Authorization` header 或 `token` 查询参数
- **过期重连**: 客户端检测到401错误时自动使用Refresh Token重新获取Access Token并重连

### D. TOTP 加密实现要点

```javascript
// 正确的GCM加密实现
const cipher = crypto.createCipherGCM('aes-256-gcm', key, iv);
cipher.setAAD(Buffer.from('totp-secret'));
// 加密和获取authTag

const decipher = crypto.createDecipherGCM('aes-256-gcm', key, iv);
decipher.setAAD(Buffer.from('totp-secret'));
decipher.setAuthTag(authTag);
// 解密
```

### E. 通知限频参数

| 通知类型 | 限频维度 | 窗口时间 | 最大次数 | 策略 |
|---------|---------|---------|---------|------|
| 登录成功 | 全局 | 1小时 | 采样10% | 随机采样 |
| 登录失败 | (user,ip) | 5分钟 | 1次 | 去抖合并 |
| 账户锁定 | 无 | 无 | 无限制 | 即时发送 |
| 入口变更 | 无 | 无 | 无限制 | 即时发送 |
| 发送入口 | IP | 1小时 | 3次 | 严格限制 |

### F. 迁移验收标准

#### 数据库迁移验收
- [ ] 所有新表创建成功（admin_users, audit_logs, login_attempts, rate_limits, refresh_tokens）
- [ ] 索引创建成功，查询性能符合预期
- [ ] 数据类型适配正确（SQLite INTEGER vs PostgreSQL BOOLEAN）

#### 默认管理员初始化验收
- [ ] 从现有密码配置成功创建admin用户
- [ ] 密码哈希正确存储
- [ ] 用户可以正常登录并获取JWT

#### JWT系统验收
- [ ] 密钥生成强度符合要求（256位随机）
- [ ] Access Token和Refresh Token正常签发
- [ ] Token验证和刷新机制工作正常

#### 登录入口验收
- [ ] 动态路由正确挂载
- [ ] /login提示页正常显示
- [ ] 发送入口功能正常且限速生效

#### 通知通道验收
- [ ] TG通知发送成功
- [ ] 邮件通知发送成功（如已配置）
- [ ] 通知类型映射正确
- [ ] 限频机制按预期工作