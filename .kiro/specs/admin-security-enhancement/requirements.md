# 管理员安全增强功能需求文档

## 介绍

本功能旨在全面提升管理员登录安全性，包括引入标准化JWT会话管理、强制双因子认证(2FA)、可自定义的登录入口、以及完善的登录事件通知系统。该功能仅针对admin用户进行优化，guest用户保持现有行为不变。

## 现状分析

当前系统采用简单的二元权限判断（admin/非admin），存在以下安全问题：
- 前端MD5+服务端明文口令MD5比对的弱认证机制
- 会话管理使用UUID写入cookie，缺乏HttpOnly/Secure/SameSite/过期设置
- 会话持久化到tokens.json文件，存在安全风险
- 无登录尝试限制，容易遭受暴力破解
- WebSocket和API依赖cookie.token进行权限判断
- 缺乏登录事件审计和通知机制

## 技术背景

系统已具备以下能力：
- 通知模块支持TG/邮件渠道（modules/notification/index.js）
- 统一配置管理系统（modules/config.js）
- 数据库迁移系统可扩展schema
- 现有登录路由：GET/POST '/login'、'/logout'、'/admin*'拦截

## 需求

### 需求 1: JWT会话管理

**用户故事:** 作为系统管理员，我希望使用标准化的JWT会话管理，以便提高会话安全性和可控性。

#### 验收标准

1. WHEN 管理员成功登录 THEN 系统 SHALL 签发包含用户身份和权限的JWT令牌完全替代UUID token机制
2. WHEN 访问/admin/*路径 THEN 系统 SHALL 通过JWT验证设置req.admin=true完全替代admin_tokens.has()检查
3. WHEN JWT令牌过期或无效 THEN 系统 SHALL 重定向到登录页面
4. WHEN WebSocket连接建立 THEN 系统 SHALL 仅支持Authorization Bearer验证JWT完全替代cookie.token检查
5. WHEN 系统启动 THEN 系统 SHALL 移除admin_tokens Set和tokens.json文件依赖

### 需求 2: 双因子认证(2FA)策略

**用户故事:** 作为系统管理员，我希望强制启用双因子认证，以便提高账户安全性。

#### 验收标准

1. WHEN 管理员使用本地密码登录 THEN 系统 SHALL 要求完成本地TOTP验证才能登录
2. WHEN 管理员通过GitHub登录 THEN 系统 SHALL 检查GitHub账号/组织白名单后视为已满足2FA要求
3. WHEN GitHub登录的管理员每日首次登录 THEN 系统 SHALL 在UI显示TOTP启用提醒（不发邮件）
4. WHEN 管理员启用或禁用2FA THEN 系统 SHALL 记录审计日志并发送通知
5. WHEN 存储2FA相关数据 THEN 系统 SHALL 加密存储TOTP密钥到数据库

### 需求 3: 自定义登录入口管理

**用户故事:** 作为系统管理员，我希望能够自定义和随机化登录入口，以便提高系统安全性。

#### 验收标准

1. WHEN 系统启动 THEN 系统 SHALL 从配置读取auth.loginPath并动态挂载登录路由
2. WHEN 访问/login THEN 系统 SHALL 显示提示页面，不泄露真实登录入口
3. WHEN 管理员请求旋转登录入口 THEN 系统 SHALL 生成随机路径并持久化配置
4. WHEN 登录入口变更 THEN 系统 SHALL 根据配置向通知渠道发送新入口信息
5. WHEN 用户点击"发送最新入口"按钮 THEN 系统 SHALL 向已启用渠道发送当前入口（强限速）

### 需求 4: 登录事件通知系统

**用户故事:** 作为系统管理员，我希望接收登录相关事件通知，以便及时了解系统安全状况。

#### 验收标准

1. WHEN 登录成功 THEN 系统 SHALL 按配置进行采样通知
2. WHEN 登录失败 THEN 系统 SHALL 按(user, ip)维度去抖后发送通知
3. WHEN 账户被锁定 THEN 系统 SHALL 立即发送锁定通知
4. WHEN 登录入口变更 THEN 系统 SHALL 立即发送变更通知
5. WHEN 点击"发送最新入口"按钮 THEN 系统 SHALL 按IP实施强限速并仅通过通知渠道下发

### 需求 5: 登录安全防护

**用户故事:** 作为系统管理员，我希望系统具备完善的登录安全防护机制，以便防止暴力破解和恶意攻击。

#### 验收标准

1. WHEN 登录尝试超过阈值 THEN 系统 SHALL 锁定账户指定时长
2. WHEN 检测到异常登录行为 THEN 系统 SHALL 实施IP和账号维度的限速
3. WHEN 连续失败次数达到阈值 THEN 系统 SHALL 采用衰减策略增加锁定时长
4. WHEN "发送入口"按钮被频繁点击 THEN 系统 SHALL 按IP实施严格限速
5. WHEN 生产环境运行 THEN 系统 SHALL 要求HTTPS并设置安全Cookie属性

### 需求 6: 配置管理

**用户故事:** 作为系统管理员，我希望通过统一配置系统灵活控制安全功能，以便根据实际需求调整安全策略。

#### 验收标准

1. WHEN 配置auth.loginPath THEN 系统 SHALL 支持自定义登录入口路径并通过config.get()访问
2. WHEN 配置auth.github.trust2fa和auth.github.allowedAccounts THEN 系统 SHALL 控制GitHub登录的2FA信任策略和账号白名单
3. WHEN 配置auth.notifications.loginEvents THEN 系统 SHALL 支持各类登录事件的通知开关
4. WHEN 配置security.login.rateLimit和security.login.lockout THEN 系统 SHALL 支持登录限速和锁定策略配置
5. WHEN 配置变更 THEN 系统 SHALL 通过统一配置管理系统持久化到数据库并立即生效

### 需求 7: 审计和日志

**用户故事:** 作为系统管理员，我希望系统记录完整的安全审计日志，以便进行安全分析和合规检查。

#### 验收标准

1. WHEN 登录成功或失败 THEN 系统 SHALL 记录时间、用户、IP、User-Agent等信息到审计表
2. WHEN 账户锁定或解锁 THEN 系统 SHALL 记录详细的审计信息到审计表
3. WHEN 2FA启用或禁用 THEN 系统 SHALL 记录操作者和操作时间到审计表
4. WHEN 登录入口变更 THEN 系统 SHALL 记录变更原因和操作者到审计表
5. WHEN 记录敏感信息 THEN 系统 SHALL 避免在日志中明文记录登录入口或TOTP密钥

### 需求 8: 数据迁移和管理界面

**用户故事:** 作为系统管理员，我希望系统提供完整的数据迁移和管理界面，以便一步到位完成安全功能部署。

#### 验收标准

1. WHEN 系统升级 THEN 系统 SHALL 创建或扩展数据库表存储admin的2FA状态、TOTP密钥、审计日志
2. WHEN 访问安全设置页 THEN 系统 SHALL 提供启用/禁用2FA、查看备份码的管理界面
3. WHEN 访问登录入口管理页 THEN 系统 SHALL 显示当前入口、提供一键旋转、配置通知选项
4. WHEN 访问安全日志页 THEN 系统 SHALL 显示登录事件和安全审计记录
5. WHEN 访问/login THEN 系统 SHALL 显示提示页面和"发送最新入口"按钮（不泄露真实入口）

### 需求 9: Guest用户兼容性

**用户故事:** 作为匿名访客，我希望系统保持现有的访问体验，以便正常查看公开的监控信息。

#### 验收标准

1. WHEN guest用户访问公开页面 THEN 系统 SHALL 保持现有的数据过滤和显示逻辑不变
2. WHEN WebSocket连接 THEN 系统 SHALL 保持ws.isAdmin=false的现有区分机制
3. WHEN API调用 THEN 系统 SHALL 保持req.admin=false的现有数据范围过滤
4. WHEN 数据推送 THEN 系统 SHALL 保持adminData/normalData的现有区分机制
5. WHEN guest用户行为 THEN 系统 SHALL 不受admin安全功能影响