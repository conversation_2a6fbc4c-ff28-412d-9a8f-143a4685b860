# Design Document

## Overview

This design document outlines the fix for the performance and data retention settings component that currently lacks essential parameter displays while maintaining a compact design. The component exists at `/views/components/performance-retention-settings.html` and integrates with existing APIs (`/modules/api/performance-mode.js` and `/modules/api/data-retention.js`) but fails to show critical information to users.

The current implementation successfully provides a three-segment toggle interface but omits the specific parameter values that administrators need to understand the impact of their selections. This design will restore the missing information while preserving the compact, space-efficient layout.

## Architecture

### Current Architecture Analysis

**Existing Components:**
- HTML Component: `/views/components/performance-retention-settings.html`
- Performance Mode API: `/modules/api/performance-mode.js`
- Data Retention API: `/modules/api/data-retention.js`
- API Integration: Both APIs are loaded in `/modules/api/index.js`

**Current Issues:**
1. Missing parameter displays for performance modes (polling intervals, WebSocket intervals)
2. Missing data retention duration displays (1小时, 7天, 30天)
3. No JavaScript integration for dynamic parameter updates
4. No usage instructions for users
5. Potential styling conflicts from previous implementations

**Existing API Structure:**
```javascript
// Performance Modes (from performance-mode.js)
PERFORMANCE_MODES = {
    eco: {
        name: '节能模式',
        polling_interval: 5000,     // 5秒
        websocket_interval: 8000    // 8秒
    },
    balanced: {
        name: '平衡模式', 
        polling_interval: 3000,     // 3秒
        websocket_interval: 4000    // 4秒
    },
    performance: {
        name: '高性能模式',
        polling_interval: 1500,     // 1.5秒
        websocket_interval: 2000    // 2秒
    }
}

// Data Retention Presets (from data-retention.js)
PRESETS = {
    minimal: {
        name: '精简模式',
        archive_hours: 1,    // 实时数据：1小时
        minute_days: 3,      // 分钟级数据：3天
        hour_days: 14        // 小时级数据：14天
    },
    standard: {
        name: '标准模式',
        archive_hours: 1,    // 实时数据：1小时
        minute_days: 7,      // 分钟级数据：7天
        hour_days: 30        // 小时级数据：30天
    },
    extended: {
        name: '扩展模式',
        archive_hours: 2,    // 实时数据：2小时
        minute_days: 14,     // 分钟级数据：14天
        hour_days: 90        // 小时级数据：90天
    }
}
```

### Proposed Architecture

The fix will enhance the existing component without changing the API structure:

```
views/components/performance-retention-settings.html (Enhanced)
├── Compact Container Layout (Preserved)
├── Performance Mode Section
│   ├── Three-segment toggle (Preserved)
│   ├── Parameter display area (NEW)
│   └── Dynamic parameter updates (NEW)
├── Data Retention Section  
│   ├── Three-segment toggle (Preserved)
│   ├── Duration display area (NEW)
│   └── Dynamic duration updates (NEW)
├── Usage Instructions (NEW)
└── JavaScript Integration (NEW)
```

## Components and Interfaces

### 1. Enhanced HTML Structure

#### Parameter Display Areas
```html
<!-- Performance Mode Parameters -->
<div class="parameter-display">
    <div class="text-xs text-slate-600 dark:text-slate-400">
        <span class="param-label">轮询间隔:</span>
        <span class="param-value" id="polling-interval">3秒</span>
        <span class="param-separator">|</span>
        <span class="param-label">WebSocket:</span>
        <span class="param-value" id="websocket-interval">4秒</span>
    </div>
</div>

<!-- Data Retention Parameters -->
<div class="parameter-display">
    <div class="text-xs text-slate-600 dark:text-slate-400">
        <span class="param-label">实时:</span>
        <span class="param-value" id="archive-duration">1小时</span>
        <span class="param-separator">|</span>
        <span class="param-label">分钟级:</span>
        <span class="param-value" id="minute-duration">7天</span>
        <span class="param-separator">|</span>
        <span class="param-label">小时级:</span>
        <span class="param-value" id="hour-duration">30天</span>
    </div>
</div>
```

#### Usage Instructions
```html
<!-- Compact Usage Instructions -->
<div class="usage-instructions">
    <div class="text-xs text-slate-500 dark:text-slate-500 text-center">
        选择模式自动调整系统参数，重启后生效
    </div>
</div>
```

### 2. JavaScript Integration Module

#### PerformanceRetentionManager Class
```javascript
class PerformanceRetentionManager {
    constructor() {
        this.performanceModes = {};
        this.retentionPresets = {};
        this.currentPerformanceMode = 'balanced';
        this.currentRetentionMode = 'standard';
        this.init();
    }
    
    async init() {
        await this.loadConfigurations();
        this.bindEvents();
        this.updateDisplays();
    }
    
    async loadConfigurations() {
        // Load performance modes and retention presets from APIs
        const [perfResponse, retentionResponse] = await Promise.all([
            fetch('/api/settings/performance-mode'),
            fetch('/api/settings/data-retention')
        ]);
        
        const perfData = await perfResponse.json();
        const retentionData = await retentionResponse.json();
        
        this.performanceModes = perfData.data.modes;
        this.retentionPresets = retentionData.data.presets;
        this.currentPerformanceMode = perfData.data.current.mode;
        this.currentRetentionMode = retentionData.data.current.preset;
    }
    
    bindEvents() {
        // Performance mode buttons
        document.querySelectorAll('.perf-mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handlePerformanceModeChange(e.target.dataset.mode);
            });
        });
        
        // Retention mode buttons  
        document.querySelectorAll('.retention-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleRetentionModeChange(e.target.dataset.preset);
            });
        });
    }
    
    async handlePerformanceModeChange(mode) {
        try {
            const response = await fetch('/api/settings/performance-mode', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ mode })
            });
            
            if (response.ok) {
                this.currentPerformanceMode = mode;
                this.updatePerformanceDisplay();
                this.updateActiveButton('.perf-mode-btn', mode);
            }
        } catch (error) {
            console.error('Failed to update performance mode:', error);
        }
    }
    
    async handleRetentionModeChange(preset) {
        try {
            const response = await fetch('/api/settings/data-retention', {
                method: 'PUT', 
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ preset })
            });
            
            if (response.ok) {
                this.currentRetentionMode = preset;
                this.updateRetentionDisplay();
                this.updateActiveButton('.retention-btn', preset);
            }
        } catch (error) {
            console.error('Failed to update retention mode:', error);
        }
    }
    
    updatePerformanceDisplay() {
        const config = this.performanceModes[this.currentPerformanceMode];
        if (config) {
            document.getElementById('polling-interval').textContent = 
                `${config.polling_interval / 1000}秒`;
            document.getElementById('websocket-interval').textContent = 
                `${config.websocket_interval / 1000}秒`;
        }
    }
    
    updateRetentionDisplay() {
        const config = this.retentionPresets[this.currentRetentionMode];
        if (config) {
            document.getElementById('archive-duration').textContent = 
                `${config.archive_hours}小时`;
            document.getElementById('minute-duration').textContent = 
                `${config.minute_days}天`;
            document.getElementById('hour-duration').textContent = 
                `${config.hour_days}天`;
        }
    }
    
    updateActiveButton(selector, activeValue) {
        document.querySelectorAll(selector).forEach(btn => {
            btn.classList.remove('bg-white', 'dark:bg-slate-600', 'text-slate-800', 'dark:text-white', 'shadow-sm');
            if (btn.dataset.mode === activeValue || btn.dataset.preset === activeValue) {
                btn.classList.add('bg-white', 'dark:bg-slate-600', 'text-slate-800', 'dark:text-white', 'shadow-sm');
            }
        });
    }
    
    updateDisplays() {
        this.updatePerformanceDisplay();
        this.updateRetentionDisplay();
        this.updateActiveButton('.perf-mode-btn', this.currentPerformanceMode);
        this.updateActiveButton('.retention-btn', this.currentRetentionMode);
    }
}
```

### 3. Enhanced CSS Styles

#### Parameter Display Styling
```css
.parameter-display {
    margin-top: 0.5rem;
    padding: 0.25rem 0;
}

.param-label {
    font-weight: 500;
}

.param-value {
    font-weight: 600;
    color: rgb(59 130 246); /* blue-500 */
}

.dark .param-value {
    color: rgb(147 197 253); /* blue-300 */
}

.param-separator {
    margin: 0 0.5rem;
    opacity: 0.5;
}

.usage-instructions {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgb(226 232 240 / 0.6); /* slate-200/60 */
}

.dark .usage-instructions {
    border-top-color: rgb(51 65 85 / 0.6); /* slate-700/60 */
}
```

## Data Models

### Performance Mode Configuration
```javascript
{
    mode: 'balanced' | 'eco' | 'performance',
    config: {
        name: string,
        polling_interval: number,    // milliseconds
        websocket_interval: number   // milliseconds
    }
}
```

### Data Retention Configuration  
```javascript
{
    preset: 'minimal' | 'standard' | 'extended',
    config: {
        name: string,
        archive_hours: number,   // hours for real-time data
        minute_days: number,     // days for minute-level data
        hour_days: number        // days for hour-level data
    }
}
```

### Display State
```javascript
{
    performance: {
        currentMode: string,
        pollingInterval: string,    // formatted for display (e.g., "3秒")
        websocketInterval: string   // formatted for display (e.g., "4秒")
    },
    retention: {
        currentPreset: string,
        archiveDuration: string,    // formatted for display (e.g., "1小时")
        minuteDuration: string,     // formatted for display (e.g., "7天")
        hourDuration: string        // formatted for display (e.g., "30天")
    }
}
```

## Error Handling

### API Error Handling
```javascript
// Standardized error handling for API calls
async function handleApiCall(apiCall, errorMessage) {
    try {
        const response = await apiCall();
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error(errorMessage, error);
        // Show user-friendly error message
        showNotification('操作失败，请稍后重试', 'error');
        throw error;
    }
}
```

### Fallback Display Values
```javascript
// Default values when API calls fail
const FALLBACK_VALUES = {
    performance: {
        pollingInterval: '3秒',
        websocketInterval: '4秒'
    },
    retention: {
        archiveDuration: '1小时',
        minuteDuration: '7天', 
        hourDuration: '30天'
    }
};
```

## Testing Strategy

### Unit Tests
1. **PerformanceRetentionManager Class**
   - Test initialization and configuration loading
   - Test mode change handlers
   - Test display update methods
   - Test error handling scenarios

2. **Display Formatting**
   - Test parameter value formatting
   - Test duration display formatting
   - Test active button state management

### Integration Tests
1. **API Integration**
   - Test performance mode API calls
   - Test data retention API calls
   - Test error response handling
   - Test configuration synchronization

2. **UI Interaction**
   - Test button click handlers
   - Test visual state updates
   - Test parameter display updates
   - Test responsive layout behavior

### Component Tests
1. **HTML Structure**
   - Test parameter display areas render correctly
   - Test usage instructions display
   - Test responsive grid layout
   - Test accessibility attributes

2. **CSS Styling**
   - Test parameter value styling
   - Test dark mode compatibility
   - Test compact layout preservation
   - Test hover and active states

## Security Considerations

### Input Validation
- Validate mode and preset values against allowed options
- Sanitize API responses before displaying
- Implement client-side validation for user selections

### API Security
- Ensure admin authentication for settings endpoints
- Implement CSRF protection for state-changing operations
- Use secure HTTP methods (PUT for updates)

### Error Information
- Avoid exposing sensitive system information in error messages
- Log detailed errors server-side while showing generic messages to users
- Implement rate limiting for settings update endpoints

## Performance Considerations

### Efficient Updates
- Use debounced API calls to prevent rapid successive requests
- Cache configuration data to reduce API calls
- Update only changed display elements

### Minimal DOM Manipulation
- Use targeted element updates instead of full re-renders
- Implement efficient CSS class toggling
- Minimize layout thrashing with batched updates

### Loading States
- Show loading indicators during API calls
- Implement optimistic UI updates where appropriate
- Provide immediate visual feedback for user interactions

## Migration Strategy

### Phase 1: Enhanced HTML Structure
1. Add parameter display areas to existing component
2. Add usage instructions section
3. Preserve existing three-segment toggle layout
4. Test visual layout and responsiveness

### Phase 2: JavaScript Integration
1. Create PerformanceRetentionManager class
2. Implement API integration methods
3. Add event handlers for button interactions
4. Test functionality with existing APIs

### Phase 3: Display Updates
1. Implement dynamic parameter updates
2. Add active button state management
3. Test display synchronization with API data
4. Verify error handling scenarios

### Phase 4: Style Cleanup
1. Remove redundant CSS from previous implementations
2. Optimize styles for compact layout
3. Ensure dark mode compatibility
4. Test cross-browser compatibility

### Phase 5: Integration Testing
1. Test complete component functionality
2. Verify API integration works correctly
3. Test error scenarios and fallbacks
4. Validate accessibility and usability

## Accessibility Considerations

### Screen Reader Support
- Add appropriate ARIA labels for parameter displays
- Ensure button states are announced correctly
- Provide meaningful descriptions for mode changes

### Keyboard Navigation
- Ensure all interactive elements are keyboard accessible
- Implement proper focus management
- Add keyboard shortcuts where appropriate

### Visual Accessibility
- Maintain sufficient color contrast for parameter displays
- Ensure text remains readable at different zoom levels
- Support high contrast mode preferences

## Browser Compatibility

### Modern Browser Support
- ES6+ features (async/await, arrow functions, template literals)
- Fetch API for HTTP requests
- CSS Grid and Flexbox for layout
- CSS custom properties for theming

### Fallback Strategies
- Provide polyfills for older browsers if needed
- Graceful degradation for unsupported CSS features
- Alternative layouts for browsers without grid support

## Documentation Requirements

### Code Documentation
- JSDoc comments for all public methods
- Inline comments for complex logic
- README updates for component usage

### User Documentation
- Clear descriptions of each mode's impact
- Guidelines for choosing appropriate settings
- Troubleshooting guide for common issues