# Requirements Document

## Introduction

This feature aims to fix the performance and data retention settings component that was recently merged into a compact container. The current implementation has lost critical display information while attempting to create a space-efficient design. The component needs to maintain its compact three-segment toggle style while restoring all necessary parameter displays and functionality.

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want to see performance mode parameters in the compact settings container, so that I can understand the current monitoring configuration without losing essential information.

#### Acceptance Criteria

1. WHEN the performance settings are displayed THEN the system SHALL show monitoring polling interval (seconds)
2. WHEN the performance settings are displayed THEN the system SHALL show WebSocket update interval (seconds)
3. WHEN performance mode is changed THEN the system SHALL display the specific time parameters for the selected mode
4. WHEN the component loads THEN all performance parameter displays SHALL be visible in the compact layout

### Requirement 2

**User Story:** As a system administrator, I want to see data retention parameters in the compact settings container, so that I can understand how long different types of data are preserved.

#### Acceptance Criteria

1. WHEN data retention settings are displayed THEN the system SHALL show "实时数据：1小时" 
2. WHEN data retention settings are displayed THEN the system SHALL show "分钟级数据：7天"
3. WHEN data retention settings are displayed THEN the system SHALL show "小时级数据：30天"
4. WHEN retention mode is changed THEN the system SHALL display the specific duration parameters for the selected mode

### Requirement 3

**User Story:** As a system administrator, I want the settings container to maintain minimal space usage, so that the interface remains clean and efficient.

#### Acceptance Criteria

1. WHEN the settings container is rendered THEN it SHALL use a three-segment toggle switch style
2. WHEN the container is displayed THEN it SHALL occupy minimal vertical space (maximum 2-3 lines)
3. WHEN parameter information is shown THEN it SHALL be integrated efficiently within the compact layout
4. WHEN the component loads THEN the overall design SHALL maintain the space-efficient approach

### Requirement 4

**User Story:** As a system administrator, I want clear but concise usage instructions, so that I understand how to use the settings without overwhelming text.

#### Acceptance Criteria

1. WHEN usage instructions are displayed THEN they SHALL be limited to maximum one line
2. WHEN the component loads THEN instructions SHALL be clear and actionable
3. WHEN instructions are shown THEN they SHALL not provide inaccurate estimated data
4. WHEN help text is displayed THEN it SHALL focus on essential usage information only

### Requirement 5

**User Story:** As a developer, I want all existing functionality preserved, so that no features are lost during the compact design implementation.

#### Acceptance Criteria

1. WHEN settings are changed THEN all API integration SHALL continue to work normally
2. WHEN the component operates THEN bidirectional synchronization SHALL function correctly
3. WHEN users interact with toggles THEN all state changes SHALL be properly handled
4. WHEN the system loads THEN no existing functionality SHALL be removed or broken

### Requirement 6

**User Story:** As a developer, I want clean code without redundant parts, so that the implementation is maintainable and efficient.

#### Acceptance Criteria

1. WHEN the component is implemented THEN all previous styling conflicts SHALL be cleaned up
2. WHEN JavaScript code is reviewed THEN no redundant code parts SHALL remain
3. WHEN the implementation is complete THEN storage estimation code SHALL be removed (as it was inaccurate)
4. WHEN code is refactored THEN only essential functionality SHALL be retained