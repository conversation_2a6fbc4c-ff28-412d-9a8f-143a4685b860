#!/usr/bin/env node
"use strict";

/**
 * 管理员安全增强功能初始化脚本
 * 用于从现有密码配置创建默认管理员用户
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

async function initAdminSecurity() {
    try {
        console.log('[Init] 开始初始化管理员安全增强功能...');
        
        // 初始化数据库
        const dbModule = require('../database/index');
        const db = await dbModule();
        
        // 初始化配置系统
        const config = require('../modules/config');
        await config.init(db);
        
        // 1. 检查是否已经有管理员用户
        const existingAdmin = await checkExistingAdmin(db);
        if (existingAdmin) {
            console.log('[Init] 管理员用户已存在，跳过创建');
        } else {
            // 2. 从现有密码配置创建默认管理员
            await createDefaultAdmin(db);
        }
        
        // 3. 生成JWT密钥
        await generateJWTSecret(config);
        
        // 4. 初始化安全配置
        await initializeSecurityConfig(config);
        
        // 5. 设置默认登录入口
        await setDefaultLoginPath(config);
        
        console.log('[Init] 管理员安全增强功能初始化完成');
        
        // 关闭数据库连接
        if (db.DB && db.DB.close) {
            db.DB.close();
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('[Init] 初始化失败:', error);
        process.exit(1);
    }
}

async function checkExistingAdmin(db) {
    try {
        const result = await db.DB.get('SELECT COUNT(*) as count FROM admin_users');
        return result && result.count > 0;
    } catch (error) {
        // 表可能不存在，返回false
        return false;
    }
}

async function createDefaultAdmin(db) {
    try {
        console.log('[Init] 创建默认管理员用户...');
        
        // 获取现有密码配置
        const currentPassword = await db.setting.get('password');
        if (!currentPassword) {
            throw new Error('未找到现有密码配置');
        }
        
        // 生成管理员用户ID
        const adminId = uuidv4();
        
        // 对密码进行哈希（使用bcrypt风格的哈希）
        const bcrypt = require('bcrypt');
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(currentPassword, saltRounds);
        
        // 创建管理员用户记录
        const now = Math.floor(Date.now() / 1000);
        await db.DB.run(`
            INSERT INTO admin_users (
                id, username, password_hash, totp_enabled, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
            adminId,
            'admin',
            passwordHash,
            0, // SQLite使用0表示false
            now,
            now
        ]);
        
        console.log('[Init] 默认管理员用户创建成功');
        
        // 检查是否为默认密码，如果是则记录警告
        if (currentPassword === 'dstatus') {
            console.warn('[Init] 警告: 检测到默认密码，建议首次登录后立即修改');
        }
        
    } catch (error) {
        console.error('[Init] 创建默认管理员失败:', error);
        throw error;
    }
}

async function generateJWTSecret(config) {
    try {
        console.log('[Init] 生成JWT密钥...');
        
        // 检查是否已有JWT密钥
        let existingSecret;
        try {
            existingSecret = await config.get('security.jwtSecret');
        } catch (error) {
            // 配置不存在，继续生成
            existingSecret = null;
        }
        
        if (existingSecret) {
            console.log('[Init] JWT密钥已存在，跳过生成');
            return;
        }
        
        // 生成256位随机密钥
        const secret = crypto.randomBytes(32).toString('hex');
        
        // 保存到配置系统
        await config.set('security.jwtSecret', secret);
        
        console.log('[Init] JWT密钥生成并保存成功');
        
    } catch (error) {
        console.error('[Init] 生成JWT密钥失败:', error);
        throw error;
    }
}

async function initializeSecurityConfig(config) {
    try {
        console.log('[Init] 初始化安全配置...');
        
        // 生成TOTP加密密钥（如果不存在）
        let totpEncryptionKey;
        try {
            totpEncryptionKey = await config.get('security.totpEncryptionKey');
        } catch (error) {
            // 密钥不存在，生成新的
            totpEncryptionKey = crypto.randomBytes(32).toString('hex');
            await config.set('security.totpEncryptionKey', totpEncryptionKey);
            console.log('[Init] 生成TOTP加密密钥');
        }

        if (!totpEncryptionKey) {
            totpEncryptionKey = crypto.randomBytes(32).toString('hex');
            await config.set('security.totpEncryptionKey', totpEncryptionKey);
            console.log('[Init] 生成TOTP加密密钥');
        }

        // 设置默认安全配置
        const defaultConfigs = {
            'auth.allowLoginPathRandomize': true,
            'auth.notifyOnLoginPathChange': true,
            'auth.githubTrust2fa': true,
            'auth.githubAllowedAccounts': [],
            'auth.githubAllowedOrganizations': [],
            'auth.reminderDaily': true,
            'security.jwtAccessTokenExpiry': '15m',
            'security.jwtRefreshTokenExpiry': '7d',
            'security.loginRateLimitMaxAttempts': 5,
            'security.loginRateLimitWindowMinutes': 15,
            'security.loginRateLimitLockoutMinutes': 30,
            'security.loginSendPathMaxAttempts': 3,
            'security.loginSendPathWindowMinutes': 60,
            'notifications.loginEventsSuccess': false,
            'notifications.loginEventsFailure': true,
            'notifications.loginEventsLockout': true,
            'notifications.loginEventsPathChange': true,
            'notifications.loginEventsTwoFactorChange': true
        };
        
        for (const [key, value] of Object.entries(defaultConfigs)) {
            let existing;
            try {
                existing = await config.get(key);
            } catch (error) {
                // 配置不存在，设置默认值
                existing = null;
            }
            
            if (existing === undefined || existing === null) {
                await config.set(key, value);
                console.log(`[Init] 设置配置: ${key} = ${JSON.stringify(value)}`);
            }
        }
        
        console.log('[Init] 安全配置初始化完成');
        
    } catch (error) {
        console.error('[Init] 初始化安全配置失败:', error);
        throw error;
    }
}

async function setDefaultLoginPath(config) {
    try {
        console.log('[Init] 设置默认登录入口...');
        
        // 检查是否已有登录入口配置
        let existingPath;
        try {
            existingPath = await config.get('auth.loginPath');
        } catch (error) {
            // 配置不存在，继续设置
            existingPath = null;
        }
        
        if (existingPath) {
            console.log(`[Init] 登录入口已配置: ${existingPath}`);
            return;
        }
        
        // 设置默认登录入口
        const defaultPath = '/admin-login';
        await config.set('auth.loginPath', defaultPath);
        
        console.log(`[Init] 默认登录入口设置为: ${defaultPath}`);
        
    } catch (error) {
        console.error('[Init] 设置默认登录入口失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    initAdminSecurity();
}

module.exports = {
    initAdminSecurity,
    createDefaultAdmin,
    generateJWTSecret,
    initializeSecurityConfig,
    setDefaultLoginPath
};